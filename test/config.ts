import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

export const config = {
  // Test environment
  isLiveTest: process.env.TEST_ENV === 'live',

  // API Configuration
  roshiUrl: process.env.ROSHI_URL || 'https://roshi.dev.flipaclip.com',
  apiAuthToken: process.env.API_AUTH_TOKEN,
  apiQaUrl: process.env.API_QA_URL || 'https://api-qa.flipaclip.com',
  apiProdUrl: process.env.API_PROD_URL || 'https://api.flipaclip.com',
  fcSecSecret: process.env.X_FC_SEC_SECRET,

  // JWT Configuration
  jwtSecret: process.env.JWT_SECRET || 'test-jwt-secret-key-min-32-chars-long',
  jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || 'test-refresh-secret-key-min-32-chars-long',

  // Firebase Configuration
  firebaseProjectId: process.env.FIREBASE_PROJECT_ID || 'test-project-id',
  firebasePrivateKey: process.env.FIREBASE_PRIVATE_KEY || 'test-private-key',
  firebaseClientEmail: process.env.FIREBASE_CLIENT_EMAIL || '<EMAIL>',

  // Other Configuration
  slackToken: process.env.SLACK_TOKEN || 'test-slack-token',

  concurrentTestsCount: process.env.CONCURRENT_TEST_COUNT || 10,
};

// Validate required configuration
if (config.isLiveTest && !config.apiAuthToken) {
  throw new Error('API_AUTH_TOKEN is required for live tests');
}

export default config;
