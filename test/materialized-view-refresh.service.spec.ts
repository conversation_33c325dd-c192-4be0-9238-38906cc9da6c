import { Test, TestingModule } from '@nestjs/testing';
import { MaterializedViewRefreshService } from '../src/common/services/materialized-view-refresh.service';
import { PostgresUserService } from '../src/common/services/postgres-user.service';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

describe('MaterializedViewRefreshService', () => {
  let service: MaterializedViewRefreshService;
  let postgresUserService: jest.Mocked<PostgresUserService>;

  beforeEach(async () => {
    const mockPostgresUserService = {
      refreshMergedUsersMaterializedView: jest.fn(),
      query: jest.fn(),
    };
    const mockConfigService = {
      get: jest.fn(key => (key === 'cron' ? { enabled: true } : undefined)),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterializedViewRefreshService,
        {
          provide: PostgresUserService,
          useValue: mockPostgresUserService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        Logger,
      ],
    }).compile();

    service = module.get<MaterializedViewRefreshService>(MaterializedViewRefreshService);
    postgresUserService = module.get(PostgresUserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('refreshMergedUsersMaterializedView', () => {
    it('should successfully refresh the materialized view', async () => {
      postgresUserService.refreshMergedUsersMaterializedView.mockResolvedValue(undefined);

      await service.refreshMergedUsersMaterializedView();

      expect(postgresUserService.refreshMergedUsersMaterializedView).toHaveBeenCalledTimes(1);
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Database connection failed');
      postgresUserService.refreshMergedUsersMaterializedView.mockRejectedValue(error);

      // Should not throw error
      await expect(service.refreshMergedUsersMaterializedView()).resolves.toBeUndefined();
    });
  });

  describe('manualRefreshMergedUsersView', () => {
    it('should successfully refresh the materialized view', async () => {
      postgresUserService.refreshMergedUsersMaterializedView.mockResolvedValue(undefined);

      await service.manualRefreshMergedUsersView();

      expect(postgresUserService.refreshMergedUsersMaterializedView).toHaveBeenCalledTimes(1);
    });

    it('should throw error when refresh fails', async () => {
      const error = new Error('Database connection failed');
      postgresUserService.refreshMergedUsersMaterializedView.mockRejectedValue(error);

      await expect(service.manualRefreshMergedUsersView()).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('checkMaterializedViewHealth', () => {
    it('should return true when materialized view is healthy', async () => {
      postgresUserService.query.mockResolvedValue(undefined);

      const result = await service.checkMaterializedViewHealth();

      expect(result).toBe(true);
    });

    it('should return false when materialized view is unhealthy', async () => {
      const error = new Error('Materialized view not found');
      postgresUserService.query.mockRejectedValue(error);

      const result = await service.checkMaterializedViewHealth();

      expect(result).toBe(false);
    });
  });
});
