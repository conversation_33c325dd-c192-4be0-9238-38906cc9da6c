import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

import { AuthService } from '../../src/auth/auth.service';
import { User, UserType } from '../../src/users/entities/user.entity';
import { testConfig } from '../../src/config/environments/test.config';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';

describe('AuthService', () => {
  let service: AuthService;
  let jwtService: JwtService;
  let module: TestingModule;
  let errorLogger: ErrorLoggerService;
  const mockUser: User = {
    fcid: 'test-fcid',
    type: UserType.ANONYMOUS,
    identifiers: {
      idfa: ['test-idfa'],
      idfv: ['test-idfv'],
      gaid: ['test-gaid'],
      adid: ['test-adid'],
    },
    properties: {},
    device_ids: ['test-device-id'],
    mergedFcids: [],
    created_at: new Date(),
    updated_at: new Date(),
    installed_at: new Date(),
    webhooks: [],
    event_control: {},
  };

  const mockErrorLogger = {
    logError: jest.fn(),
  };

  beforeEach(async () => {
    const mockJwtService = {
      sign: jest.fn().mockImplementation((payload, options) => {
        const now = Math.floor(Date.now() / 1000);
        const exp = now + 3600; // 1 hour from now
        return 'mock-token';
      }),
      verify: jest.fn().mockReturnValue({ fcid: 'user-123', deviceId: 'device-key' }),
      decode: jest.fn().mockImplementation(() => {
        const now = Math.floor(Date.now() / 1000);
        return {
          iat: now,
          exp: now + 3600, // 1 hour from now
          fcid: 'test-fcid',
          type: 'ANONYMOUS',
        };
      }),
    };

    module = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'jwt') {
                return {
                  ...testConfig.jwt,
                  accessTokenExpiry: '1h',
                };
              }
              return testConfig[key as keyof typeof testConfig];
            }),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: mockErrorLogger,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get<JwtService>(JwtService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(jwtService).toBeDefined();
  });

  it('should generate an access token', async () => {
    const result = await service.generateAccessToken(mockUser);
    expect(result).toBe('mock-token');
    expect(jwtService.sign).toHaveBeenCalledWith(
      expect.objectContaining({
        fcid: mockUser.fcid,
        type: mockUser.type,
      }),
      expect.objectContaining({
        secret: testConfig.jwt.secret,
        expiresIn: '1h',
        noTimestamp: false,
      }),
    );
  });

  it('should throw error if JWT_SECRET is missing', async () => {
    const configService = module.get<ConfigService>(ConfigService);
    jest.spyOn(configService, 'get').mockReturnValueOnce(undefined);

    await expect(service.generateAccessToken(mockUser)).rejects.toThrow(
      'JWT_SECRET is not configured',
    );
  });

  it('should get access token expiry date', () => {
    const expiryDate = service.getAccessTokenExpiry();

    expect(expiryDate).toBeDefined();
    expect(expiryDate).toBeInstanceOf(Date);
    expect(expiryDate.getTime()).toBeGreaterThan(Date.now());
  });
});
