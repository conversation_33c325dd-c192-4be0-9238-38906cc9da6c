import { UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';

import { AuthController } from '../../src/auth/auth.controller';
import { AuthService } from '../../src/auth/auth.service';
import { JwtDto } from '../../src/auth/jwt.dto';
import { FirebaseService } from '../../src/auth/firebase.service';
import { User, UserType } from '../../src/users/entities/user.entity';
import { UsersService } from '../../src/users/users.service';
import { EventsService } from '../../src/webhooks/events.service';
import { Webhook } from '../../src/webhooks/entities/webhook.entity';
import { repositoryMockFactory } from '../utils';
import { testConfig } from '../../src/config/environments/test.config';
import { generateUUID } from '../test-utils';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PendingUserService } from '../../src/pending-user/application/services/pending-user.service';

import type { Request } from 'express';

describe('AuthController', () => {
  let authController: AuthController;
  let authService: AuthService;
  let errorLogger: ErrorLoggerService;
  const mockUser: User = {
    fcid: 'test-fcid',
    type: UserType.ANONYMOUS,
    identifiers: {
      idfa: ['test-idfa'],
      idfv: ['test-idfv'],
      gaid: ['test-gaid'],
      adid: ['test-adid'],
    },
    properties: {},
    device_ids: ['test-device-id'],
    mergedFcids: [],
    created_at: new Date(),
    updated_at: new Date(),
    installed_at: new Date(),
    webhooks: [],
    event_control: {},
  };

  const mockErrorLogger = {
    logError: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            generateAccessToken: jest.fn().mockReturnValue('mockAccessToken'),
            getAccessTokenExpiry: jest.fn().mockReturnValue(new Date(Date.now() + 15 * 60 * 1000)),
          },
        },
        {
          provide: FirebaseService,
          useValue: {
            verifyToken: jest.fn().mockResolvedValue({ uid: 'test-uid' }),
            createCustomToken: jest.fn().mockResolvedValue('mock-token'),
          },
        },
        {
          provide: UsersService,
          useValue: {
            findOrCreateUser: jest.fn().mockResolvedValue(mockUser),
            findByFcid: jest.fn().mockResolvedValue(mockUser),
            findUserByFcidCached: jest.fn().mockResolvedValue(mockUser),
            findOrCreatePendingUser: jest.fn().mockResolvedValue(mockUser),
          },
        },
        {
          provide: PendingUserService,
          useValue: {
            findOrCreateUserForJwt: jest.fn().mockResolvedValue(mockUser),
          },
        },
        EventsService,
        {
          provide: getRepositoryToken(Webhook),
          useFactory: repositoryMockFactory,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'jwt') {
                return testConfig.jwt;
              }
              return testConfig[key as keyof typeof testConfig];
            }),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: mockErrorLogger,
        },
      ],
    }).compile();

    authController = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  const mockRequest = { headers: { 'user-agent': 'Android' } } as unknown as Request;

  describe('createJwt', () => {
    const mockRequest = {
      headers: {},
      body: {},
      query: {},
      params: {},
      url: '/auth/createJwt',
      method: 'POST',
    } as any;

    it('should return access token with expiry date', async () => {
      const userData: JwtDto = { fcid: 'user-123', idfv: generateUUID() };
      const result = await authController.createJwt(
        userData,
        testConfig.api.authTokens[0],
        'Android',
        mockRequest,
      );

      expect(result).toEqual({
        accessToken: 'mockAccessToken',
        accessTokenExpiry: expect.stringContaining('T'),
      });
    });

    it('should throw UnauthorizedException with specific error code for missing API key', async () => {
      const userData: JwtDto = { fcid: 'user-123', idfv: 'idfv-123' };
      await expect(authController.createJwt(userData, '', 'Android', mockRequest)).rejects.toThrow(
        new UnauthorizedException({
          message: 'Invalid API Key',
          errorCode: 'INVALID_API_KEY',
          statusCode: 401,
        }),
      );
    });

    it('should throw UnauthorizedException with specific error code for invalid API key', async () => {
      const userData: JwtDto = { fcid: 'user-123', idfv: 'idfv-123' };
      await expect(
        authController.createJwt(userData, 'invalid-key', 'Android', mockRequest),
      ).rejects.toThrow(
        new UnauthorizedException({
          message: 'Invalid API Key',
          errorCode: 'INVALID_API_KEY',
          statusCode: 401,
        }),
      );
    });

    it('should throw UnauthorizedException with specific error code when JWT creation fails', async () => {
      const userData: JwtDto = { fcid: 'user-123', idfv: 'idfv-123' };
      jest
        .spyOn(authService, 'generateAccessToken')
        .mockRejectedValueOnce(new Error('JWT creation failed'));

      await expect(
        authController.createJwt(userData, testConfig.api.authTokens[0], 'Android', mockRequest),
      ).rejects.toThrow(
        new UnauthorizedException({
          message: 'Failed to create JWT token',
          errorCode: 'JWT_CREATION_FAILED',
          statusCode: 401,
        }),
      );
    });
  });
});
