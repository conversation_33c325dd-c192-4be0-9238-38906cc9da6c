import axios, { AxiosResponse } from 'axios';

import { config } from '../config';
import { createUser, generateAdid, generateUUID, TestSetup } from '../test-utils';

const TEST_COUNT = Number(config.concurrentTestsCount);

type User = {
  adid?: string;
  idfv?: string;
};

const generateUser = () => ({
  adid: generateAdid(),
  idfv: generateUUID(),
});

const generateConcurrentCalls = (count: number) =>
  new Array(count).fill(0).map(() => [generateUser()]);

const ANDROID_USER_AGENT = 'Android/4.2.8 (756)';
const IOS_USER_AGENT = 'iOS/4.2.15 (779)';

axios.defaults.baseURL = config.roshiUrl;
axios.defaults.headers.post['Content-Type'] = 'application/json';

type Platform = 'Android/4.2.8 (756)' | 'iOS/4.2.15 (779)';

const createJWT = async (platform: Platform, { adid, idfv }: User) => {
  try {
    const payload = platform === ANDROID_USER_AGENT ? { adid } : { idfv };
    const response = await axios.post(`${config.roshiUrl}/auth/createJwt`, payload, {
      headers: {
        'x-api-key': config.apiAuthToken,
        'Content-Type': 'application/json',
        'user-agent': platform === IOS_USER_AGENT ? 'Apple' : platform,
      },
    });
    return response;
  } catch (error) {
    return axios.isAxiosError(error)
      ? (error.response as AxiosResponse)
      : { status: 500, data: { message: error.message } };
  }
};

describe('Concurrent JWT creation', () => {
  let testSetup: TestSetup;
  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('create 2 concurrent JWT tokens', () => {
    it.each(generateConcurrentCalls(TEST_COUNT))('Android', async payload => {
      const [callA, callB] = await Promise.all([
        createJWT(ANDROID_USER_AGENT, payload),
        createJWT(ANDROID_USER_AGENT, payload),
      ]);
      // Always call createUser to be deleted on cleanup later
      if (callA.status === 201 && callB.status === 201) {
        expect(callA.data.data.accessToken).toBeDefined();
        expect(callB.data.data.accessToken).toBeDefined();
        await createUser({ identifiers: { adid: [payload.adid] }, installed_at: new Date() });
      } else if (callA.status === 201) {
        expect(callA.data.data.accessToken).toBeDefined();
        expect(callB.status).toBe(500);
        await createUser({ identifiers: { adid: [payload.adid] }, installed_at: new Date() });
      } else {
        expect(callB.data.data.accessToken).toBeDefined();
        expect(callA.status).toBe(500);
        await createUser({ identifiers: { adid: [payload.adid] }, installed_at: new Date() });
      }
    });

    it.each(generateConcurrentCalls(TEST_COUNT))('iOS', async payload => {
      const [callA, callB] = await Promise.all([
        createJWT(IOS_USER_AGENT, payload),
        createJWT(IOS_USER_AGENT, payload),
      ]);
      if (callA.status === 201) {
        expect(callA.data.data.accessToken).toBeDefined();
        expect(callB.status).toBe(500);
        // Create user to delete it later
        await createUser({ identifiers: { idfv: [payload.idfv] }, installed_at: new Date() });
      }
      if (callB.status === 201) {
        expect(callB.data.data.accessToken).toBeDefined();
        expect(callA.status).toBe(500);
        // Create user to delete it later
        await createUser({ identifiers: { idfv: [payload.idfv] }, installed_at: new Date() });
      }
    });
  });

  describe('JWT creation concurrency handling', () => {
    it('should handle multiple concurrent JWT requests with same device ID using race condition lock', async () => {
      const payload = generateUser();

      // Make 5 concurrent JWT requests with identical payload
      const concurrentRequests = Array(5)
        .fill(null)
        .map(() => createJWT(ANDROID_USER_AGENT, payload));

      const results = await Promise.all(concurrentRequests);

      // Count successful responses
      const successfulResults = results.filter(result => result.status === 201);
      const failedResults = results.filter(result => result.status === 500);

      // Should have at least one success and at most 5 (actual behavior may vary)
      expect(successfulResults.length).toBeGreaterThan(0);
      expect(successfulResults.length).toBeLessThanOrEqual(5);

      // If multiple requests succeed, they should return the same token (deduplication)
      if (successfulResults.length > 1) {
        const firstToken = successfulResults[0].data.data.accessToken;
        successfulResults.forEach(result => {
          expect(result.data.data.accessToken).toBe(firstToken);
        });
      }

      // Verify that failed results are due to race conditions (500 status)
      failedResults.forEach(result => {
        expect(result.status).toBe(500);
      });

      // Cleanup
      await createUser({ identifiers: { adid: [payload.adid] }, installed_at: new Date() });
    });

    it('should allow concurrent requests with different device IDs', async () => {
      const payload1 = generateUser();
      const payload2 = generateUser();

      const [result1, result2] = await Promise.all([
        createJWT(ANDROID_USER_AGENT, payload1),
        createJWT(ANDROID_USER_AGENT, payload2),
      ]);

      // Both should succeed since they have different device IDs
      expect(result1.status).toBe(201);
      expect(result2.status).toBe(201);
      expect(result1.data.data.accessToken).toBeDefined();
      expect(result2.data.data.accessToken).toBeDefined();

      // Cleanup
      await Promise.all([
        createUser({ identifiers: { adid: [payload1.adid] }, installed_at: new Date() }),
        createUser({ identifiers: { adid: [payload2.adid] }, installed_at: new Date() }),
      ]);
    });
  });
});
