import 'reflect-metadata';
import axios from 'axios';
import {
  TestSetup,
  generateUUID,
  generateAdid,
  validateJwtResponse,
  createUser,
  validateUsersEndpointResponse,
} from '../test-utils';
import { config } from '../config';
import { UserType } from '../../src/users/user.dto';

describe('JWT Creation HTTP Examples E2E', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('HTTP Examples from test-jwt-creation.http', () => {
    describe('Test 1: Create JWT with iOS device ID only (Flow 2.a)', () => {
      it('should create JWT with iOS IDFV', async () => {
        const idfv = generateUUID();

        const response = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { idfv },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        );

        const jwtData = validateJwtResponse(response);
        expect(jwtData.accessToken).toBeTruthy();
        expect(jwtData.accessTokenExpiry).toBeTruthy();

        // Verify token expiry is in the future
        const expiryDate = new Date(jwtData.accessTokenExpiry);
        expect(expiryDate.getTime()).toBeGreaterThan(Date.now());
      });
    });

    describe('Test 2: Create JWT with Android device ID only (Flow 2.a)', () => {
      it('should create JWT with Android ADID', async () => {
        const adid = '1234567890abcdef';

        const response = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { adid },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        );

        const jwtData = validateJwtResponse(response);
        expect(jwtData.accessToken).toBeTruthy();
        expect(jwtData.accessTokenExpiry).toBeTruthy();
      });
    });

    describe('Test 3: Create JWT with device ID + FCID (Flow 2.b)', () => {
      it('should fall back to Flow 2.a when FCID does not exist', async () => {
        const idfv = generateUUID();
        const nonExistentFcid = 'non-existent-fcid-12345';

        const response = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          {
            idfv,
            fcid: nonExistentFcid,
          },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        );

        const jwtData = validateJwtResponse(response);
        expect(jwtData.accessToken).toBeTruthy();
        expect(jwtData.accessTokenExpiry).toBeTruthy();

        // Should succeed despite non-existent FCID (falls back to Flow 2.a)
      });
    });

    describe('Test 4 & 4b: FCID Consistency', () => {
      it('should return consistent FCID for same device ID', async () => {
        const idfv = '12345678-1234-1234-1234-123456789012';

        // First JWT creation
        const firstResponse = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { idfv },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        );

        const firstJwtData = validateJwtResponse(firstResponse);

        // Second JWT creation with same device ID
        const secondResponse = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { idfv },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        );

        const secondJwtData = validateJwtResponse(secondResponse);

        // Both should succeed
        expect(firstJwtData.accessToken).toBeTruthy();
        expect(secondJwtData.accessToken).toBeTruthy();

        // Create users with both tokens to verify FCID consistency
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: { idfv: [idfv] },
          installed_at: 1738772200000,
        };

        const firstUserResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${firstJwtData.accessToken}`,
          },
        });

        const secondUserResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${secondJwtData.accessToken}`,
          },
        });

        const firstUser = validateUsersEndpointResponse(firstUserResponse);
        const secondUser = validateUsersEndpointResponse(secondUserResponse);

        // Should have the same FCID due to deterministic generation
        expect(firstUser.fcid).toBe(secondUser.fcid);

        // Track for cleanup
        if (firstUser.fcid) testSetup.trackCreatedUser(firstUser.fcid);
      });
    });

    describe('Test 5: Invalid request - missing device ID', () => {
      it('should return 400 for missing device ID', async () => {
        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { fcid: 'some-fcid' }, // No device ID
            {
              headers: {
                'x-api-key': config.apiAuthToken,
                'Content-Type': 'application/json',
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 500,
            data: {
              statusCode: 500,
              message: 'Failed to create JWT token',
            },
          },
        });
      });
    });

    describe('Test 6: Invalid request - invalid IDFV format', () => {
      it('should return 400 for invalid IDFV format', async () => {
        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { idfv: 'invalid-uuid-format' },
            {
              headers: {
                'x-api-key': config.apiAuthToken,
                'Content-Type': 'application/json',
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 400,
            data: {
              statusCode: 400,
              message: {
                error: 'Bad Request',
                message: expect.arrayContaining(['Invalid idfv format.']),
                statusCode: 400,
              },
            },
          },
        });
      });
    });

    describe('Test 7 & 8: Create and verify user with new JWT system', () => {
      it('should create user with JWT from new system and verify user creation', async () => {
        const idfv = '12345678-1234-1234-1234-123456789012';

        // Create JWT
        const jwtResponse = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { idfv },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        );

        const jwtData = validateJwtResponse(jwtResponse);

        // Create user with the JWT
        const userData = {
          identifiers: {
            idfv: [idfv],
          },
          installed_at: 1738772200000,
        };

        const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${jwtData.accessToken}`,
          },
        });

        const user = validateUsersEndpointResponse(userResponse);
        expect(user.fcid).toBeTruthy();
        // Note: identifiers are filtered out in response, so we expect empty arrays
        expect(user.identifiers.idfv).toEqual([]);

        // Verify user was created correctly by fetching it
        const getUserResponse = await axios.get(
          `${config.roshiUrl}/users?identifiers.idfv=${idfv}`,
          {
            headers: {
              Authorization: `Bearer ${jwtData.accessToken}`,
            },
          },
        );

        expect(getUserResponse.status).toBe(200);
        expect(getUserResponse.data.data.fcid).toBe(user.fcid);
        expect(getUserResponse.data.data.identifiers.idfv).toEqual([]);

        // Track for cleanup
        if (user.fcid) testSetup.trackCreatedUser(user.fcid);
      });
    });
  });

  describe('Additional Edge Cases', () => {
    it('should handle multiple device identifiers in JWT creation', async () => {
      const idfv = generateUUID();
      const adid = generateAdid();

      // Note: JWT creation typically uses one device ID, but let's test with IDFV
      const response = await axios.post(
        `${config.roshiUrl}/auth/createJwt`,
        { idfv },
        {
          headers: {
            'x-api-key': config.apiAuthToken,
            'Content-Type': 'application/json',
          },
        },
      );

      const jwtData = validateJwtResponse(response);

      // Create user with multiple identifiers
      const userData = {
        identifiers: {
          idfv: [idfv],
          adid: [adid],
        },
        installed_at: Date.now(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtData.accessToken}`,
        },
      });

      const user = validateUsersEndpointResponse(userResponse);
      // Note: identifiers are filtered out in response, so we expect empty arrays
      expect(user.identifiers.idfv).toEqual([]);
      expect(user.identifiers.adid).toEqual([]);

      if (user.fcid) testSetup.trackCreatedUser(user.fcid);
    });

    it('should handle JWT creation with existing user FCID', async () => {
      const idfv = generateUUID();

      // First create a user to get a valid FCID
      const initialUserData = {
        identifiers: { idfv: [idfv] },
        installed_at: Date.now(),
      };

      const initialUserResponse = await createUser(initialUserData);
      const initialUser = validateUsersEndpointResponse(initialUserResponse);
      const existingFcid = initialUser.fcid;

      // Now create JWT with the existing FCID and same device ID
      const jwtResponse = await axios.post(
        `${config.roshiUrl}/auth/createJwt`,
        {
          idfv,
          fcid: existingFcid,
        },
        {
          headers: {
            'x-api-key': config.apiAuthToken,
            'Content-Type': 'application/json',
          },
        },
      );

      const jwtData = validateJwtResponse(jwtResponse);

      // Create another user with this JWT
      const secondUserResponse = await axios.post(`${config.roshiUrl}/users`, initialUserData, {
        headers: {
          Authorization: `Bearer ${jwtData.accessToken}`,
        },
      });

      const secondUser = validateUsersEndpointResponse(secondUserResponse);

      // Should use the same FCID since it's valid and device ID matches
      expect(secondUser.fcid).toBe(existingFcid);
    });

    it('should handle rate limiting gracefully', async () => {
      const idfv = generateUUID();
      const requests: Promise<any>[] = [];

      // Make multiple rapid requests
      for (let i = 0; i < 3; i++) {
        requests.push(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { idfv },
            {
              headers: {
                'x-api-key': config.apiAuthToken,
                'Content-Type': 'application/json',
              },
            },
          ),
        );
      }

      // All should either succeed or fail with rate limiting
      const responses = await Promise.allSettled(requests);

      responses.forEach(result => {
        if (result.status === 'fulfilled') {
          expect(result.value.status).toBe(201);
        } else {
          // If rate limited, should be 429
          expect([201, 429]).toContain(result.reason.response?.status);
        }
      });
    });
  });
});
