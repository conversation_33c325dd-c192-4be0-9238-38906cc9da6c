import { Test, TestingModule } from '@nestjs/testing';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { KwsService } from '../../src/kws/kws.service';
import { UserType } from '../../src/users/entities/user.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';

import type { BaseEvent } from '../../src/webhooks/schemas/types';
import type { User } from '../../src/users/entities/user.entity';
import type { KwsAgeGateResponse } from '../../src/kws/interfaces/kws-response.interface';

describe('WebhookHandler: handle age_selected event', () => {
  let handler: WebhookHandler;
  let kwsService: KwsService;
  let pgUserService: PostgresUserService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookHandler,
        {
          provide: PostgresUserService,
          useValue: {
            findByAttribute: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGateData: jest.fn(),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<WebhookHandler>(WebhookHandler);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    kwsService = module.get<KwsService>(KwsService);
  });

  const now = new Date();
  const mockUser: Partial<User> = {
    fcid: 'test-fcid',
    type: UserType.REGISTERED,
    event_control: {},
    identifiers: {},
    device_ids: [],
    properties: {},
    mergedFcids: [],
    created_at: now,
    updated_at: now,
    installed_at: now,
    webhooks: [],
  };

  const mockEvent: BaseEvent & { timestamp: string } = {
    event_name: 'age_selected',
    provider: 'FlipaClip',
    fcid: 'test-fcid',
    store: 'app_store',
    session_id: 123456789,
    event_control: {
      device_id: 'test-device-id',
      timestamp: 123456789,
    },
    timestamp: now.toISOString(),
    payload: {},
  };

  it('should process an age_selected event without ageSelectorId successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    jest
      .spyOn(kwsService, 'getAgeGateData')
      .mockResolvedValue({ consentAge: 18 } as KwsAgeGateResponse);
    const payload = { userDeclaredAge: 5 };
    const result = await handler.handle({
      ...mockEvent,
      payload,
    });
    expect(result).toEqual({
      eventTimestamp: mockEvent.timestamp,
      fcid: mockEvent.fcid,
      eventName: mockEvent.event_name,
      provider: mockEvent.provider,
      store: mockEvent.store,
      payload: {
        ...payload,
        isMinor: true,
      },
      modified_properties: [
        {
          affected_at: now.toISOString(),
          affected_property: 'userDeclaredAge',
          affected_value: 5,
        },
        {
          affected_at: now.toISOString(),
          affected_property: 'userCurrentAge',
          affected_value: 5,
        },
        {
          affected_at: now.toISOString(),
          affected_property: 'isMinor',
          affected_value: true,
        },
      ],
      sessionId: mockEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      mockEvent.fcid,
      expect.objectContaining({
        properties: {
          isMinor: true,
          userCurrentAge: 5,
          userDeclaredAge: 5,
        },
      }),
    );
  });

  it('should process an age_selected event with ageSelectorId (minor) successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    jest
      .spyOn(kwsService, 'getAgeGateData')
      .mockResolvedValue({ consentAge: 18 } as KwsAgeGateResponse);
    const payload = { ageSelectorId: 'age_selector_12345', userDeclaredAge: 12 };
    const result = await handler.handle({
      ...mockEvent,
      payload,
    });
    expect(result).toEqual({
      eventTimestamp: mockEvent.timestamp,
      fcid: mockEvent.fcid,
      eventName: mockEvent.event_name,
      provider: mockEvent.provider,
      store: mockEvent.store,
      payload: {
        ...payload,
        isMinor: true,
      },
      modified_properties: [
        {
          affected_at: now.toISOString(),
          affected_property: 'userDeclaredAge',
          affected_value: 12,
        },
        {
          affected_at: now.toISOString(),
          affected_property: 'userCurrentAge',
          affected_value: 12,
        },
        {
          affected_at: now.toISOString(),
          affected_property: 'isMinor',
          affected_value: true,
        },
      ],
      sessionId: mockEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      mockEvent.fcid,
      expect.objectContaining({
        properties: {
          isMinor: true,
          userCurrentAge: 12,
          userDeclaredAge: 12,
        },
      }),
    );
  });

  it('should process an age_selected event with ageSelectorId successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    jest
      .spyOn(kwsService, 'getAgeGateData')
      .mockResolvedValue({ consentAge: 18 } as KwsAgeGateResponse);
    const payload = { ageSelectorId: 'age_selector_12345', userDeclaredAge: 19 };
    const result = await handler.handle({
      ...mockEvent,
      payload,
    });
    expect(result).toEqual({
      eventTimestamp: mockEvent.timestamp,
      fcid: mockEvent.fcid,
      eventName: mockEvent.event_name,
      provider: mockEvent.provider,
      store: mockEvent.store,
      payload: {
        ...payload,
        isMinor: false,
      },
      modified_properties: [
        {
          affected_at: now.toISOString(),
          affected_property: 'userDeclaredAge',
          affected_value: 19,
        },
        {
          affected_at: now.toISOString(),
          affected_property: 'userCurrentAge',
          affected_value: 19,
        },
        {
          affected_at: now.toISOString(),
          affected_property: 'isMinor',
          affected_value: false,
        },
      ],
      sessionId: mockEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      mockEvent.fcid,
      expect.objectContaining({
        properties: {
          isMinor: false,
          userCurrentAge: 19,
          userDeclaredAge: 19,
        },
      }),
    );
  });

  it('should not process if no user found', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(null);
    const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn');

    const result = await handler.handle(mockEvent);
    expect(result).toEqual(null);
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);

    expect(pgUserService.updateUser).not.toHaveBeenCalled();
    expect(loggerWarnSpy).toHaveBeenCalledWith(`User not found for fcid: ${mockEvent.fcid}`);
  });
});
