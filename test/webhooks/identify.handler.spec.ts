import { Test, TestingModule } from '@nestjs/testing';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { KwsService } from '../../src/kws/kws.service';
import { UserType } from '../../src/users/entities/user.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';

import type { BaseEvent } from '../../src/webhooks/schemas/types';
import type { User } from '../../src/users/entities/user.entity';

describe('WebhookHandler: identify event', () => {
  let handler: WebhookHandler;
  let pgUserService: PostgresUserService;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookHandler,
        {
          provide: PostgresUserService,
          useValue: {
            findByAttribute: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGate: jest.fn(),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<WebhookHandler>(WebhookHandler);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  describe('handle webhook', () => {
    const mockUser: Partial<User> = {
      fcid: 'test-fcid',
      type: UserType.REGISTERED,
      event_control: {},
      identifiers: {},
      device_ids: [],
      properties: {
        totalProjectsBackedUpCount: 0,
      },
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      webhooks: [],
    };

    const mockEvent: BaseEvent & { timestamp: string } = {
      event_name: 'identify',
      provider: 'FlipaClip',
      fcid: 'test-fcid',
      store: 'app_store',
      session_id: 123456789,
      event_control: {
        device_id: 'test-device-id',
        timestamp: 123456789,
      },
      timestamp: new Date().toISOString(),
      payload: {
        totalProjectsBackedUpCount: 1,
        notificationChannels: ['a', 'b', 'c'],
      },
    };

    it('should process an identify event successfully', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
      const result = await handler.handle(mockEvent);
      expect(result).toEqual({
        eventTimestamp: mockEvent.timestamp,
        fcid: mockEvent.fcid,
        eventName: mockEvent.event_name,
        provider: mockEvent.provider,
        store: mockEvent.store,
        payload: mockEvent.payload,
        modified_properties: [
          {
            affected_at: mockEvent.timestamp,
            affected_property: 'totalProjectsBackedUpCount',
            affected_value: 1,
          },
          {
            affected_at: mockEvent.timestamp,
            affected_property: 'notificationChannels',
            affected_value: ['a', 'b', 'c'],
          },
        ],
        saveToDatabase: true,
        sessionId: mockEvent.session_id,
      });
      expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
      expect(pgUserService.updateUser).toHaveBeenCalledWith(
        mockEvent.fcid,
        expect.objectContaining({
          properties: expect.objectContaining({
            totalProjectsBackedUpCount: 1,
            notificationChannels: ['a', 'b', 'c'],
          }),
        }),
      );
    });

    it('should not process if no user found', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(null);
      const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn');
      const result = await handler.handle(mockEvent);
      expect(result).toEqual(null);
      expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
      expect(pgUserService.updateUser).not.toHaveBeenCalled();
      expect(loggerWarnSpy).toHaveBeenCalledWith(`User not found for fcid: ${mockEvent.fcid}`);
    });
  });
});
