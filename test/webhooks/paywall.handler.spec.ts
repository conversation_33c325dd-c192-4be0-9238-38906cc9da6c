import { Test, TestingModule } from '@nestjs/testing';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { KwsService } from '../../src/kws/kws.service';
import { UserType } from '../../src/users/entities/user.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';

import type { BaseEvent } from '../../src/webhooks/schemas/types';
import type { User } from '../../src/users/entities/user.entity';

describe('WebhookHandler: paywall events', () => {
  let handler: WebhookHandler;
  let pgUserService: PostgresUserService;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookHandler,
        {
          provide: PostgresUserService,
          useValue: {
            findByAttribute: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGate: jest.fn(),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<WebhookHandler>(WebhookHandler);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  const mockUser: Partial<User> = {
    fcid: 'test-fcid',
    type: UserType.REGISTERED,
    event_control: {},
    identifiers: {},
    device_ids: [],
    properties: {},
    mergedFcids: [],
    created_at: new Date(),
    updated_at: new Date(),
    installed_at: new Date(),
    webhooks: [],
  };

  const mockEvent: BaseEvent & { timestamp: string } = {
    event_name: 'subscription_offer_shown',
    provider: 'FlipaClip',
    fcid: 'test-fcid',
    store: 'app_store',
    session_id: 123456789,
    event_control: {
      device_id: 'test-device-id',
      timestamp: 123456789,
    },
    timestamp: new Date().toISOString(),
    payload: {
      paywall_id: 'default_light_qa',
      placement_id: 'home_subscription_button',
      trigger_action: 'app_open',
      plans: [
        {
          id: 'flipaclip_599_1m_7d0',
          offers_free_trial: true,
          period: 'MONTH',
        },
        {
          id: 'flipaclip_2999_1y_7d0',
          offers_free_trial: true,
          period: 'YEAR',
        },
      ],
      ab_test_id: 'test_001',
      ab_test_variant: 'variant_A',
    },
  };

  it('should process a subscription_offer_shown event successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    const result = await handler.handle(mockEvent);
    expect(result).toEqual({
      eventTimestamp: mockEvent.timestamp,
      fcid: mockEvent.fcid,
      eventName: mockEvent.event_name,
      provider: mockEvent.provider,
      store: mockEvent.store,
      payload: mockEvent.payload,
      modified_properties: [
        {
          affected_at: mockEvent.timestamp,
          affected_property: 'totalSubscriptionOffers',
          affected_value: 1,
        },
      ],
      sessionId: mockEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      mockEvent.fcid,
      expect.objectContaining({
        properties: {
          totalSubscriptionOffers: 1,
        },
      }),
    );

    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue({
      ...mockUser,
      properties: {
        totalSubscriptionOffers: 10,
      },
    } as User);
    const anotherResult = await handler.handle(mockEvent);
    expect(anotherResult).toEqual({
      eventTimestamp: mockEvent.timestamp,
      fcid: mockEvent.fcid,
      eventName: mockEvent.event_name,
      provider: mockEvent.provider,
      store: mockEvent.store,
      payload: mockEvent.payload,
      modified_properties: [
        {
          affected_at: mockEvent.timestamp,
          affected_property: 'totalSubscriptionOffers',
          affected_value: 11,
        },
      ],
      sessionId: mockEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      mockEvent.fcid,
      expect.objectContaining({
        properties: {
          totalSubscriptionOffers: 11,
        },
      }),
    );
  });

  it('should process a subscription_offer_aborted event successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    const abortedEvent = {
      ...mockEvent,
      event_name: 'subscription_offer_aborted',
      payload: {
        ...mockEvent.payload,
        offer_selected: 'flipaclip_599_1m_7d0',
        abort_reason: 'Any reason',
      },
    };
    const result = await handler.handle(abortedEvent);
    expect(result).toEqual({
      eventTimestamp: abortedEvent.timestamp,
      fcid: abortedEvent.fcid,
      eventName: abortedEvent.event_name,
      provider: abortedEvent.provider,
      store: abortedEvent.store,
      payload: abortedEvent.payload,
      modified_properties: [
        {
          affected_at: abortedEvent.timestamp,
          affected_property: 'totalSubscriptionOffersAborted',
          affected_value: 1,
        },
      ],
      sessionId: abortedEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      abortedEvent.fcid,
      expect.objectContaining({
        properties: {
          totalSubscriptionOffersAborted: 1,
        },
      }),
    );

    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue({
      ...mockUser,
      properties: {
        totalSubscriptionOffersAborted: 10,
      },
    } as User);
    const anotherResult = await handler.handle(abortedEvent);
    expect(anotherResult).toEqual({
      eventTimestamp: abortedEvent.timestamp,
      fcid: abortedEvent.fcid,
      eventName: abortedEvent.event_name,
      provider: abortedEvent.provider,
      store: abortedEvent.store,
      payload: abortedEvent.payload,
      modified_properties: [
        {
          affected_at: abortedEvent.timestamp,
          affected_property: 'totalSubscriptionOffersAborted',
          affected_value: 11,
        },
      ],
      sessionId: abortedEvent.session_id,
    });
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      abortedEvent.fcid,
      expect.objectContaining({
        properties: {
          totalSubscriptionOffersAborted: 11,
        },
      }),
    );
  });

  it('should not process if no user found', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(null);
    const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn');
    const result = await handler.handle(mockEvent);
    expect(result).toEqual(null);
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
    expect(pgUserService.updateUser).not.toHaveBeenCalled();
    expect(loggerWarnSpy).toHaveBeenCalledWith(`User not found for fcid: ${mockEvent.fcid}`);
  });
});
