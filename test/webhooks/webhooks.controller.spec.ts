import {
  BadRequestException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpStatus } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { WinstonLoggerService } from '../../src/common/services/winston-logger.service';
import { WebhooksController } from '../../src/webhooks/webhooks.controller';
import { SingleWebhookService } from '../../src/webhooks/services/single-webhook.service';
import { BatchWebhookService } from '../../src/webhooks/services/batch-webhook.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { User } from '../../src/users/entities/user.entity';
import { UserType } from '../../src/users/user.dto';
import { FirebaseService } from '../../src/auth/firebase.service';
import { testConfig } from '../../src/config/environments/test.config';
import { Webhook } from '../../src/webhooks/entities/webhook.entity';
import { RedisService } from '../../src/common/services/redis.service';

const LOCAL_IP = '';

describe('WebhooksController', () => {
  let controller: WebhooksController;
  let pgUserService: PostgresUserService;
  let singleWebhookService: SingleWebhookService;
  let batchWebhookService: BatchWebhookService;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    // Set required environment variables
    process.env.JWT_SECRET = 'test-jwt-secret';
    process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';
    process.env.FIREBASE_PROJECT_ID = 'test-project-id';
    process.env.FIREBASE_PRIVATE_KEY = 'test-private-key';
    process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WebhooksController],
      providers: [
        {
          provide: PostgresUserService,
          useValue: {
            findUniqueUser: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: SingleWebhookService,
          useValue: {
            processEvent: jest.fn(),
          },
        },
        {
          provide: BatchWebhookService,
          useValue: {
            processWebhook: jest.fn(),
          },
        },
        ErrorLoggerService,
        WinstonLoggerService,
        {
          provide: FirebaseService,
          useValue: {
            verifyToken: jest.fn().mockResolvedValue({ uid: 'test-uid' }),
            createCustomToken: jest.fn().mockResolvedValue('mock-token'),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'jwt') {
                return testConfig.jwt;
              }
              return testConfig[key as keyof typeof testConfig];
            }),
          },
        },
        {
          provide: RedisService,
          useValue: {},
        },
        {
          provide: require('../../src/webhooks/services/timestamp-validation.service')
            .TimestampValidationService,
          useValue: {
            validateEventByTimestamp: jest.fn().mockImplementation(async event => {
              const user = await pgUserService.findUniqueUser(
                event.fcid,
                event.event_control.device_id,
              );
              const lastTimestamp = user?.event_control?.[event.event_control.device_id];
              const incomingTimestamp = Number(event.event_control.timestamp);
              if (typeof lastTimestamp === 'number' && lastTimestamp >= incomingTimestamp) {
                return { alreadyProcessed: true };
              }
              return undefined;
            }),
          },
        },
        {
          provide: getRepositoryToken(Webhook),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            insert: jest.fn(),
            // Add more methods as needed for your tests
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
        // {
        //   provide: AgeSelectedHandler,
        //   useValue: {
        //     handle: jest.fn(),
        //   },
        // },
      ],
    }).compile();

    controller = module.get<WebhooksController>(WebhooksController);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    singleWebhookService = module.get<SingleWebhookService>(SingleWebhookService);
    batchWebhookService = module.get<BatchWebhookService>(BatchWebhookService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.JWT_SECRET;
    delete process.env.JWT_REFRESH_SECRET;
    delete process.env.FIREBASE_PROJECT_ID;
    delete process.env.FIREBASE_PRIVATE_KEY;
    delete process.env.FIREBASE_CLIENT_EMAIL;
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleWebhook with session_id for ad/paywall events', () => {
    const mockUser = {
      fcid: 'test-fcid',
      type: UserType.ANONYMOUS,
      identifiers: {},
      device_ids: ['test-device-id'],
      properties: {},
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      event_control: {},
      webhooks: [],
    } as User;

    beforeEach(() => {
      jest.spyOn(singleWebhookService, 'processEvent').mockResolvedValue({});
      // Mock findUniqueUser to return our mock user
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      // Mock updateUser to avoid any side effects
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
    });

    const baseEvent = {
      provider: 'FlipaClip',
      event_name: 'ad_shown',
      event_control: {
        device_id: 'test-device-id', // Match the device_id in mockUser
        timestamp: 123456789,
      },
      fcid: 'test-fcid', // Match the fcid in mockUser
      store: 'apple_app_store',
      payload: {
        adUnitId: 'DefaultRewardedVideo',
        adType: 'Rewarded',
        loadTime: 3,
        revenue: 0.0028,
        publisherNetwork: 'admanager',
        triggerAction: 'Add, Merge or Duplicate Layer',
        isRewardGranted: true,
      },
    };

    it('should process webhook with valid numeric session_id', async () => {
      const event = { ...baseEvent, session_id: 12345 };
      const result = await controller.handleWebhook(event, LOCAL_IP);
      expect(result).toEqual({
        message: 'Success',
        statusCode: 201,
        status: 'success',
        modified_properties: {},
        timestamp: expect.any(String),
      });
      expect(singleWebhookService.processEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          session_id: 12345,
        }),
        '',
      );
    });

    it('should process webhook with omitted session_id', async () => {
      const result = await controller.handleWebhook(baseEvent, LOCAL_IP);
      expect(result).toEqual({
        message: 'Success',
        statusCode: 201,
        status: 'success',
        modified_properties: {},
        timestamp: expect.any(String),
      });
      expect(singleWebhookService.processEvent).toHaveBeenCalledWith(
        expect.not.objectContaining({
          session_id: expect.anything(),
        }),
        '',
      );
    });

    it('should process webhook with session_id -1', async () => {
      const event = { ...baseEvent, session_id: -1 };
      const result = await controller.handleWebhook(event, LOCAL_IP);
      expect(result).toEqual({
        message: 'Success',
        statusCode: 201,
        status: 'success',
        modified_properties: {},
        timestamp: expect.any(String),
      });
      expect(singleWebhookService.processEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          session_id: -1,
        }),
        '',
      );
    });

    it('should reject webhook with non-numeric session_id', async () => {
      const event = { ...baseEvent, session_id: 'invalid-session' };
      await expect(controller.handleWebhook(event, LOCAL_IP)).rejects.toThrow(BadRequestException);
    });
  });

  describe('handleWebhookBatch with session_id validation', () => {
    const mockUser = {
      fcid: 'test-fcid',
      type: UserType.ANONYMOUS,
      identifiers: {},
      device_ids: ['test-device-id'],
      properties: {},
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      event_control: {},
      webhooks: [],
    } as User;

    beforeEach(() => {
      jest.spyOn(batchWebhookService, 'processWebhook').mockResolvedValue({});
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
    });

    const baseEvent = {
      provider: 'FlipaClip',
      event_name: 'ad_shown',
      event_control: {
        device_id: 'test-device-id',
        timestamp: 123456789,
      },
      fcid: 'test-fcid',
      store: 'apple_app_store',
      payload: {
        adUnitId: 'DefaultRewardedVideo',
        adType: 'Rewarded',
        loadTime: 3,
        revenue: 0.0028,
        publisherNetwork: 'admanager',
        triggerAction: 'Add, Merge or Duplicate Layer',
        isRewardGranted: true,
      },
    };

    it('should process batch with mixed session_id values', async () => {
      const events = [
        { ...baseEvent, session_id: 12345 }, // Valid numeric
        { ...baseEvent }, // Omitted
        { ...baseEvent, session_id: -1 }, // -1 value
        { ...baseEvent, event_name: 'subscription_offer_shown', session_id: 67890 }, // Different event type
      ];

      const response = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);

      // Verify successful processing
      expect(response.status).toHaveBeenCalledWith(HttpStatus.CREATED);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 201,
          status: 'success',
          processed: 4,
          failed: 0,
          results: expect.any(Array),
        }),
      );

      // Verify each event was processed
      expect(batchWebhookService.processWebhook).toHaveBeenCalledTimes(4);
      events.forEach(event => {
        expect(batchWebhookService.processWebhook).toHaveBeenCalledWith(
          expect.objectContaining({
            eventName: event.event_name,
            provider: event.provider,
            store: event.store,
            deviceId: event.event_control.device_id,
            fcid: event.fcid,
            payload: event.payload,
          } as Partial<Webhook>),
          '',
        );
      });
    });

    it('should reject batch with invalid session_id', async () => {
      const events = [
        { ...baseEvent, session_id: 12345 }, // Valid
        { ...baseEvent, session_id: 'invalid-session' }, // Invalid
        { ...baseEvent }, // Valid (omitted)
      ];

      const response = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);

      // Verify partial success response
      expect(response.status).toHaveBeenCalledWith(HttpStatus.ACCEPTED);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 202,
          status: 'partial_success',
          processed: 2,
          failed: 1,
          validationErrors: expect.arrayContaining([
            expect.objectContaining({
              error: expect.any(String),
              event: expect.objectContaining({ session_id: 'invalid-session' }),
              index: 1,
            }),
          ]),
        }),
      );

      // Verify only valid events were processed
      expect(batchWebhookService.processWebhook).toHaveBeenCalledTimes(2);
    });

    it('should handle empty batch', async () => {
      const response = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await controller.handleWebhookBatch({ events: [] }, LOCAL_IP, response);

      // Verify error response for empty batch
      expect(response.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 400,
          message: expect.stringContaining('No valid events'),
          status: 'error',
          validationErrors: expect.any(Array),
        }),
      );

      // Verify no events were processed
      expect(batchWebhookService.processWebhook).not.toHaveBeenCalled();
    });

    it('should handle batch exceeding max size', async () => {
      const events = Array(101).fill(baseEvent); // Assuming maxBatchSize is 100
      const response = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      // Verify batch size limit is enforced
      await expect(controller.handleWebhookBatch({ events }, LOCAL_IP, response)).rejects.toThrow(
        expect.objectContaining({
          message: expect.stringContaining('Batch size cannot exceed'),
        }),
      );

      // Verify no events were processed
      expect(batchWebhookService.processWebhook).not.toHaveBeenCalled();
    });
  });

  describe('handleWebhook with project backup events', () => {
    const mockUser = {
      fcid: 'test-fcid',
      type: UserType.ANONYMOUS,
      identifiers: {},
      device_ids: ['test-device-id'],
      properties: {},
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      event_control: {},
      webhooks: [],
    } as User;

    beforeEach(() => {
      jest.spyOn(singleWebhookService, 'processEvent').mockResolvedValue({
        eventName: 'project_backed_up',
        provider: 'FlipaClip',
        fcid: 'test-fcid',
        store: 'apple_app_store',
        deviceId: 'test-device-id',
        eventTimestamp: new Date().toISOString(),
        payload: {
          canvasSize: '1920x1080',
          fps: 24,
          backgroundType: 'color',
          totalFramesCount: 100,
          projectType: 'animation',
          projectId: 'proj_123',
          templateId: 'template_456',
          isImportedProject: false,
          triggerAction: 'test',
        },
        modified_properties: {
          totalProjectsBackedUpCount: 1,
        },
      } as Partial<Webhook>);
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
    });

    const baseEvent = {
      provider: 'FlipaClip',
      event_name: 'project_backed_up',
      event_control: {
        device_id: 'test-device-id',
        timestamp: 123456789,
      },
      fcid: 'test-fcid',
      store: 'apple_app_store',
      payload: {
        canvasSize: '1920x1080',
        fps: 24,
        backgroundType: 'Color',
        totalFramesCount: 100,
        projectType: 'Contest',
        projectId: 'proj_123',
        templateId: 'template_456',
        isImportedProject: false,
        triggerAction: 'test', // 1MB
      },
    };

    it('should process project backup webhook successfully', async () => {
      const result = await controller.handleWebhook(baseEvent, LOCAL_IP);

      expect(result).toEqual({
        message: 'Success',
        statusCode: 201,
        status: 'success',
        modified_properties: {
          totalProjectsBackedUpCount: 1,
        },
        timestamp: expect.any(String),
      });

      expect(singleWebhookService.processEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          event_name: 'project_backed_up',
          provider: 'FlipaClip',
          fcid: 'test-fcid',
          payload: baseEvent.payload,
        }),
        '',
      );
    });

    it('should validate project backup event payload', async () => {
      const invalidEvent = {
        ...baseEvent,
        payload: {
          // Missing required fields
          canvasSize: '1920x1080'
        },
      };

      await expect(controller.handleWebhook(invalidEvent, LOCAL_IP)).rejects.toThrow(
        BadRequestException,
      );
      expect(singleWebhookService.processEvent).not.toHaveBeenCalled();
    });

    it('should handle project backup event processing failure', async () => {
      jest
        .spyOn(singleWebhookService, 'processEvent')
        .mockRejectedValue(new Error('Failed to process project backup'));

      await expect(controller.handleWebhook(baseEvent, LOCAL_IP)).rejects.toThrow(
        InternalServerErrorException,
      );
      expect(singleWebhookService.processEvent).toHaveBeenCalled();
    });
  });

  describe('handleWebhookBatch with project backup events', () => {
    const mockUser = {
      fcid: 'test-fcid',
      type: UserType.ANONYMOUS,
      identifiers: {},
      device_ids: ['test-device-id'],
      properties: {},
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      event_control: {},
      webhooks: [],
    } as User;

    beforeEach(() => {
      jest.spyOn(batchWebhookService, 'processWebhook').mockResolvedValue({
        eventName: 'project_backed_up',
        provider: 'FlipaClip',
        fcid: 'test-fcid',
        store: 'apple_app_store',
        deviceId: 'test-device-id',
        eventTimestamp: new Date().toISOString(),
        payload: {
          canvasSize: '1920x1080',
          fps: 24,
          backgroundType: 'Color',
          totalFramesCount: 100,
          projectType: 'Contest',
          projectId: 'proj_123',
          templateId: 'template_456',
          isImportedProject: false,
          triggerAction: 'test',
        },
        modified_properties: {
          totalProjectsBackedUpCount: 1,
        },
      } as Partial<Webhook>);
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
    });

    const baseEvent = {
      provider: 'FlipaClip',
      event_name: 'project_backed_up',
      event_control: {
        device_id: 'test-device-id',
        timestamp: 123456789,
      },
      fcid: 'test-fcid',
      store: 'apple_app_store',
      payload: {
        canvasSize: '1920x1080',
        fps: 24,
        backgroundType: 'Color',
        totalFramesCount: 100,
        projectType: 'Contest',
        projectId: 'proj_123',
        templateId: 'template_456',
        isImportedProject: false,
        triggerAction: 'test', // 1MB
      },
    };

    it('should process batch with mixed event types including project backup', async () => {
      const adShownPayload = {
        adUnitId: 'DefaultRewardedVideo',
        adType: 'Rewarded',
        loadTime: 3,
        revenue: 0.0028,
        publisherNetwork: 'admanager',
        triggerAction: 'Add, Merge or Duplicate Layer',
      };

      const events = [
        { ...baseEvent }, // Project backup event
        { ...baseEvent, event_name: 'ad_shown', payload: adShownPayload }, // Ad event
        { ...baseEvent, event_name: 'subscription_offer_shown' }, // Subscription event
      ];

      const response = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);

      expect(response.status).toHaveBeenCalledWith(HttpStatus.CREATED);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 201,
          status: 'success',
          processed: 3,
          failed: 0,
          results: expect.any(Array),
        }),
      );

      expect(batchWebhookService.processWebhook).toHaveBeenCalledTimes(3);
      events.forEach(event => {
        expect(batchWebhookService.processWebhook).toHaveBeenCalledWith(
          expect.objectContaining({
            eventName: event.event_name,
            provider: event.provider,
            store: event.store,
            deviceId: event.event_control.device_id,
            fcid: event.fcid,
            payload: event.payload,
          } as Partial<Webhook>),
          '',
        );
      });
    });

    it('should handle invalid project backup events in batch', async () => {
      const events = [
        { ...baseEvent }, // Valid project backup event
        {
          ...baseEvent,
          payload: {
            // Invalid payload missing required fields
            canvasSize: '1920x1080'
          },
        },
        { ...baseEvent, event_name: 'ad_shown', payload: { adType: 'Rewarded', revenue: 0.05 } }, // Invalid ad event
      ];

      const response = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);

      // expect(response.status).toHaveBeenCalledWith(HttpStatus.ACCEPTED);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 202,
          status: 'partial_success',
          processed: 1,
          failed: 2,
          validationErrors: expect.arrayContaining([
            expect.objectContaining({
              error: expect.any(String),
              event: expect.objectContaining({
                event_name: 'project_backed_up',
                payload: expect.objectContaining({
                  canvasSize: '1920x1080'
                }),
              }),
              index: 1,
            }),
          ]),
        }),
      );

      expect(batchWebhookService.processWebhook).toHaveBeenCalledTimes(1);
    });
  });

  describe('timestamp validation', () => {
    const fcid = 'test-fcid';
    const deviceId = 'test-device-id';
    const baseEvent = {
      provider: 'FlipaClip',
      event_name: 'ad_shown',
      event_control: {
        device_id: deviceId,
        timestamp: 2000,
      },
      fcid,
      store: 'apple_app_store',
      payload: {
        adUnitId: 'DefaultRewardedVideo',
        adType: 'Rewarded',
        loadTime: 3,
        revenue: 0.0028,
        publisherNetwork: 'admanager',
        triggerAction: 'Add, Merge or Duplicate Layer',
        isRewardGranted: true,
      },
    };

    it('should process single webhook with fresh timestamp', async () => {
      const mockUser = {
        fcid,
        device_ids: [deviceId],
        event_control: { [deviceId]: 1000 },
      } as any;
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
      jest.spyOn(singleWebhookService, 'processEvent').mockResolvedValue({});
      const result = await controller.handleWebhook(baseEvent, LOCAL_IP);
      expect(result.statusCode).toBe(201);
      expect(result.message).toBe('Success');
      expect(singleWebhookService.processEvent).toHaveBeenCalled();
    });

    it('should return already processed for single webhook with old timestamp', async () => {
      const mockUser = {
        fcid,
        device_ids: [deviceId],
        event_control: { [deviceId]: 3000 },
      } as any;
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      const result = await controller.handleWebhook(baseEvent, LOCAL_IP);
      expect(result.statusCode).toBe(201);
      expect(result.message).toContain('Webhook event already processed');
    });

    it('should process batch webhook with all fresh timestamps', async () => {
      const mockUser = {
        fcid,
        device_ids: [deviceId],
        event_control: { [deviceId]: 1000 },
      } as any;
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
      jest.spyOn(batchWebhookService, 'processWebhook').mockResolvedValue({});
      const events = [
        { ...baseEvent, event_control: { device_id: deviceId, timestamp: 2000 } },
        { ...baseEvent, event_control: { device_id: deviceId, timestamp: 2500 } },
      ];
      const response = { status: jest.fn().mockReturnThis(), json: jest.fn() } as any;
      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);
      expect(response.status).toHaveBeenCalledWith(HttpStatus.CREATED);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 201,
          status: 'success',
          processed: 2,
          failed: 0,
        }),
      );
      expect(batchWebhookService.processWebhook).toHaveBeenCalledTimes(2);
    });

    it('should return already processed for batch webhook with all old timestamps', async () => {
      const mockUser = {
        fcid,
        device_ids: [deviceId],
        event_control: { [deviceId]: 3000 },
      } as any;
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      const events = [
        { ...baseEvent, event_control: { device_id: deviceId, timestamp: 2000 } },
        { ...baseEvent, event_control: { device_id: deviceId, timestamp: 2500 } },
      ];
      const response = { status: jest.fn().mockReturnThis(), json: jest.fn() } as any;
      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);
      expect(response.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 200,
          message: expect.stringContaining('Webhook event already processed'),
        }),
      );
    });

    it('should return partial success for batch webhook with mixed timestamps', async () => {
      const mockUser = {
        fcid,
        device_ids: [deviceId],
        event_control: { [deviceId]: 2000 },
      } as any;
      jest.spyOn(pgUserService, 'findUniqueUser').mockResolvedValue(mockUser);
      jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser);
      jest.spyOn(batchWebhookService, 'processWebhook').mockResolvedValue({});
      const events = [
        { ...baseEvent, event_control: { device_id: deviceId, timestamp: 2500 } }, // fresh
        { ...baseEvent, event_control: { device_id: deviceId, timestamp: 1500 } }, // old
      ];
      const response = { status: jest.fn().mockReturnThis(), json: jest.fn() } as any;
      await controller.handleWebhookBatch({ events }, LOCAL_IP, response);
      expect(response.status).toHaveBeenCalledWith(HttpStatus.ACCEPTED);
      expect(response.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 202,
          status: 'partial_success',
          processed: 1,
          failed: 1,
        }),
      );
      expect(batchWebhookService.processWebhook).toHaveBeenCalledTimes(1);
    });
  });
});
