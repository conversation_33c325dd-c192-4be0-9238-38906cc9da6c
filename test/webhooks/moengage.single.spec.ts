import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { SingleWebhookService } from '../../src/webhooks/services/single-webhook.service';
import { UserType } from '../../src/users/user.dto';
import { User } from '../../src/users/entities/user.entity';
import { UsersService } from '../../src/users/users.service';
import { Webhook } from '../../src/webhooks/entities/webhook.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import type { MoEngageEventPayload } from '../../src/webhooks/schemas/moengage.schema';

describe('SingleWebhookService: process multiple MOENGAGE events', () => {
  let singleWebhookService: SingleWebhookService;
  let usersService: UsersService;
  let errorLogger: ErrorLoggerService;
  const mockUser: Partial<User> = {
    fcid: 'test-fcid',
    type: UserType.REGISTERED,
    event_control: {},
    identifiers: {},
    device_ids: [],
    properties: {
      totalProjectsBackedUpCount: 0,
    },
    mergedFcids: [],
    created_at: new Date(),
    updated_at: new Date(),
    installed_at: new Date(),
    webhooks: [],
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SingleWebhookService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Webhook),
          useValue: {
            save: jest.fn(),
          },
        },
        {
          provide: WebhookHandler,
          useValue: {},
        },
        {
          provide: UsersService,
          useValue: {
            findByFcid: jest.fn().mockResolvedValue(mockUser),
            findUserByFcidCached: jest.fn().mockResolvedValue(mockUser),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();
    singleWebhookService = module.get<SingleWebhookService>(SingleWebhookService);
    usersService = module.get<UsersService>(UsersService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);

    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  describe('handle webhook', () => {
    const mockEvent: MoEngageEventPayload = {
      source: 'MOENGAGE',
      app_name: 'FlipaClip',
      moe_request_id: 'moengage-unique-request-id-invalid',
      events: [
        {
          event_name: 'Notification Received Android',
          event_code: 'NOTIFICATION_RECEIVED_MOE',
          event_uuid: 'moengage-unique-event-id-invalid',
          event_time: **********,
          event_type: 'CAMPAIGN_EVENT',
          event_source: 'MOENGAGE',
          push_id: 'recipient-device-push-token',
          uid: mockUser.fcid as string,
          event_attributes: {
            campaign_id: '353df897hkbh67663',
            campaign_name: 'Test Campaign',
            campaign_type: 'Event Trigger',
            campaign_channel: 'Push',
          },
          user_attributes: {
            moengage_user_id: 'moe_internal_user_id_invalid',
            user_attr_1: 'user_attr_val1',
            user_attr_2: 'user_attr_val2',
          },
          device_attributes: {
            moengage_device_id: 'moe_internal_device_id_invalid',
            device_attr_1: 'device_attr_val1',
            device_attr_2: 'device_attr_val2',
          },
        },
      ],
    };

    it('should process an identify event successfully', async () => {
      const timestamp = new Date().toISOString();
      const result = await singleWebhookService.processMoengageEvents(mockEvent.events, timestamp);

      expect(result).toEqual([
        {
          deviceId: 'moe_internal_device_id_invalid',
          eventControlDeviceId: 'moe_internal_device_id_invalid',
          eventControlTimestamp: '**********',
          eventName: 'Notification Received Android',
          eventTimestamp: timestamp,
          fcid: 'test-fcid',
          modified_properties: {},
          payload: {
            device_attributes: {
              device_attr_1: 'device_attr_val1',
              device_attr_2: 'device_attr_val2',
              moengage_device_id: 'moe_internal_device_id_invalid',
            },
            event_attributes: {
              campaign_channel: 'Push',
              campaign_id: '353df897hkbh67663',
              campaign_name: 'Test Campaign',
              campaign_type: 'Event Trigger',
            },
            event_code: 'NOTIFICATION_RECEIVED_MOE',
            event_name: 'Notification Received Android',
            event_source: 'MOENGAGE',
            event_time: **********,
            event_type: 'CAMPAIGN_EVENT',
            event_uuid: 'moengage-unique-event-id-invalid',
            push_id: 'recipient-device-push-token',
            uid: 'test-fcid',
            user_attributes: {
              moengage_user_id: 'moe_internal_user_id_invalid',
              user_attr_1: 'user_attr_val1',
              user_attr_2: 'user_attr_val2',
            },
          },
          provider: 'MOENGAGE',
        },
      ]);
    });

    it('should not process if no user found', async () => {
      jest.spyOn(usersService, 'findUserByFcidCached').mockResolvedValue(null);
      const loggerWarnSpy = jest.spyOn(singleWebhookService['logger'], 'warn');

      const result = await singleWebhookService.processMoengageEvents(
        mockEvent.events,
        new Date().toISOString(),
      );
      expect(result).toEqual([]);
      expect(usersService.findUserByFcidCached).toHaveBeenCalledWith(mockUser.fcid);
      expect(loggerWarnSpy).toHaveBeenCalledWith(`User with fcid ${mockUser.fcid} not found.`);
    });
  });
});
