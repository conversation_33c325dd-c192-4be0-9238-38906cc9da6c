import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BatchWebhookService } from '../../../src/webhooks/services/batch-webhook.service';
import { UsersService } from '../../../src/users/users.service';
import { MetricsService } from '../../../src/webhooks/services/metrics.service';
import { Webhook } from '../../../src/webhooks/entities/webhook.entity';
import { UserType } from '../../../src/users/entities/user.entity';
import { WebhookHandler } from '../../../src/webhooks/handlers/webhook.handler';
import { KwsService } from '../../../src/kws/kws.service';

const LOCAL_IP = '';

describe('BatchWebhookService', () => {
  let service: BatchWebhookService;
  let webhookRepository: Repository<Webhook>;
  let kwsService: KwsService;
  let webhookHandler: WebhookHandler;
  let usersService: UsersService;
  let metricsService: MetricsService;
  let logger: Logger;
  let errorLogger: { log: jest.Mock; logError: jest.Mock };

  beforeEach(async () => {
    const mockKwsService = {
      getAgeGate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BatchWebhookService,
        {
          provide: getRepositoryToken(Webhook),
          useValue: {
            save: jest.fn(),
          },
        },
        {
          provide: WebhookHandler,
          useValue: {
            handle: jest.fn(),
          },
        },
        {
          provide: UsersService,
          useValue: {
            findByFcid: jest.fn(),
            findUserByFcidCached: jest.fn(),
            updateUserProperties: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: mockKwsService,
        },
        {
          provide: 'BullQueue_webhooks',
          useValue: {
            add: jest.fn(),
          },
        },
        {
          provide: MetricsService,
          useValue: {
            startWebhookProcessing: jest.fn().mockReturnValue(() => 1),
            endWebhookProcessing: jest.fn(),
            recordError: jest.fn(),
            recordRevenue: jest.fn(),
            recordWebhookProcessed: jest.fn(),
          },
        },
        Logger,
        {
          provide: require('../../../src/common/services/error-logger.service').ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BatchWebhookService>(BatchWebhookService);
    kwsService = module.get<KwsService>(KwsService);
    webhookRepository = module.get<Repository<Webhook>>(getRepositoryToken(Webhook));
    webhookHandler = module.get<WebhookHandler>(WebhookHandler);
    usersService = module.get<UsersService>(UsersService);
    metricsService = module.get<MetricsService>(MetricsService);
    logger = module.get<Logger>(Logger);

    // Get the errorLogger mock for assertions
    errorLogger = module.get(
      require('../../../src/common/services/error-logger.service').ErrorLoggerService,
    );

    // Mock the service's logger directly
    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    // Replace the service's logger with our mock
    Object.defineProperty(service, 'logger', {
      value: mockLogger,
      writable: true,
    });

    // Use the mock logger for assertions
    logger = mockLogger as unknown as Logger;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processWebhook', () => {
    const webhook: Webhook = {
      id: 1,
      eventName: 'ad_shown',
      provider: 'FlipaClip',
      store: 'app_store',
      deviceId: 'test_device',
      eventTimestamp: new Date().toISOString(),
      payload: {
        revenue: 0.05,
        adType: 'Rewarded',
        loadTime: 1.5,
        adUnitId: 'test_ad_unit',
        publisherNetwork: 'test_network',
        triggerAction: 'test_action',
      },
      fcid: 'test_fcid',
      modified_properties: [],
      sessionId: 12345,
      user: {
        fcid: 'test-fcid',
        type: UserType.REGISTERED,
        event_control: {},
        identifiers: {},
        device_ids: [],
        properties: {},
        mergedFcids: [],
        created_at: new Date(),
        updated_at: new Date(),
        installed_at: new Date(),
        webhooks: [],
      },
    };

    it('should process a webhook synchronously', async () => {
      (webhookHandler.handle as jest.Mock).mockResolvedValue({ modified_properties: [] });
      (usersService.findUserByFcidCached as jest.Mock).mockResolvedValue({ properties: {} });

      await service.processWebhook(webhook, LOCAL_IP);

      expect(webhookHandler.handle).toHaveBeenCalledWith(
        expect.objectContaining({
          event_name: webhook.eventName,
          provider: webhook.provider,
          fcid: webhook.fcid,
          payload: webhook.payload,
          timestamp: webhook.eventTimestamp,
        }),
        LOCAL_IP,
      );
      expect(webhookRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deviceId: webhook.deviceId,
          modified_properties: [],
        }),
      );
      expect(usersService.findUserByFcidCached).toHaveBeenCalledWith(webhook.fcid);
      expect(usersService.updateUserProperties).toHaveBeenCalledWith(
        webhook.fcid,
        expect.objectContaining({ last_amplitude_device_id: webhook.deviceId }),
      );
      expect(logger.log).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Saved webhook data to repository',
          fcid: webhook.fcid,
          eventName: webhook.eventName,
          provider: webhook.provider,
        }),
      );
    });

    it('should handle errors during processing', async () => {
      const error = new Error('Processing error');
      (webhookHandler.handle as jest.Mock).mockRejectedValue(error);

      expect.assertions(3);
      try {
        await service.processWebhook(webhook, LOCAL_IP);
        throw new Error('Expected processWebhook to throw an error');
      } catch (e) {
        expect(e).toEqual(error);
        expect(errorLogger.logError).toHaveBeenCalled();
        expect(metricsService.recordError).not.toHaveBeenCalled(); // Not called in sync process
      }
    });

    it('should skip Purchasely webhooks', async () => {
      const purchaselyWebhook: Webhook = {
        ...webhook,
        provider: 'purchasely',
      };

      await service.processWebhook(purchaselyWebhook, LOCAL_IP);

      expect(webhookHandler.handle).not.toHaveBeenCalled();
      expect(webhookRepository.save).not.toHaveBeenCalled();
      expect(usersService.findByFcid).not.toHaveBeenCalled();
      expect(usersService.updateUserProperties).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalled();
    });
  });

  describe('processWebhookJob', () => {
    const webhook: Webhook = {
      id: 1,
      eventName: 'ad_shown',
      provider: 'FlipaClip',
      store: 'app_store',
      deviceId: 'test_device',
      eventTimestamp: new Date().toISOString(),
      payload: {
        revenue: 0.05,
        adType: 'Rewarded',
        loadTime: 1.5,
        adUnitId: 'test_ad_unit',
        publisherNetwork: 'test_network',
        triggerAction: 'test_action',
      },
      fcid: 'test_fcid',
      modified_properties: [],
      sessionId: 12345,
      user: {
        fcid: 'test-fcid',
        type: UserType.REGISTERED,
        event_control: {},
        identifiers: {},
        device_ids: [],
        properties: {},
        mergedFcids: [],
        created_at: new Date(),
        updated_at: new Date(),
        installed_at: new Date(),
        webhooks: [],
      },
    };

    it('should process an ad event job successfully', async () => {
      (webhookHandler.handle as jest.Mock).mockResolvedValue({ modified_properties: [] });
      (usersService.findUserByFcidCached as jest.Mock).mockResolvedValue({ properties: {} });

      await service.processWebhookJob(webhook);

      expect(webhookHandler.handle).toHaveBeenCalledWith(
        expect.objectContaining({
          event_name: webhook.eventName,
          provider: webhook.provider,
          fcid: webhook.fcid,
          payload: webhook.payload,
          timestamp: webhook.eventTimestamp,
        }),
      );
      expect(webhookRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deviceId: webhook.deviceId,
          modified_properties: [],
        }),
      );
      expect(usersService.findUserByFcidCached).toHaveBeenCalledWith(webhook.fcid);
      expect(usersService.updateUserProperties).toHaveBeenCalledWith(
        webhook.fcid,
        expect.objectContaining({ last_amplitude_device_id: webhook.deviceId }),
      );
      expect(metricsService.recordRevenue).toHaveBeenCalledWith(
        webhook.provider,
        'ad',
        webhook.payload.revenue,
      );
      expect(metricsService.endWebhookProcessing).toHaveBeenCalledWith(
        expect.any(Function),
        webhook.provider,
        webhook.eventName,
        'success',
      );
    });

    it('should process a subscription event job successfully', async () => {
      const subscriptionWebhook: Webhook = {
        ...webhook,
        eventName: 'subscription_offer_shown',
      };
      (webhookHandler.handle as jest.Mock).mockResolvedValue({ modified_properties: [] });
      (usersService.findUserByFcidCached as jest.Mock).mockResolvedValue({ properties: {} });

      await service.processWebhookJob(subscriptionWebhook);

      expect(webhookHandler.handle).toHaveBeenCalledWith(
        expect.objectContaining({
          event_name: subscriptionWebhook.eventName,
          provider: subscriptionWebhook.provider,
          fcid: subscriptionWebhook.fcid,
          payload: subscriptionWebhook.payload,
          timestamp: subscriptionWebhook.eventTimestamp,
        }),
      );
      expect(webhookRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deviceId: subscriptionWebhook.deviceId,
          modified_properties: [],
        }),
      );
      expect(usersService.findUserByFcidCached).toHaveBeenCalledWith(subscriptionWebhook.fcid);
      expect(usersService.updateUserProperties).toHaveBeenCalledWith(
        subscriptionWebhook.fcid,
        expect.objectContaining({ last_amplitude_device_id: subscriptionWebhook.deviceId }),
      );
      expect(metricsService.endWebhookProcessing).toHaveBeenCalledWith(
        expect.any(Function),
        subscriptionWebhook.provider,
        subscriptionWebhook.eventName,
        'success',
      );
    });

    it('should process a project backup event job successfully', async () => {
      const projectBackupWebhook: Webhook = {
        ...webhook,
        eventName: 'project_backed_up',
        payload: {
          canvasSize: '1920x1080',
          fps: 24,
          backgroundType: 'color',
          totalFramesCount: 100,
          projectType: 'animation',
          projectId: 'proj_123',
          templateId: 'template_456',
          isImportedProject: false,
          triggerAction: 'test', // 1MB
        },
      };

      (webhookHandler.handle as jest.Mock).mockResolvedValue({
        success: true,
        modified_properties: {
          totalProjectsBackedUpCount: 1,
        },
      });
      (usersService.findUserByFcidCached as jest.Mock).mockResolvedValue({ properties: {} });

      await service.processWebhookJob(projectBackupWebhook);

      expect(webhookHandler.handle).toHaveBeenCalledWith(
        expect.objectContaining({
          event_name: projectBackupWebhook.eventName,
          provider: projectBackupWebhook.provider,
          fcid: projectBackupWebhook.fcid,
          payload: projectBackupWebhook.payload,
          timestamp: projectBackupWebhook.eventTimestamp,
        }),
      );
      expect(webhookRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          deviceId: projectBackupWebhook.deviceId,
          modified_properties: expect.objectContaining({
            totalProjectsBackedUpCount: 1,
          }),
        }),
      );
      expect(usersService.findUserByFcidCached).toHaveBeenCalledWith(projectBackupWebhook.fcid);
      expect(usersService.updateUserProperties).toHaveBeenCalledWith(
        projectBackupWebhook.fcid,
        expect.objectContaining({ last_amplitude_device_id: projectBackupWebhook.deviceId }),
      );
      expect(metricsService.endWebhookProcessing).toHaveBeenCalledWith(
        expect.any(Function),
        projectBackupWebhook.provider,
        projectBackupWebhook.eventName,
        'success',
      );
    });

    it('should handle project backup event processing failure', async () => {
      const projectBackupWebhook: Webhook = {
        ...webhook,
        eventName: 'project_backed_up',
        payload: {
          canvasSize: '1920x1080',
          fps: 24,
        },
      };

      const error = new Error('Failed to process project backup');
      (webhookHandler.handle as jest.Mock).mockRejectedValue(error);

      await expect(service.processWebhookJob(projectBackupWebhook)).rejects.toThrow(error);

      expect(metricsService.recordError).toHaveBeenCalledWith(
        projectBackupWebhook.provider,
        'project_backed_up',
        error.name,
      );
      expect(metricsService.endWebhookProcessing).toHaveBeenCalledWith(
        expect.any(Function),
        projectBackupWebhook.provider,
        projectBackupWebhook.eventName,
        'error',
      );
    });

    it('should handle errors during job processing', async () => {
      const error = new Error('Job processing error');
      (webhookHandler.handle as jest.Mock).mockRejectedValue(error);

      expect.assertions(3);
      try {
        await service.processWebhookJob(webhook);
        fail('Expected processWebhookJob to throw an error');
      } catch (e) {
        expect(errorLogger.logError).toHaveBeenCalled();
        expect(metricsService.endWebhookProcessing).toHaveBeenCalledWith(
          expect.any(Function),
          webhook.provider,
          webhook.eventName,
          'error',
        );
        expect(e).toEqual(error);
      }
    });

    it('should handle user not found', async () => {
      (webhookHandler.handle as jest.Mock).mockResolvedValue({ modified_properties: [] });
      (usersService.findUserByFcidCached as jest.Mock).mockResolvedValue(null);

      await service.processWebhookJob(webhook);

      expect(logger.warn).toHaveBeenCalled();
      expect(usersService.updateUserProperties).not.toHaveBeenCalled();
    });

    it('should skip Purchasely webhook jobs', async () => {
      const purchaselyWebhook: Webhook = {
        ...webhook,
        provider: 'purchasely',
      };

      await service.processWebhookJob(purchaselyWebhook);

      expect(webhookHandler.handle).not.toHaveBeenCalled();
      expect(webhookRepository.save).not.toHaveBeenCalled();
      expect(usersService.findUserByFcidCached).not.toHaveBeenCalled();
      expect(usersService.updateUserProperties).not.toHaveBeenCalled();
      expect(metricsService.recordRevenue).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalled();
    });

    it('should use default store value if not provided', async () => {
      const webhookWithoutStore: Webhook = {
        ...webhook,
        store: '',
      };
      (webhookHandler.handle as jest.Mock).mockResolvedValue({ modified_properties: [] });
      (usersService.findUserByFcidCached as jest.Mock).mockResolvedValue({ properties: {} });

      await service.processWebhookJob(webhookWithoutStore);

      expect(webhookHandler.handle).toHaveBeenCalled();
      const callArgs = (webhookHandler.handle as jest.Mock).mock.calls[0][0];
      expect(callArgs.store).toBe('unknown');
    });
  });

  describe('getWebhookPriority', () => {
    it('should return correct priority for subscription_offer_shown', () => {
      const webhook: Webhook = { eventName: 'subscription_offer_shown' } as Webhook;
      expect((service as any).getWebhookPriority(webhook)).toBe(1);
    });

    it('should return correct priority for subscription_offer_aborted', () => {
      const webhook: Webhook = { eventName: 'subscription_offer_aborted' } as Webhook;
      expect((service as any).getWebhookPriority(webhook)).toBe(1);
    });

    it('should return correct priority for ad_shown', () => {
      const webhook: Webhook = { eventName: 'ad_shown' } as Webhook;
      expect((service as any).getWebhookPriority(webhook)).toBe(2);
    });

    it('should return correct priority for project_backed_up', () => {
      const webhook: Webhook = { eventName: 'project_backed_up' } as Webhook;
      expect((service as any).getWebhookPriority(webhook)).toBe(3);
    });

    it('should return default priority for unknown events', () => {
      const webhook: Webhook = { eventName: 'unknown_event' } as Webhook;
      expect((service as any).getWebhookPriority(webhook)).toBe(3);
    });
  });
});
