import { Test, TestingModule } from '@nestjs/testing';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { KwsService } from '../../src/kws/kws.service';
import { UserType } from '../../src/users/entities/user.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';

import type { BaseEvent } from '../../src/webhooks/schemas/types';
import type { User } from '../../src/users/entities/user.entity';

describe('WebhookHandler: purchasely event', () => {
  let handler: WebhookHandler;
  let pgUserService: PostgresUserService;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookHandler,
        {
          provide: PostgresUserService,
          useValue: {
            findByAttribute: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGate: jest.fn(),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<WebhookHandler>(WebhookHandler);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  describe('handle webhook', () => {
    const mockUser: Partial<User> = {
      fcid: 'test-fcid',
      type: UserType.REGISTERED,
      event_control: {},
      identifiers: {},
      device_ids: [],
      properties: {},
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      webhooks: [],
    };

    const basePayload = {
      event_name: 'ACTIVATE',
      plan: 'monthly',
      product: 'PURCHASELY_PLUS',
      purchasely_subscription_id: 'subs_123456',
    };

    const mockEvent: BaseEvent & { timestamp: string } = {
      event_name: 'ACTIVATE',
      fcid: 'test-fcid',
      provider: 'purchasely',
      store: 'GOOGLE_PLAY_STORE',
      payload: {
        ...basePayload,
        store: 'GOOGLE_PLAY_STORE',
        subscription_status: 'AUTO_RENEWING',
      },
      event_control: {
        device_id: 'test-device-id',
        timestamp: new Date().getTime(),
      },
      timestamp: new Date().toISOString(),
    };

    it('should process a purchasely event successfully', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
      const result = await handler.handle(mockEvent);
      expect(result).toBeDefined();
      expect(result?.modified_properties).toHaveLength(3);
      expect(result).toEqual(
        expect.objectContaining({
          eventTimestamp: mockEvent.timestamp,
          fcid: mockEvent.fcid,
          eventName: mockEvent.event_name,
          provider: mockEvent.provider,
          payload: {
            ...basePayload,
            store: 'Google Play Store',
            'Subscription State': 'Auto Renewing',
          },
          modified_properties: expect.arrayContaining([
            expect.objectContaining({
              affected_at: mockEvent.timestamp,
              affected_property: 'subscriptionType',
              affected_value: 'monthly',
            }),
            expect.objectContaining({
              affected_at: mockEvent.timestamp,
              affected_property: 'subscriptionState',
              affected_value: 'Auto Renewing',
            }),
            expect.objectContaining({
              affected_at: mockEvent.timestamp,
              affected_property: 'purchasely_subscription_id',
              affected_value: 'subs_123456',
            }),
          ]),
          sessionId: mockEvent.session_id,
          store: mockEvent.store,
        }),
      );
      expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
      expect(pgUserService.updateUser).toHaveBeenCalledWith(
        mockEvent.fcid,
        expect.objectContaining({
          properties: expect.objectContaining({
            purchasely_subscription_id: 'subs_123456',
            subscriptionState: 'Auto Renewing',
            subscriptionType: 'monthly',
          }),
        }),
      );
    });

    it('should not process if no user found', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(null);
      const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn');
      const result = await handler.handle(mockEvent);
      expect(result).toEqual(null);
      expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
      expect(pgUserService.updateUser).not.toHaveBeenCalled();
      expect(loggerWarnSpy).toHaveBeenCalledWith(`User not found for fcid: ${mockEvent.fcid}`);
    });
  });
});
