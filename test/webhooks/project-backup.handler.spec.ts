import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { KwsService } from '../../src/kws/kws.service';
import { UserType } from '../../src/users/entities/user.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';
import { ProjectBackupEventPayload } from '../../src/webhooks/schemas/project-backup.schema';

import type { User } from '../../src/users/entities/user.entity';

describe('WebhookHandler: project_backed_up', () => {
  let handler: WebhookHandler;
  let pgUserService: PostgresUserService;
  let logger: Logger;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookHandler,
        {
          provide: PostgresUserService,
          useValue: {
            findByAttribute: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGate: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<WebhookHandler>(WebhookHandler);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    logger = module.get<Logger>(Logger);

    // Mock the handler's logger directly
    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    Object.defineProperty(handler, 'logger', {
      value: mockLogger,
      writable: true,
    });
    logger = mockLogger as unknown as Logger;
  });

  describe('handle', () => {
    const mockUser: Partial<User> = {
      fcid: 'test-fcid',
      type: UserType.REGISTERED,
      event_control: {},
      identifiers: {},
      device_ids: [],
      properties: {
        totalProjectsBackedUpCount: 0,
      },
      mergedFcids: [],
      created_at: new Date(),
      updated_at: new Date(),
      installed_at: new Date(),
      webhooks: [],
    };

    const mockEvent: ProjectBackupEventPayload & { timestamp: string } = {
      event_name: 'project_backed_up',
      provider: 'FlipaClip',
      fcid: 'test-fcid',
      store: 'app_store',
      session_id: 123456789,
      event_control: {
        device_id: 'test-device-id',
        timestamp: 123456789,
      },
      timestamp: new Date().toISOString(),
      payload: {
        canvasSize: '1920x1080',
        fps: 24,
        backgroundType: 'color',
        totalFramesCount: 100,
        projectType: 'animation',
        projectId: 'proj_123',
        templateId: 'template_456',
        isImportedProject: false,
        triggerAction: 'manual_backup',
      },
    };

    it('should process a project backup event successfully', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
      const result = await handler.handle(mockEvent);
      expect(result).toEqual({
        eventTimestamp: mockEvent.timestamp,
        fcid: mockEvent.fcid,
        eventName: mockEvent.event_name,
        provider: mockEvent.provider,
        store: mockEvent.store,
        payload: mockEvent.payload,
        modified_properties: [
          {
            affected_at: mockEvent.timestamp,
            affected_property: 'totalProjectsBackedUpCount',
            affected_value: 1,
          },
        ],
        sessionId: mockEvent.session_id,
      });
      expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);
      expect(pgUserService.updateUser).toHaveBeenCalledWith(
        mockEvent.fcid,
        expect.objectContaining({
          properties: expect.objectContaining({
            totalProjectsBackedUpCount: 1,
          }),
        }),
      );
    });

    it('should handle existing backup data correctly', async () => {
      const existingUser: Partial<User> = {
        ...mockUser,
        properties: {
          ...mockUser.properties,
          totalProjectsBackedUpCount: 5,
        },
      };
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(existingUser as User);
      const result = await handler.handle(mockEvent);
      expect(result).toEqual({
        eventTimestamp: mockEvent.timestamp,
        fcid: mockEvent.fcid,
        eventName: mockEvent.event_name,
        provider: mockEvent.provider,
        store: mockEvent.store,
        payload: mockEvent.payload,
        modified_properties: [
          {
            affected_at: mockEvent.timestamp,
            affected_property: 'totalProjectsBackedUpCount',
            affected_value: 6,
          },
        ],
        sessionId: mockEvent.session_id,
      });
      expect(pgUserService.updateUser).toHaveBeenCalledWith(
        mockEvent.fcid,
        expect.objectContaining({
          properties: expect.objectContaining({
            totalProjectsBackedUpCount: 6,
          }),
        }),
      );
    });

    it('should handle user not found', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(null);
      const result = await handler.handle(mockEvent);
      expect(result).toEqual(null);
      expect(logger.warn).toHaveBeenCalledWith(`User not found for fcid: ${mockEvent.fcid}`);
      expect(pgUserService.updateUser).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
      jest.spyOn(pgUserService, 'updateUser').mockRejectedValue(new Error('Database error'));
      await expect(handler.handle(mockEvent)).rejects.toThrow('Database error');
      expect(logger.error).not.toHaveBeenCalled();
    });
  });
});
