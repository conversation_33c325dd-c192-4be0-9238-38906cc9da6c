import { Test, TestingModule } from '@nestjs/testing';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { KwsService } from '../../src/kws/kws.service';
import { UserType } from '../../src/users/entities/user.entity';
import { WebhookHandler } from '../../src/webhooks/handlers/webhook.handler';

import type { BaseEvent } from '../../src/webhooks/schemas/types';
import type { User } from '../../src/users/entities/user.entity';

describe('WebhookHandler: ad events', () => {
  let handler: WebhookHandler;
  let errorLogger: ErrorLoggerService;
  let pgUserService: PostgresUserService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookHandler,
        {
          provide: PostgresUserService,
          useValue: {
            findByAttribute: jest.fn(),
            updateUser: jest.fn(),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGate: jest.fn(),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
            // Add more mocked methods if needed
          },
        },
      ],
    }).compile();

    handler = module.get<WebhookHandler>(WebhookHandler);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
  });

  const mockUser: Partial<User> = {
    fcid: 'test-fcid',
    type: UserType.REGISTERED,
    event_control: {},
    identifiers: {},
    device_ids: [],
    properties: {
      totalProjectsBackedUpCount: 0,
    },
    mergedFcids: [],
    created_at: new Date(),
    updated_at: new Date(),
    installed_at: new Date(),
    webhooks: [],
  };

  const mockEvent: BaseEvent & { timestamp: string } = {
    event_name: 'ad_shown',
    provider: 'FlipaClip',
    fcid: 'test-fcid',
    store: 'app_store',
    session_id: 123456789,
    event_control: {
      device_id: 'test-device-id',
      timestamp: 123456789,
    },
    timestamp: new Date().toISOString(),
    payload: {
      adUnitId: 'DefaultRewardedVideo',
      adType: 'Rewarded',
      loadTime: 3,
      revenue: 0.0028,
      publisherNetwork: 'admanager',
      triggerAction: 'Add, Merge or Duplicate Layer',
      isRewardGranted: true,
    },
  };

  it('should process an ad_shown with FlipaClip as provider event successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser as User);

    const result = await handler.handle(mockEvent);

    expect(result).toEqual({
      eventTimestamp: mockEvent.timestamp,
      fcid: mockEvent.fcid,
      eventName: mockEvent.event_name,
      provider: mockEvent.provider,
      store: mockEvent.store,
      payload: mockEvent.payload,
      modified_properties: [
        {
          affected_at: mockEvent.timestamp,
          affected_property: 'totalAdsShown',
          affected_value: 1,
        },
        {
          affected_at: mockEvent.timestamp,
          affected_property: 'totalAdRevenue',
          affected_value: mockEvent.payload.revenue,
        },
        {
          affected_at: mockEvent.timestamp,
          affected_property: 'totalRevenue',
          affected_value: mockEvent.payload.revenue,
        },
      ],
      sessionId: mockEvent.session_id,
    });

    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);

    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      mockEvent.fcid,
      expect.objectContaining({
        properties: expect.objectContaining({
          totalAdRevenue: 0.0028,
          totalAdsShown: 1,
          totalProjectsBackedUpCount: 0,
          totalRevenue: 0.0028,
        }),
      }),
    );
  });

  it('should process an ad_shown with iron-source as provider event successfully', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(mockUser as User);
    jest.spyOn(pgUserService, 'updateUser').mockResolvedValue(mockUser as User);

    const ironSourceEvent = {
      ...mockEvent,
      provider: 'iron-source',
      payload: {
        revenue: 0.00025,
        adType: 'Rewarded',
        abTestGroup: 'A',
        instanceName: 'Bidding',
        segmentName: 'non_coppa',
        loadTime: 1,
        adUnitId: 'DefaultRewardedVideo',
        isRewardGranted: true,
        publisherNetwork: 'IronSource',
        triggerAction: 'user_initiated',
      },
    };

    const result = await handler.handle(ironSourceEvent);

    expect(result).toEqual({
      eventTimestamp: ironSourceEvent.timestamp,
      fcid: ironSourceEvent.fcid,
      eventName: ironSourceEvent.event_name,
      provider: ironSourceEvent.provider,
      store: ironSourceEvent.store,
      payload: ironSourceEvent.payload,
      modified_properties: [
        {
          affected_at: ironSourceEvent.timestamp,
          affected_property: 'totalAdsShown',
          affected_value: 1,
        },
        {
          affected_at: ironSourceEvent.timestamp,
          affected_property: 'totalAdRevenue',
          affected_value: ironSourceEvent.payload.revenue,
        },
        {
          affected_at: ironSourceEvent.timestamp,
          affected_property: 'totalRevenue',
          affected_value: ironSourceEvent.payload.revenue,
        },
      ],
      sessionId: ironSourceEvent.session_id,
    });

    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', ironSourceEvent.fcid);

    expect(pgUserService.updateUser).toHaveBeenCalledWith(
      ironSourceEvent.fcid,
      expect.objectContaining({
        properties: expect.objectContaining({
          totalAdRevenue: 0.00025,
          totalAdsShown: 1,
          totalProjectsBackedUpCount: 0,
          totalRevenue: 0.00025,
        }),
      }),
    );
  });

  it('should not process if no user found', async () => {
    jest.spyOn(pgUserService, 'findByAttribute').mockResolvedValue(null);
    const loggerWarnSpy = jest.spyOn(handler['logger'], 'warn');

    const result = await handler.handle(mockEvent);
    expect(result).toEqual(null);
    expect(pgUserService.findByAttribute).toHaveBeenCalledWith('fcid', mockEvent.fcid);

    expect(pgUserService.updateUser).not.toHaveBeenCalled();
    expect(loggerWarnSpy).toHaveBeenCalledWith(`User not found for fcid: ${mockEvent.fcid}`);
  });
});
