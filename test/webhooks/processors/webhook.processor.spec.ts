import { Test, TestingModule } from '@nestjs/testing';
import { WebhookProcessor } from '../../../src/webhooks/processors/webhook.processor';
import { BatchWebhookService } from '../../../src/webhooks/services/batch-webhook.service';
import { Job } from 'bull';
import { Webhook } from '../../../src/webhooks/entities/webhook.entity';
import { Logger } from '@nestjs/common';
import { Validator } from 'jsonschema';
import { batchConfig } from '../../../src/config/batch.config';
import { UserType } from '../../../src/users/entities/user.entity';
import { RedisService } from '../../../src/common/services/redis.service';
import { ErrorLoggerService } from '../../../src/common/services/error-logger.service';
jest.mock('jsonschema', () => ({
  Validator: jest.fn().mockImplementation(() => ({
    validate: jest.fn(),
  })),
}));

describe('WebhookProcessor', () => {
  let processor: WebhookProcessor;
  let batchWebhookService: BatchWebhookService;
  let validator: Validator;
  let logger: Logger;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookProcessor,
        {
          provide: BatchWebhookService,
          useValue: {
            processWebhookJob: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
        {
          provide: RedisService,
          useValue: {
            addToDlq: jest.fn(),
          },
        },
        {
          provide: require('../../../src/webhooks/services/timestamp-validation.service')
            .TimestampValidationService,
          useValue: {
            validateEventByTimestamp: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    processor = module.get<WebhookProcessor>(WebhookProcessor);
    batchWebhookService = module.get<BatchWebhookService>(BatchWebhookService);
    logger = module.get<Logger>(Logger);
    validator = (processor as any).validator;
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
    // Mock the processor's logger directly
    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    // Replace the processor's logger with our mock
    Object.defineProperty(processor, 'logger', {
      value: mockLogger,
      writable: true,
    });

    // Use the mock logger for assertions
    logger = mockLogger as unknown as Logger;
  });

  it('should be defined', () => {
    expect(processor).toBeDefined();
  });

  describe('processWebhook', () => {
    let job: Job<Webhook>;
    let webhookData: Webhook;

    beforeEach(() => {
      webhookData = {
        id: 1,
        eventName: 'test_event',
        provider: 'FlipaClip',
        store: 'app_store',
        deviceId: 'test_device',
        eventTimestamp: new Date().toISOString(),
        payload: {},
        fcid: 'test_fcid',
        modified_properties: [],
        sessionId: 12345,
        user: {
          fcid: 'test-fcid',
          type: UserType.REGISTERED,
          event_control: {},
          identifiers: {},
          device_ids: [],
          properties: {},
          mergedFcids: [],
          created_at: new Date(),
          updated_at: new Date(),
          installed_at: new Date(),
          webhooks: [],
        },
      };

      job = {
        id: 1,
        data: webhookData,
        progress: jest.fn(),
        moveToFailed: jest.fn(),
        attemptsMade: 0,
        opts: { attempts: batchConfig.retry.attempts },
      } as unknown as Job<Webhook>;
    });

    it('should process a valid webhook job successfully', async () => {
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });
      (batchWebhookService.processWebhookJob as jest.Mock).mockResolvedValue(undefined);

      const result = await processor.processWebhook(job);

      expect(result.success).toBe(true);
      expect(result.jobId).toBe(1);
      expect(result.eventId).toBe('1');
      expect(batchWebhookService.processWebhookJob).toHaveBeenCalledWith(webhookData);
      expect(job.progress).toHaveBeenCalledWith(100);
      expect(logger.debug).toHaveBeenCalledWith('Successfully processed webhook job 1');
    });

    it('should handle validation failure', async () => {
      const validationError = {
        valid: false,
        errors: [{ property: 'fcid', message: 'is required' }],
      };
      (validator.validate as jest.Mock).mockReturnValue(validationError);

      const result = await processor.processWebhook(job);

      expect(result.success).toBe(false);
      expect(result.jobId).toBe(1);
      expect(result.error).toContain('Schema validation failed');
      expect(batchWebhookService.processWebhookJob).not.toHaveBeenCalled();
      expect(errorLogger.logError).toHaveBeenCalledWith(
        expect.any(Error),
        undefined,
        expect.objectContaining({
          errorName: 'Validation failed for webhook',
          context: 'WebhookProcessor',
          metadata: expect.objectContaining({ jobId: 1 }),
        }),
      );
    });

    it('should handle processing failure with a retryable error', async () => {
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });
      const retryableError = new Error('ECONNRESET');
      (batchWebhookService.processWebhookJob as jest.Mock).mockRejectedValue(retryableError);

      // Mock isRetryableError to return true for our test error
      jest.spyOn(processor as any, 'isRetryableError').mockReturnValue(true);

      try {
        await processor.processWebhook(job);
        fail('Expected processWebhook to throw an error');
      } catch (e) {
        expect(e).toEqual(retryableError);
        expect(batchWebhookService.processWebhookJob).toHaveBeenCalledWith(webhookData);
        expect(logger.warn).toHaveBeenCalledWith(
          `Retryable error processing webhook 1 (attempt 1/${batchConfig.retry.attempts}): ECONNRESET`,
        );
      }
    });

    it('should handle processing failure with a non-retryable error', async () => {
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });
      const nonRetryableError = new Error('Non-retryable error');
      (batchWebhookService.processWebhookJob as jest.Mock).mockRejectedValue(nonRetryableError);

      const result = await processor.processWebhook(job);

      expect(result.success).toBe(false);
      expect(result.jobId).toBe(1);
      expect(result.error).toBe('Non-retryable error');
      expect(result.eventId).toBe('1');
      expect(batchWebhookService.processWebhookJob).toHaveBeenCalledWith(webhookData);
      expect(job.moveToFailed).toHaveBeenCalledWith(nonRetryableError, true);
      expect(errorLogger.logError).toHaveBeenCalledWith(
        nonRetryableError,
        undefined,
        expect.objectContaining({
          errorName: 'Failed to process webhook job',
          context: 'WebhookProcessor',
          metadata: expect.objectContaining({ jobId: 1 }),
        }),
      );
    });

    it('should handle unexpected errors during processing', async () => {
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });
      const unexpectedError = new Error('Unexpected error');
      (batchWebhookService.processWebhookJob as jest.Mock).mockImplementation(() => {
        throw unexpectedError;
      });

      const result = await processor.processWebhook(job);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Unexpected error');
      expect(batchWebhookService.processWebhookJob).toHaveBeenCalledWith(webhookData);
      expect(errorLogger.logError).toHaveBeenCalledWith(
        unexpectedError,
        undefined,
        expect.objectContaining({
          errorName: 'Failed to process webhook job',
          context: 'WebhookProcessor',
          metadata: expect.objectContaining({ jobId: 1 }),
        }),
      );
    });
  });

  describe('validateWebhook', () => {
    let webhookData: Webhook;

    beforeEach(() => {
      webhookData = {
        id: 1,
        eventName: 'test_event',
        provider: 'FlipaClip',
        store: 'app_store',
        deviceId: 'test_device',
        eventTimestamp: new Date().toISOString(),
        payload: {},
        fcid: 'test_fcid',
        modified_properties: [],
        sessionId: 12345,
        user: {
          fcid: 'test-fcid',
          type: UserType.REGISTERED,
          event_control: {},
          identifiers: {},
          device_ids: [],
          properties: {},
          mergedFcids: [],
          created_at: new Date(),
          updated_at: new Date(),
          installed_at: new Date(),
          webhooks: [],
        },
      };
    });

    it('should pass validation for a valid webhook', async () => {
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });

      const result = await (processor as any).validateWebhook(webhookData);

      expect(result).toBeNull();
      expect(validator.validate).toHaveBeenCalledWith(
        {
          event_name: webhookData.eventName,
          event_control: {
            device_id: webhookData.deviceId,
            timestamp: Date.parse(webhookData.eventTimestamp),
          },
          provider: webhookData.provider,
          fcid: webhookData.fcid,
          store: webhookData.store,
          payload: webhookData.payload,
        },
        expect.anything(),
      );
    });

    it('should fail validation for schema errors', async () => {
      const validationError = {
        valid: false,
        errors: [{ property: 'fcid', message: 'is required' }],
      };
      (validator.validate as jest.Mock).mockReturnValue(validationError);

      const result = await (processor as any).validateWebhook(webhookData);

      expect(result).toContain('Schema validation failed');
      expect(logger.debug).toHaveBeenCalled();
    });

    it('should fail validation for invalid provider', async () => {
      webhookData.provider = 'InvalidProvider';
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });

      const result = await (processor as any).validateWebhook(webhookData);

      expect(result).toBe('Invalid webhook provider: InvalidProvider');
    });

    it('should fail validation for missing FCID in Purchasely webhook', async () => {
      webhookData.provider = 'purchasely';
      webhookData.fcid = '';
      (validator.validate as jest.Mock).mockReturnValue({ valid: true, errors: [] });

      const result = await (processor as any).validateWebhook(webhookData);

      expect(result).toBe('FCID is required for Purchasely webhooks');
    });

    it('should handle validation errors gracefully', async () => {
      (validator.validate as jest.Mock).mockImplementation(() => {
        throw new Error('Validation error');
      });

      const result = await (processor as any).validateWebhook(webhookData);

      expect(result).toBe('Validation error: Validation error');
      expect(errorLogger.logError).toHaveBeenCalledWith(
        expect.any(Error),
        undefined,
        expect.objectContaining({
          errorName: 'Validation error',
          context: 'WebhookProcessor',
          metadata: expect.any(Object),
        }),
      );
    });
  });

  describe('isRetryableError', () => {
    it.each([
      'ECONNRESET',
      'ETIMEDOUT',
      'ECONNREFUSED',
      'ENOTFOUND',
      'EAI_AGAIN',
      'ETIMEOUT',
      'ECONNABORTED',
      'ENETUNREACH',
      'EHOSTUNREACH',
      'deadlock',
      'timeout',
      'connection',
      'transaction',
      'lock',
    ])('should return true for retryable error: %s', error => {
      const retryableError = new Error(error);
      expect((processor as any).isRetryableError(retryableError)).toBe(true);
    });

    it('should return false for non-retryable error', () => {
      const nonRetryableError = new Error('Non-retryable error');
      expect((processor as any).isRetryableError(nonRetryableError)).toBe(false);
    });
  });

  describe('onQueueError', () => {
    it('should log queue errors', () => {
      const error = new Error('Queue error');
      processor.onError(error);
      expect(errorLogger.logError).toHaveBeenCalledWith(
        error,
        undefined,
        expect.objectContaining({
          errorName: 'Queue error',
          context: 'WebhookProcessor',
          metadata: expect.any(Object),
        }),
      );
    });
  });

  describe('onQueueFailed', () => {
    it('should log failed job details', () => {
      const job = { id: 1, attemptsMade: 2, opts: { attempts: 3 } } as Job;
      const error = new Error('Job failed');
      processor.onFailed(job, error);
      expect(errorLogger.logError).toHaveBeenCalledWith(
        error,
        undefined,
        expect.objectContaining({
          errorName: 'Job failed',
          context: 'WebhookProcessor',
          metadata: expect.objectContaining({ jobId: 1, attemptsMade: 2, attempts: 3 }),
        }),
      );
    });
  });
});
