import { Validator } from 'jsonschema';

import { abTestSchema } from '../../src/abtests/schemas/abtest.schema';

const JSON_SAMPLE = `
{
  "test_name":"PAYWALL",
  "enabled":true,
  "exclude_from_future":false,
  "variants":{
    "A":{"distribution":50, "exclude_from_future":false},
    "B":{"distribution":50, "exclude_from_future":false}
  },
  "conditions":{
    "activation_event":{
        "name":"Ad Shown",
          "event_properties":{
            "$revenue":{"$lte":0.1}
          },
        "is_new_user":false
    },
    "user_properties":{
      "Country":{"$in":["US","CA"]},
      "Total Ads Shown Count": 5,
      "Platform":"Android",
      "Days After Install":{"$gte":5}
    }
  }
}
`;

describe('Check AB test JSON schema', () => {
  let validator: Validator = new Validator();

  it('should validate documentation example', () => {
    const result = validator.validate(JSON.parse(JSON_SAMPLE), abTestSchema);
    expect(result.valid).toBe(true);
    expect(result.errors).toEqual([]);
  });
});
