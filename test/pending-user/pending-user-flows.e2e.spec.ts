import 'reflect-metadata';
import axios from 'axios';
import {
  TestSetup,
  generateUUID,
  generateAdid,
  createJwtWithPayload,
  simulate<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createMultipleJwts,
  delay,
} from '../test-utils';
import { config } from '../config';
import { UserType } from '../../src/users/user.dto';

describe('Pending User Service Flows E2E', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('Flow 2.a - Device ID Only (Pending User Creation)', () => {
    it('should create pending user in Redis for new device ID', async () => {
      const idfv = generateUUID();

      // This should create a PENDING user in Redis
      const jwtResponse = await createJwtWithPayload({ idfv });
      expect(jwtResponse.accessToken).toBeTruthy();

      // Subsequent calls with same device ID should find the pending user
      const secondJwtResponse = await createJwtWithPayload({ idfv });
      expect(secondJwtResponse.accessToken).toBeTruthy();

      // Both tokens should work for user creation
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);
      expect(userResponse.data.data.fcid).toBeTruthy();

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });

    it('should find existing ANONYMOUS user before creating pending user', async () => {
      const idfv = generateUUID();

      // First create an ANONYMOUS user
      const { user: existingUser } = await simulateUserJourney({ idfv });
      expect(existingUser.type).toBe(UserType.ANONYMOUS);

      // Now create JWT with same device ID - should find existing ANONYMOUS user
      const newJwtResponse = await createJwtWithPayload({ idfv });
      expect(newJwtResponse.accessToken).toBeTruthy();

      // Create another user with the new JWT - should get same FCID
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const newUserResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${newJwtResponse.accessToken}`,
        },
      });

      expect(newUserResponse.status).toBe(201);
      expect(newUserResponse.data.data.fcid).toBe(existingUser.fcid);
    });

    it('should handle Android device IDs (ADID)', async () => {
      const adid = generateAdid();

      const jwtResponse = await createJwtWithPayload({ adid });
      expect(jwtResponse.accessToken).toBeTruthy();

      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { adid: [adid] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });
  });

  describe('Flow 2.b - Device ID + FCID (Validation Flow)', () => {
    it('should validate FCID exists and device ID is associated', async () => {
      const idfv = generateUUID();

      // First create a user to get a valid FCID
      const { user: existingUser } = await simulateUserJourney({ idfv });
      const validFcid = existingUser.fcid;

      // Now create JWT with valid FCID and same device ID
      const jwtResponse = await createJwtWithPayload({
        idfv,
        fcid: validFcid,
      });
      expect(jwtResponse.accessToken).toBeTruthy();

      // Should be able to create user with this JWT
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);
      expect(userResponse.data.data.fcid).toBe(validFcid);
    });

    it('should fall back to Flow 2.a when FCID does not exist', async () => {
      const idfv = generateUUID();
      const nonExistentFcid = `fake-${generateUUID()}`;

      // Should fall back to Flow 2.a
      const jwtResponse = await createJwtWithPayload({
        idfv,
        fcid: nonExistentFcid,
      });
      expect(jwtResponse.accessToken).toBeTruthy();

      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);
      expect(userResponse.data.data.fcid).not.toBe(nonExistentFcid);

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });

    it('should fall back to Flow 2.a when device ID is not associated with FCID', async () => {
      const idfv1 = generateUUID();
      const idfv2 = generateUUID();

      // Create user with first device ID
      const { user: existingUser } = await simulateUserJourney({ idfv: idfv1 });
      const validFcid = existingUser.fcid;

      // Try to create JWT with valid FCID but different device ID
      const jwtResponse = await createJwtWithPayload({
        idfv: idfv2, // Different device ID
        fcid: validFcid,
      });
      expect(jwtResponse.accessToken).toBeTruthy();

      // Should create new user with different FCID
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv2] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);
      expect(userResponse.data.data.fcid).not.toBe(validFcid);

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });
  });

  describe('Pending User Lifecycle Management', () => {
    it('should transition from PENDING to ANONYMOUS when user is created', async () => {
      const idfv = generateUUID();

      // Step 1: Create JWT (creates PENDING user in Redis)
      const jwtResponse = await createJwtWithPayload({ idfv });
      expect(jwtResponse.accessToken).toBeTruthy();

      // Step 2: Create user (should transition PENDING to ANONYMOUS)
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);
      expect(userResponse.data.data.type).toBe(UserType.ANONYMOUS);

      // Step 3: Subsequent JWT creation should find ANONYMOUS user
      const secondJwtResponse = await createJwtWithPayload({ idfv });
      expect(secondJwtResponse.accessToken).toBeTruthy();

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });

    it('should handle cleanup of pending users after transition', async () => {
      const idfv = generateUUID();

      // Create multiple JWTs (creates pending user)
      const jwt1 = await createJwtWithPayload({ idfv });
      const jwt2 = await createJwtWithPayload({ idfv });

      expect(jwt1.accessToken).toBeTruthy();
      expect(jwt2.accessToken).toBeTruthy();

      // Create user (should clean up pending user from Redis)
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwt1.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);

      // Subsequent JWT creation should work with database user
      const jwt3 = await createJwtWithPayload({ idfv });
      expect(jwt3.accessToken).toBeTruthy();

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent JWT creation for same device ID', async () => {
      const idfv = generateUUID();
      const concurrentRequests = 5;

      // Create multiple concurrent JWT requests
      const promises = Array.from({ length: concurrentRequests }, () =>
        createJwtWithPayload({ idfv }),
      );

      const responses = await Promise.all(promises);

      // All should succeed
      responses.forEach(response => {
        expect(response.accessToken).toBeTruthy();
      });

      // Create user with one of the tokens
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${responses[0].accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });

    it('should handle concurrent JWT creation and user creation', async () => {
      const idfv = generateUUID();

      // Start JWT creation
      const jwtPromise = createJwtWithPayload({ idfv });

      // Small delay then start another JWT creation
      await delay(50);
      const secondJwtPromise = createJwtWithPayload({ idfv });

      const [jwt1, jwt2] = await Promise.all([jwtPromise, secondJwtPromise]);

      expect(jwt1.accessToken).toBeTruthy();
      expect(jwt2.accessToken).toBeTruthy();

      // Create user with first token
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwt1.accessToken}`,
        },
      });

      expect(userResponse.status).toBe(201);

      if (userResponse.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse.data.data.fcid);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle Redis fallback when Redis is unavailable', async () => {
      // This test would require mocking Redis failure, which is difficult in E2E
      // For now, we'll test that the system continues to work under normal conditions
      const idfv = generateUUID();

      const jwtResponse = await createJwtWithPayload({ idfv });
      expect(jwtResponse.accessToken).toBeTruthy();

      // Should still be able to create user
      const { user } = await simulateUserJourney({ idfv });
      expect(user.fcid).toBeTruthy();
    });

    it('should handle invalid device ID formats gracefully', async () => {
      const invalidIdfv = 'not-a-valid-uuid';

      await expect(createJwtWithPayload({ idfv: invalidIdfv })).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });

    it('should handle empty device IDs', async () => {
      await expect(createJwtWithPayload({ idfv: '' })).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });

    it('should handle very long device IDs', async () => {
      const longDeviceId = 'a'.repeat(1000);

      await expect(createJwtWithPayload({ idfv: longDeviceId })).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle multiple unique device IDs efficiently', async () => {
      const deviceCount = 10;

      const responses = await createMultipleJwts(deviceCount, () => ({ idfv: generateUUID() }));

      expect(responses).toHaveLength(deviceCount);
      responses.forEach(response => {
        expect(response.accessToken).toBeTruthy();
        expect(response.deviceId).toBeTruthy();
      });
    });

    it('should handle rapid sequential requests for same device', async () => {
      const idfv = generateUUID();
      const requestCount = 5;

      const startTime = Date.now();

      for (let i = 0; i < requestCount; i++) {
        const response = await createJwtWithPayload({ idfv });
        expect(response.accessToken).toBeTruthy();

        // Small delay to avoid overwhelming
        await delay(100);
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete within reasonable time
      expect(totalTime).toBeLessThan(10000); // 10 seconds
    });

    it('should handle mixed device types efficiently', async () => {
      const mixedRequests = [
        { idfv: generateUUID() },
        { adid: generateAdid() },
        { idfv: generateUUID() },
        { adid: generateAdid() },
        { idfv: generateUUID() },
      ];

      const promises = mixedRequests.map(deviceData => createJwtWithPayload(deviceData));

      const responses = await Promise.all(promises);

      responses.forEach(response => {
        expect(response.accessToken).toBeTruthy();
      });
    });
  });

  describe('Data Consistency', () => {
    it('should maintain FCID consistency across multiple operations', async () => {
      const idfv = generateUUID();

      // Create JWT multiple times
      const jwt1 = await createJwtWithPayload({ idfv });
      const jwt2 = await createJwtWithPayload({ idfv });

      // Create users with different tokens
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const user1Response = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwt1.accessToken}`,
        },
      });

      const user2Response = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwt2.accessToken}`,
        },
      });

      expect(user1Response.status).toBe(201);
      expect(user2Response.status).toBe(201);
      expect(user1Response.data.data.fcid).toBe(user2Response.data.data.fcid);

      if (user1Response.data.data.fcid) {
        testSetup.trackCreatedUser(user1Response.data.data.fcid);
      }
    });

    it('should handle device ID case sensitivity correctly', async () => {
      const baseIdfv = generateUUID();
      const upperIdfv = baseIdfv.toUpperCase();
      const lowerIdfv = baseIdfv.toLowerCase();

      // UUIDs should be case-insensitive, but let's test with the exact format
      const response1 = await createJwtWithPayload({ idfv: lowerIdfv });
      const response2 = await createJwtWithPayload({ idfv: upperIdfv });

      expect(response1.accessToken).toBeTruthy();
      expect(response2.accessToken).toBeTruthy();

      // Both should work for user creation
      const userData1 = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [lowerIdfv] },
        installed_at: new Date(),
      };

      const userData2 = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [upperIdfv] },
        installed_at: new Date(),
      };

      const userResponse1 = await axios.post(`${config.roshiUrl}/users`, userData1, {
        headers: {
          Authorization: `Bearer ${response1.accessToken}`,
        },
      });

      const userResponse2 = await axios.post(`${config.roshiUrl}/users`, userData2, {
        headers: {
          Authorization: `Bearer ${response2.accessToken}`,
        },
      });

      expect(userResponse1.status).toBe(201);
      expect(userResponse2.status).toBe(201);

      if (userResponse1.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse1.data.data.fcid);
      }
      if (userResponse2.data.data.fcid) {
        testSetup.trackCreatedUser(userResponse2.data.data.fcid);
      }
    });
  });
});
