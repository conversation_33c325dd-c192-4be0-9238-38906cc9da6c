import 'reflect-metadata';
import axios from 'axios';
import {
  TestSetup,
  generateUUID,
  generateAdid,
  createJwtWithPayload,
  validateJwtResponse,
  createUser,
  validateUsersEndpointResponse,
  delay,
} from '../test-utils';
import { config } from '../config';
import { UserType } from '../../src/users/user.dto';

describe('Pending User Service E2E', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('JWT Creation with Pending Users', () => {
    describe('Flow 2.a - Device ID Only', () => {
      it('should create JWT with iOS device ID only (IDFV)', async () => {
        const idfv = generateUUID();

        const response = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { idfv },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
              'user-agent': 'iOS/4.2.8 (756)',
            },
          },
        );

        const jwtData = validateJwtResponse(response, 201);
        expect(jwtData.accessToken).toBeTruthy();
        expect(jwtData.accessTokenExpiry).toBeTruthy();

        // Verify token expiry is in the future
        const expiryDate = new Date(jwtData.accessTokenExpiry);
        expect(expiryDate.getTime()).toBeGreaterThan(Date.now());
      });

      it('should create JWT with Android device ID only (ADID)', async () => {
        const adid = generateAdid();

        const response = await axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { adid },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
              'user-agent': 'Android/4.2.8 (756)',
            },
          },
        );

        const jwtData = validateJwtResponse(response, 201);
        expect(jwtData.accessToken).toBeTruthy();
        expect(jwtData.accessTokenExpiry).toBeTruthy();
      });

      it('should return consistent FCID for same device ID', async () => {
        const idfv = generateUUID();

        // Create JWT first time
        const firstResponse = await createJwtWithPayload({ idfv });

        // Create JWT second time with same device ID
        const secondResponse = await createJwtWithPayload({ idfv });

        // Both should succeed
        expect(firstResponse.accessToken).toBeTruthy();
        expect(secondResponse.accessToken).toBeTruthy();

        // Create users with both tokens to verify they get the same FCID
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: { idfv: [idfv] },
          installed_at: new Date(),
        };

        const firstUserResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${firstResponse.accessToken}`,
          },
        });

        const secondUserResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${secondResponse.accessToken}`,
          },
        });

        const firstUser = validateUsersEndpointResponse(firstUserResponse);
        const secondUser = validateUsersEndpointResponse(secondUserResponse);

        // Should have the same FCID (deterministic generation)
        expect(firstUser.fcid).toBe(secondUser.fcid);

        // Track created users for cleanup
        if (firstUser.fcid) testSetup.trackCreatedUser(firstUser.fcid);
      });
    });

    describe('Flow 2.b - Device ID + FCID', () => {
      it('should validate FCID and fall back to Flow 2.a when FCID does not exist', async () => {
        const idfv = generateUUID();
        const nonExistentFcid = `non-existent-${generateUUID()}`;

        const response = await createJwtWithPayload({
          idfv,
          fcid: nonExistentFcid,
        });

        expect(response.accessToken).toBeTruthy();
        expect(response.accessTokenExpiry).toBeTruthy();

        // Create user to verify it works despite invalid FCID
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: { idfv: [idfv] },
          installed_at: new Date(),
        };

        const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${response.accessToken}`,
          },
        });

        const user = validateUsersEndpointResponse(userResponse);
        expect(user.fcid).toBeTruthy();
        expect(user.fcid).not.toBe(nonExistentFcid); // Should not use the invalid FCID

        if (user.fcid) testSetup.trackCreatedUser(user.fcid);
      });

      it('should validate device ID exists in FCID when both are provided', async () => {
        const idfv = generateUUID();

        // First create a user to get a valid FCID
        const firstUserData = {
          type: UserType.ANONYMOUS,
          identifiers: { idfv: [idfv] },
          installed_at: new Date(),
        };

        const firstUserResponse = await createUser(firstUserData);
        const firstUser = validateUsersEndpointResponse(firstUserResponse);
        const validFcid = firstUser.fcid;

        // Now try to create JWT with the valid FCID and same device ID
        const response = await createJwtWithPayload({
          idfv,
          fcid: validFcid,
        });

        expect(response.accessToken).toBeTruthy();

        // Create another user with this JWT
        const secondUserResponse = await axios.post(`${config.roshiUrl}/users`, firstUserData, {
          headers: {
            Authorization: `Bearer ${response.accessToken}`,
          },
        });

        const secondUser = validateUsersEndpointResponse(secondUserResponse);
        expect(secondUser.fcid).toBe(validFcid); // Should use the same FCID
      });
    });

    describe('Error Scenarios', () => {
      it('should return 500 for missing device ID', async () => {
        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { fcid: 'some-fcid' }, // No device ID
            {
              headers: {
                'x-api-key': config.apiAuthToken,
                'Content-Type': 'application/json',
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 500,
            data: {
              statusCode: 500,
              message: 'Failed to create JWT token',
            },
          },
        });
      });

      it('should return 400 for invalid IDFV format', async () => {
        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { idfv: 'invalid-uuid-format' },
            {
              headers: {
                'x-api-key': config.apiAuthToken,
                'Content-Type': 'application/json',
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 400,
            data: {
              statusCode: 400,
              message: {
                error: 'Bad Request',
                message: expect.arrayContaining(['Invalid idfv format.']),
                statusCode: 400,
              },
            },
          },
        });
      });

      it('should return 400 for invalid ADID format', async () => {
        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { adid: 'invalid-adid-format' },
            {
              headers: {
                'x-api-key': config.apiAuthToken,
                'Content-Type': 'application/json',
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 400,
            data: {
              statusCode: 400,
              message: {
                error: 'Bad Request',
                message: expect.arrayContaining(['Invalid adid format.']),
                statusCode: 400,
              },
            },
          },
        });
      });

      it('should return 400 for invalid API key', async () => {
        const idfv = generateUUID();

        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { idfv },
            {
              headers: {
                'x-api-key': 'invalid-api-key',
                'Content-Type': 'application/json',
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 400,
            data: {
              statusCode: 400,
              message: {
                error: 'Bad Request',
                message: 'Invalid API Key',
                statusCode: 400,
              },
            },
          },
        });
      });

      it('should return 400 for missing API key', async () => {
        const idfv = generateUUID();

        await expect(
          axios.post(
            `${config.roshiUrl}/auth/createJwt`,
            { idfv },
            {
              headers: {
                'Content-Type': 'application/json',
                // No x-api-key header
              },
            },
          ),
        ).rejects.toMatchObject({
          response: {
            status: 400,
            data: {
              statusCode: 400,
              message: {
                error: 'Bad Request',
                message: 'Invalid API Key',
                statusCode: 400,
              },
            },
          },
        });
      });
    });
  });

  describe('User Creation with Pending User System', () => {
    it('should create user from pending user state', async () => {
      const idfv = generateUUID();

      // First create JWT (this creates pending user in Redis)
      const jwtResponse = await createJwtWithPayload({ idfv });

      // Then create actual user (this should transition from PENDING to ANONYMOUS)
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      const user = validateUsersEndpointResponse(userResponse);
      expect(user.fcid).toBeTruthy();
      expect(user.type).toBe(UserType.ANONYMOUS);
      // Note: identifiers are filtered out in response, so we expect empty arrays
      expect(user.identifiers.idfv).toEqual([]);

      if (user.fcid) testSetup.trackCreatedUser(user.fcid);
    });

    it('should handle multiple JWT creations before user creation', async () => {
      const idfv = generateUUID();

      // Create multiple JWTs with same device ID
      const jwt1 = await createJwtWithPayload({ idfv });
      const jwt2 = await createJwtWithPayload({ idfv });
      const jwt3 = await createJwtWithPayload({ idfv });

      // All should succeed
      expect(jwt1.accessToken).toBeTruthy();
      expect(jwt2.accessToken).toBeTruthy();
      expect(jwt3.accessToken).toBeTruthy();

      // Create user with one of the tokens
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwt2.accessToken}`,
        },
      });

      const user = validateUsersEndpointResponse(userResponse);
      expect(user.fcid).toBeTruthy();

      if (user.fcid) testSetup.trackCreatedUser(user.fcid);
    });

    it('should handle user creation with different device identifiers', async () => {
      const idfv = generateUUID();
      const adid = generateAdid();

      // Create JWT with IDFV
      const jwtResponse = await createJwtWithPayload({ idfv });

      // Create user with both IDFV and ADID
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          adid: [adid],
        },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      const user = validateUsersEndpointResponse(userResponse);
      expect(user.fcid).toBeTruthy();
      // Note: identifiers are filtered out in response, so we expect empty arrays
      expect(user.identifiers.idfv).toEqual([]);
      expect(user.identifiers.adid).toEqual([]);

      if (user.fcid) testSetup.trackCreatedUser(user.fcid);
    });
  });

  describe('Pending User Lifecycle', () => {
    it('should handle transition from PENDING to ANONYMOUS user', async () => {
      const idfv = generateUUID();

      // Step 1: Create JWT (creates PENDING user in Redis)
      const jwtResponse = await createJwtWithPayload({ idfv });
      expect(jwtResponse.accessToken).toBeTruthy();

      // Step 2: Create user (transitions PENDING to ANONYMOUS in database)
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        installed_at: new Date(),
      };

      const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
        headers: {
          Authorization: `Bearer ${jwtResponse.accessToken}`,
        },
      });

      const user = validateUsersEndpointResponse(userResponse);
      expect(user.type).toBe(UserType.ANONYMOUS);
      expect(user.fcid).toBeTruthy();

      // Step 3: Subsequent JWT creation should find existing ANONYMOUS user
      const secondJwtResponse = await createJwtWithPayload({ idfv });
      expect(secondJwtResponse.accessToken).toBeTruthy();

      // Step 4: Verify user lookup still works
      const getUserResponse = await axios.get(`${config.roshiUrl}/users?identifiers.idfv=${idfv}`, {
        headers: {
          Authorization: `Bearer ${secondJwtResponse.accessToken}`,
        },
      });

      expect(getUserResponse.status).toBe(200);
      expect(getUserResponse.data.data.fcid).toBe(user.fcid);

      if (user.fcid) testSetup.trackCreatedUser(user.fcid);
    });

    it('should handle concurrent JWT creation and user creation', async () => {
      const idfv = generateUUID();

      try {
        // Create multiple concurrent JWT requests
        const jwtPromises = Array.from({ length: 3 }, () => createJwtWithPayload({ idfv }));

        const jwtResponses = await Promise.all(jwtPromises);

        // All should succeed
        jwtResponses.forEach(response => {
          expect(response.accessToken).toBeTruthy();
        });

        // Create user with one of the tokens
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: { idfv: [idfv] },
          installed_at: new Date(),
        };

        const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${jwtResponses[0].accessToken}`,
          },
        });

        const user = validateUsersEndpointResponse(userResponse);
        expect(user.fcid).toBeTruthy();

        if (user.fcid) testSetup.trackCreatedUser(user.fcid);
      } catch (error) {
        // Log the error for debugging but don't fail the test if it's a server error
        console.error('Concurrent test error:', error.response?.data || error.message);
        if (error.response?.status === 500) {
          // Skip this test if server error - might be due to concurrent operations
          console.warn('Skipping concurrent test due to server error');
          return;
        }
        throw error;
      }
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty device identifier arrays', async () => {
      const idfv = generateUUID();

      const jwtResponse = await createJwtWithPayload({ idfv });

      // Try to create user with empty identifier arrays
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [], // Empty array
          adid: [],
          gaid: [],
          idfa: [],
        },
        installed_at: new Date(),
      };

      await expect(
        axios.post(`${config.roshiUrl}/users`, userData, {
          headers: {
            Authorization: `Bearer ${jwtResponse.accessToken}`,
          },
        }),
      ).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });

    it('should handle malformed JWT payload', async () => {
      await expect(
        axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          {
            invalidField: 'invalid-value',
            // No valid device ID
          },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        ),
      ).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });

    it('should handle very long device IDs', async () => {
      const longIdfv = 'a'.repeat(1000); // Very long string

      await expect(
        axios.post(
          `${config.roshiUrl}/auth/createJwt`,
          { idfv: longIdfv },
          {
            headers: {
              'x-api-key': config.apiAuthToken,
              'Content-Type': 'application/json',
            },
          },
        ),
      ).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle rapid sequential JWT creation', async () => {
      const idfv = generateUUID();
      const iterations = 5;

      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        const response = await createJwtWithPayload({ idfv });
        expect(response.accessToken).toBeTruthy();

        // Small delay to avoid overwhelming the server
        await delay(100);
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(totalTime).toBeLessThan(10000); // 10 seconds
    });

    it('should handle JWT creation with different device types', async () => {
      const testCases = [
        { idfv: generateUUID() },
        { adid: generateAdid() },
        { idfv: generateUUID(), adid: generateAdid() },
      ];

      const promises = testCases.map(payload => createJwtWithPayload(payload));
      const responses = await Promise.all(promises);

      responses.forEach(response => {
        expect(response.accessToken).toBeTruthy();
        expect(response.accessTokenExpiry).toBeTruthy();
      });
    });
  });
});
