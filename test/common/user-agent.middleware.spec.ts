import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException } from '@nestjs/common';
import { UserAgentMiddleware } from '../../src/common/middleware/user-agent.middleware';
import { Request, Response } from 'express';
import { ParsedUserAgent } from '../../src/common/utils/user-agent.utils';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';

describe('UserAgentMiddleware', () => {
  let middleware: UserAgentMiddleware;
  let mockRequest: Partial<Request> & { userAgentInfo?: ParsedUserAgent };
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        UserAgentMiddleware,
        {
          provide: ErrorLoggerService,
          useValue: {
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    middleware = module.get<UserAgentMiddleware>(UserAgentMiddleware);

    mockRequest = {};
    mockResponse = {};
    mockNext = jest.fn();
  });

  describe('use', () => {
    it('should allow iOS user agent with version', () => {
      mockRequest.headers = { 'user-agent': 'iOS/4.2.15 (779)' };

      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.userAgentInfo).toBeDefined();
      expect(mockRequest.userAgentInfo!.family).toBe('iOS');
    });

    it('should allow iOS user agent with different case', () => {
      mockRequest.headers = { 'user-agent': 'ios/4.2.15 (779)' };

      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.userAgentInfo).toBeDefined();
      expect(mockRequest.userAgentInfo!.family).toBe('iOS');
    });

    it('should allow Android user agent with version', () => {
      mockRequest.headers = { 'user-agent': 'Android/7.2.3 (757)' };

      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.userAgentInfo).toBeDefined();
      expect(mockRequest.userAgentInfo!.family).toBe('Android');
    });

    it('should allow Vegeta user agent', () => {
      mockRequest.headers = { 'user-agent': 'Vegeta' };

      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.userAgentInfo).toBeDefined();
      expect(mockRequest.userAgentInfo!.family).toBe('Vegeta');
    });

    it('should allow user agent with extra whitespace', () => {
      mockRequest.headers = { 'user-agent': '  iOS/4.2.15 (779)  ' };

      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.userAgentInfo).toBeDefined();
      expect(mockRequest.userAgentInfo!.family).toBe('iOS');
    });

    it('should allow user agent with mixed case', () => {
      mockRequest.headers = { 'user-agent': 'iOs/4.2.15 (779)' };

      middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockRequest.userAgentInfo).toBeDefined();
      expect(mockRequest.userAgentInfo!.family).toBe('iOS');
    });

    it('should reject unknown user agent', () => {
      mockRequest.headers = { 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)' };

      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).toThrow(ForbiddenException);

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject empty user agent', () => {
      mockRequest.headers = { 'user-agent': '' };

      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).toThrow(ForbiddenException);

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject missing user agent header', () => {
      mockRequest.headers = {};

      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).toThrow(ForbiddenException);

      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle user agent that contains allowed substring but is not actually valid', () => {
      mockRequest.headers = { 'user-agent': 'MyCustomiOSApp/1.0' };
      // const errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);

      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).toThrow(ForbiddenException);
      // expect(errorLogger.logError).toHaveBeenCalledWith(
      //   expect.any(Error),
      //   mockRequest,
      //   expect.objectContaining({
      //     errorName: 'Forbidden user agent',
      //     context: 'UserAgentMiddleware',
      //     includeStack: true,
      //     includeRequest: true,
      //     metadata: { userAgent: 'MyCustomiOSApp/1.0' },
      //   }),
      // );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should log error when iOS user agent is forbidden', () => {
      mockRequest.headers = { 'user-agent': 'iOSInvalidFormat' };
      // const errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);

      expect(() => {
        middleware.use(mockRequest as Request, mockResponse as Response, mockNext);
      }).toThrow(ForbiddenException);
      // expect(errorLogger.logError).toHaveBeenCalledWith(
      //   expect.any(Error),
      //   mockRequest,
      //   expect.objectContaining({
      //     errorName: 'Forbidden user agent',
      //     context: 'UserAgentMiddleware',
      //     includeStack: true,
      //     includeRequest: true,
      //     metadata: { userAgent: 'iOSInvalidFormat' },
      //   }),
      // );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
