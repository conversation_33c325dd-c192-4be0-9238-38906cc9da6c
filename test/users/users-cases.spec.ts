import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  generateUUID,
  generateAdid,
} from '../test-utils';

describe('User Cases', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('Anonymous User Identifier Merging', () => {
    it('should merge identifiers when updating existing anonymous user', async () => {
      // First create an anonymous user
      const idfv = generateUUID();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [idfv] },
        properties: {
          totalAdRevenue: 0,
          totalSubscriptionRevenue: 0,
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Then update the user with new identifiers
      const newIdfv = generateUUID();
      const updateData = {
        fcid: createdUser.fcid,
        type: UserType.ANONYMOUS,
        identifiers: { idfv: [newIdfv] },
        properties: {
          totalAdRevenue: 50,
          totalSubscriptionRevenue: 0,
        },
        installed_at: new Date(),
      };

      const updateResponse = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(updateResponse);
      // expect(updatedUser.identifiers.idfv).toEqual([idfv, newIdfv]);
      expect(updatedUser.identifiers.idfv).toEqual([]);
      expect(updatedUser.properties).toBeDefined();
      expect(updatedUser.properties?.totalAdRevenue).toBe(50);
    });
  });

  describe('Revenue Calculations', () => {
    it('should calculate total revenue from ad and subscription revenue', async () => {
      // First create a registered user with initial revenue
      const userData = {
        fcaid: 'test-fcaid-' + generateUUID(),
        type: UserType.REGISTERED,
        identifiers: {
          adid: [generateAdid()],
          idfv: [generateUUID()],
        },
        properties: {
          totalAdRevenue: 100,
          totalSubscriptionRevenue: 50,
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Then update with additional revenue
      const updateData = {
        fcid: createdUser.fcid,
        fcaid: createdUser.fcaid,
        type: UserType.REGISTERED,
        identifiers: {
          adid: [generateAdid()],
          idfv: [generateUUID()],
        },
        properties: {
          totalAdRevenue: 50,
          totalSubscriptionRevenue: 25,
        },
        installed_at: new Date(),
      };

      const updateResponse = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(updateResponse);
      expect(updatedUser.properties).toBeDefined();
      expect(updatedUser.properties?.totalAdRevenue).toBe(150);
      expect(updatedUser.properties?.totalSubscriptionRevenue).toBe(75);
    });

    it('should handle missing revenue properties', async () => {
      // First create a registered user with initial revenue
      const userData = {
        fcaid: 'test-fcaid-' + generateUUID(),
        type: UserType.REGISTERED,
        identifiers: {
          adid: [generateAdid()],
          idfv: [generateUUID()],
        },
        properties: {
          totalAdRevenue: 100,
          totalSubscriptionRevenue: 0,
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Then update with only subscription revenue
      const updateData = {
        fcid: createdUser.fcid,
        fcaid: createdUser.fcaid,
        type: UserType.REGISTERED,
        identifiers: {
          adid: [generateAdid()],
          idfv: [generateUUID()],
        },
        properties: {
          totalAdRevenue: 0,
          totalSubscriptionRevenue: 50,
        },
        installed_at: new Date(),
      };

      const updateResponse = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(updateResponse);
      expect(updatedUser.properties).toBeDefined();
      expect(updatedUser.properties?.totalAdRevenue).toBe(100);
      expect(updatedUser.properties?.totalSubscriptionRevenue).toBe(50);
    });
  });
});
