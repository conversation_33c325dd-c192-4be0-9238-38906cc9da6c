import { AdvertisingRegex } from '../../src/users/user.dto';

describe('AdvertisingRegex', () => {
  it('matches dummy ID values', () => {
    const dummy = '00000000-0000-0000-0000-000000000000';
    expect(AdvertisingRegex.test(dummy)).toBe(true);
  });

  it('matches valid UUID values', () => {
    const uuid = '123e4567-e89b-12d3-a456-************';
    expect(AdvertisingRegex.test(uuid)).toBe(true);
  });

  it('matches specific [0+-]+ dummy values', () => {
    const validDummyStrings = ['0', '000', '+', '-', '0+-0'];
    validDummyStrings.forEach(dummy => {
      expect(AdvertisingRegex.test(dummy)).toBe(true);
    });

    const invalidRelatedToDummy = ['0a', 'a0', '0+a', '+ '];
    invalidRelatedToDummy.forEach(invalid => {
      expect(AdvertisingRegex.test(invalid)).toBe(false);
    });
  });

  it('does not match invalid or partially valid strings', () => {
    const invalidStrings = [
      'not-a-valid-id',
      'garbage123e4567-e89b-12d3-a456-************', // UUID with prefix
      '123e4567-e89b-12d3-a456-************garbage', // UUID with suffix
      '000garbage', // [0+-]+ pattern with suffix
      'garbage000', // [0+-]+ pattern with prefix
      '', // Empty string
      '123e4567-e89b-12d3-a456-************0', // Malformed UUID (extra char in last segment)
      '123e456-e89b-12d3-a456-************', // Malformed UUID (segment too short)
      '00000000-0000-0000-0000-00000000000g', // Malformed UUID (invalid char in last segment)
    ];
    invalidStrings.forEach(invalid => {
      expect(AdvertisingRegex.test(invalid)).toBe(false);
    });
  });
});
