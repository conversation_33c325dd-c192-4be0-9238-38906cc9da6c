import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { RedisService } from '../../src/common/services/redis.service';
import { testConfig } from '../../src/config/environments/test.config';
import { GeolocationService } from '../../src/geolocation/geolocation.service';
import { UsersService } from '../../src/users/users.service';
import { UserType } from '../../src/users/user.dto';
import { EventsService } from '../../src/webhooks/events.service';
import { Webhook } from '../../src/webhooks/entities/webhook.entity';
import { PendingUserService } from '../../src/pending-user/application/services/pending-user.service';
import { generateUUID } from '../test-utils';

import type { User } from '../../src/users/entities/user.entity';

describe('Endpoint /users: Queue Geolocation Scenarios', () => {
  const requestProvider = { get: jest.fn() };

  let userService: UsersService;
  let pgUserService: PostgresUserService;
  let geolocationService: GeolocationService;
  let pendingUserService: PendingUserService;
  const compileTestingModule = async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PostgresUserService,
          useValue: {
            createUser: jest.fn(),
            findByAttribute: jest.fn(),
          },
        },
        EventsService,
        {
          provide: ConfigService,
          useValue: {
            get: (k: string) => testConfig[k as keyof typeof testConfig],
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            logError: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Webhook),
          useValue: {},
        },
        {
          provide: GeolocationService,
          useValue: {
            queueGeolocation: jest.fn(),
          },
        },
        {
          provide: PendingUserService,
          useValue: {
            findOrCreateUserForJwt: jest.fn().mockResolvedValue(mockUser),
          },
        },
        {
          provide: REQUEST,
          useFactory: () => requestProvider.get(),
        },
        {
          provide: RedisService,
          useValue: {
            acquireLock: jest.fn(),
            releaseLock: jest.fn(),
          },
        },
      ],
    }).compile();
    userService = module.get<UsersService>(UsersService);
    geolocationService = module.get<GeolocationService>(GeolocationService);
    pgUserService = module.get<PostgresUserService>(PostgresUserService);
    pendingUserService = module.get<PendingUserService>(PendingUserService);
  };

  const now = new Date();
  const mockUser: Partial<User> = {
    fcid: 'test-fcid',
    type: UserType.REGISTERED,
    event_control: {},
    identifiers: {},
    device_ids: [],
    properties: {},
    mergedFcids: [],
    created_at: now,
    updated_at: now,
    installed_at: now,
    webhooks: [],
  };

  it('should queue a new geolocation for a created user', async () => {
    jest.spyOn(requestProvider, 'get').mockReturnValue({
      headers: {
        'x-forwarded-for': '***********',
      },
      socket: {
        remoteAddress: '********',
      },
    });
    await compileTestingModule();
    jest.spyOn(pgUserService, 'createUser').mockResolvedValue(mockUser as User);
    const userData = {
      type: UserType.ANONYMOUS,
      identifiers: {
        idfv: [generateUUID()],
      },
      installed_at: new Date(),
    };
    await userService.createOrUpdateUser(userData);
    expect(geolocationService.queueGeolocation).toHaveBeenCalledWith('test-fcid', '***********');
  });

  it('should not queue a new geolocation when IP is empty', async () => {
    jest.spyOn(requestProvider, 'get').mockReturnValue({
      headers: {},
      socket: {},
    });
    await compileTestingModule();
    jest.spyOn(pgUserService, 'createUser').mockResolvedValue(mockUser as User);
    const userData = {
      type: UserType.ANONYMOUS,
      identifiers: {
        idfv: [generateUUID()],
      },
      installed_at: new Date(),
    };
    await userService.createOrUpdateUser(userData);
    expect(geolocationService.queueGeolocation).not.toHaveBeenCalled();
  });
});
