import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  delay,
  generateAdid,
  generateUUID,
  createJwtForIdentifier,
} from '../test-utils';
import axios from 'axios';
import { config } from '../config';
import exp from 'constants';

describe('UsersService - Device Identifier Updates (API)', () => {
  let testSetup: TestSetup;

  // Define IDs outside for using them later
  const firstAdid = generateAdid();
  const secondAdid = generateAdid();
  const existingIdfv = generateUUID();

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  beforeEach(async () => {
    // Add longer delay before each test to avoid rate limiting
    await delay(10000);
  });

  describe('Anonymous User - Advertising ID Updates', () => {
    it('should successfully update advertising ID for anonymous user', async () => {
      const firstAdid = generateAdid();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          adid: [firstAdid],
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const existingUser = validateUsersEndpointResponse(createResponse);

      await delay(3000);

      const secondAdid = generateAdid();
      const updateData = {
        fcid: existingUser.fcid,
        identifiers: {
          adid: [secondAdid],
        },
        installed_at: new Date(),
      };

      const result = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(result);
      // expect(updatedUser.identifiers?.adid).toEqual([firstAdid, secondAdid]);
      expect(updatedUser.identifiers?.adid).toEqual([]);
      expect(updatedUser.type).toBe(UserType.ANONYMOUS);
    });
  });

  describe('Anonymous User - Vendor ID Updates', () => {
    it('should successfully add new vendor ID for anonymous user', async () => {
      const oldIdfv = generateUUID();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [oldIdfv],
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const existingUser = validateUsersEndpointResponse(createResponse);

      await delay(1000);

      const newIdfv = generateUUID();
      const updateData = {
        fcid: existingUser.fcid,
        identifiers: {
          idfv: [newIdfv],
        },
        installed_at: new Date(),
      };

      const result = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(result);
      // expect(updatedUser.identifiers?.idfv).toEqual([oldIdfv, newIdfv]);
      expect(updatedUser.identifiers?.idfv).toEqual([]);
      expect(updatedUser.type).toBe(UserType.ANONYMOUS);
    });

    it('should update existing user when idfv collides', async () => {
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [existingIdfv],
          adid: [firstAdid],
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const conflictingUser = validateUsersEndpointResponse(createResponse);

      await delay(3000);

      const updateData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [existingIdfv],
          adid: [secondAdid],
        },
        installed_at: new Date(),
      };

      const result = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(result);
      expect(updatedUser.fcid).toBe(conflictingUser.fcid);
      // expect(updatedUser.identifiers.idfv).toEqual([existingIdfv]);
      expect(updatedUser.identifiers.idfv).toEqual([]);
      // expect(updatedUser.identifiers.adid).toEqual([firstAdid, secondAdid]);
      expect(updatedUser.identifiers.adid).toEqual([]);
      expect(updatedUser.type).toBe(UserType.ANONYMOUS);
    });
  });

  describe('Registered User - Identifier Updates', () => {
    it('should return new anonymous if fcaid is missing for registered user update', async () => {
      // First create a registered user
      const initialUserData = {
        type: UserType.REGISTERED,
        fcaid: 'test-fcaid-123',
        identifiers: {
          adid: [generateAdid()],
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(initialUserData);
      const existingUser = validateUsersEndpointResponse(createResponse);

      await delay(1000);

      // Now try to update without fcaid
      const updateData = {
        fcid: existingUser.fcid,
        type: UserType.REGISTERED,
        identifiers: {
          adid: [generateAdid()],
        },
        installed_at: new Date(),
      };

      const newAnonResponse = await createUser(updateData);
      const newAnon = validateUsersEndpointResponse(newAnonResponse);

      expect(newAnon.type).toBe(UserType.ANONYMOUS);
      expect(newAnon.fcaid).toBeUndefined();
      expect(newAnon.fcid === existingUser.fcid).toBe(false);
    });

    it('should return new anonymous when trying to update a registered user to anonymous', async () => {
      // First create a registered user
      const initialUserData = {
        type: UserType.REGISTERED,
        fcaid: 'test-fcaid-123',
        identifiers: {
          adid: [generateAdid()],
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(initialUserData);
      const existingUser = validateUsersEndpointResponse(createResponse);

      await delay(1000);

      // Now try to update to anonymous
      const updateData = {
        fcid: existingUser.fcid,
        type: UserType.ANONYMOUS,
        identifiers: {
          adid: [generateAdid()],
        },
        installed_at: new Date(),
      };

      const newAnonResponse = await createUser(updateData);
      const newAnon = validateUsersEndpointResponse(newAnonResponse);

      expect(newAnon.type).toBe(UserType.ANONYMOUS);
      expect(newAnon.fcaid).toBeUndefined();
      expect(newAnon.fcid === existingUser.fcid).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should return 400 Bad Request when creating a user with empty identifiers', async () => {
      const inputData = {
        type: UserType.ANONYMOUS,
        identifiers: {},
        installed_at: new Date(),
      };
      try {
        const token = await createJwtForIdentifier('idfv', generateUUID());
        const headers = { Authorization: `Bearer ${token}` };
        await axios.post(`${config.roshiUrl}/users`, inputData, { headers });
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.statusText).toBe('Bad Request');
        expect(error.response.data.message.message).toContain(
          'At least one valid identifier must be provided',
        );
      }
    });
  });
});
