import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  generateAdid,
  generateUUID,
  generateFcaid,
} from '../test-utils';

describe('User Merge Deduplication (Android Flow)', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  it('should follow the Android merge flow and handle deduplication and errors', async () => {
    // Step 1: Create new anonymous Android user
    const adid = generateAdid();
    const gaid1 = generateUUID();
    const anonUserData = {
      type: UserType.ANONYMOUS,
      identifiers: {
        gaid: [gaid1],
        adid: [adid],
      },
      isFreshInstall: true,
      installed_at: new Date(),
    };
    const createResponse = await createUser(anonUserData);
    const anonUser = validateUsersEndpointResponse(createResponse);
    expect(anonUser.fcid).toBeDefined();
    const anonFcid = anonUser.fcid;

    // Step 2: Merge the anonymous user into a registered user using fcaid
    const fcaid = generateFcaid();
    const mergeUserData = {
      fcaid,
      fcid: anonFcid,
      identifiers: {
        gaid: [gaid1],
        adid: [adid],
      },
    };
    const mergeResponse = await createUser(mergeUserData);
    const mergedUser = validateUsersEndpointResponse(mergeResponse);
    expect(mergedUser.fcaid).toBe(fcaid);
    expect(mergedUser.fcid).toBe(anonFcid);
    expect(mergedUser.type).toBe(UserType.REGISTERED);

    // Step 3: Update user using new gaid and same adid
    const gaid2 = generateUUID();
    const updateUserData = {
      fcaid,
      fcid: anonFcid,
      identifiers: {
        gaid: [gaid2],
        adid: [adid],
      },
    };
    const updateResponse = await createUser(updateUserData);
    const updatedUser = validateUsersEndpointResponse(updateResponse);
    expect(updatedUser.fcaid).toBe(fcaid);
    expect(updatedUser.fcid).toBe(anonFcid);
    expect(updatedUser.type).toBe(UserType.REGISTERED);

    // Step 4: Attempt to merge again with new gaid and same adid (should return the same user)
    const mergeAgainData = {
      fcaid,
      fcid: anonFcid,
      identifiers: {
        gaid: [generateUUID()],
        adid: [adid],
      },
    };
    const mergeAgainResponse = await createUser(mergeAgainData);
    const mergedUserAgain = validateUsersEndpointResponse(mergeAgainResponse);
    expect(mergedUserAgain.fcaid).toBe(fcaid);
    expect(mergedUserAgain.fcid).toBe(anonFcid);
    expect(mergedUserAgain.type).toBe(UserType.REGISTERED);

    // Step 5: Should fail because fcaid AND installed_at is not provided
    const failData = {
      fcid: anonFcid,
      identifiers: {
        gaid: [generateUUID()],
        adid: [adid],
      },
    };

    await expect(createUser(failData)).rejects.toMatchObject({
      response: {
        status: 400,
        data: {
          statusCode: 400,
          message: {
            error: 'Bad Request',
            message: 'installed_at is required for new users',
            statusCode: 400,
          },
        },
      },
    });
  });
});
