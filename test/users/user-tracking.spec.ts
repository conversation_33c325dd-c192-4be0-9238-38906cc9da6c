import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  generateUUID,
  generateAdid,
} from '../test-utils';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { Test, TestingModule } from '@nestjs/testing';

describe('User Tracking', () => {
  let testSetup: TestSetup;
  let errorLogger: ErrorLoggerService;
  let module: TestingModule;
  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
    module = await Test.createTestingModule({
      providers: [
        {
          provide: ErrorLoggerService,
          useValue: mockErrorLogger,
        },
      ],
    }).compile();
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  const mockErrorLogger = {
    logError: jest.fn(),
  };

  describe('New User Tracking', () => {
    it('should mark user as new when creating with fresh install', async () => {
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [generateUUID()],
          adid: [generateAdid()],
        },
        isFreshInstall: true,
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      expect(createdUser.newUser).toBe(true);
      expect(createdUser.properties).toBeDefined();
      expect(createdUser.properties?.reinstallCount).toBe(0);
    });

    it('should not mark user as new when creating without fresh install', async () => {
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [generateUUID()],
          adid: [generateAdid()],
        },
        isFreshInstall: false,
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      expect(createdUser.newUser).toBeUndefined(); // Should not be in response
      expect(createdUser.properties).toBeDefined();
      expect(createdUser.properties?.reinstallCount).toBe(0);
    });
  });

  describe('Reinstall Tracking', () => {
    it('should increment reinstall count for existing user with fresh install', async () => {
      // First create a user
      const idfv = generateUUID();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          idfa: [generateUUID()],
        },
        isFreshInstall: true,
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Then update with fresh install flag
      const updateData = {
        fcid: createdUser.fcid,
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          idfa: [generateUUID()],
        },
        isFreshInstall: true,
      };

      const updateResponse = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(updateResponse);
      expect(createdUser.properties).toBeDefined();
      expect(updatedUser.properties?.reinstallCount).toBe(1);
      expect(updatedUser.newUser).toBeUndefined();
    });

    it('should not increment reinstall count for existing user without fresh install', async () => {
      // First create a user
      const idfv = generateUUID();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          idfa: [generateUUID()],
        },
        isFreshInstall: true,
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Then update without fresh install flag
      const updateData = {
        fcid: createdUser.fcid,
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          idfa: [generateUUID()],
        },
        isFreshInstall: false,
      };

      const updateResponse = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(updateResponse);

      expect(createdUser.properties).toBeDefined();
      expect(updatedUser.properties?.reinstallCount).toBe(0);
      expect(updatedUser.newUser).toBeUndefined(); // Should not be in response for non-fresh install
    });

    it('should handle multiple reinstalls correctly', async () => {
      // First create a user
      const idfv = generateUUID();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          idfa: [generateUUID()],
        },
        isFreshInstall: true,
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Perform multiple reinstalls
      for (let i = 0; i < 3; i++) {
        const updateData = {
          fcid: createdUser.fcid,
          type: UserType.ANONYMOUS,
          identifiers: {
            idfv: [idfv],
            idfa: [generateUUID()],
          },
          isFreshInstall: true,
        };
        const updateResponse = await createUser(updateData);
        const updatedUser = validateUsersEndpointResponse(updateResponse);
        expect(updatedUser.properties).toBeDefined();
        expect(updatedUser.properties?.reinstallCount).toBe(i + 1);
        expect(updatedUser.newUser).toBeUndefined();
      }
    });
  });

  describe('Property Preservation', () => {
    it('should preserve tracking properties during regular updates', async () => {
      // First create a user with fresh install
      const idfv = generateUUID();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          adid: [generateAdid()],
        },
        isFreshInstall: true,
        properties: {
          totalAdRevenue: 100,
        },
        installed_at: new Date(),
      };

      const createResponse = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(createResponse);

      // Then update with different properties
      const updateData = {
        fcid: createdUser.fcid,
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
          adid: [generateAdid()],
        },
        properties: {
          totalAdRevenue: 200,
        },
      };

      const updateResponse = await createUser(updateData);
      const updatedUser = validateUsersEndpointResponse(updateResponse);

      // Tracking properties should be preserved
      expect(updatedUser.newUser).toBeUndefined(); // Should not be in response for regular update
      expect(updatedUser.properties).toBeDefined();
      expect(updatedUser.properties?.reinstallCount).toBe(0);
      // Other properties should be updated
      expect(updatedUser.properties?.totalAdRevenue).toBe(300);
    });
  });
});
