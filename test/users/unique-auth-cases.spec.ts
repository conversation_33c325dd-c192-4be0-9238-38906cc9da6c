import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUserWithIdfv,
  validateUsersEndpointResponse,
  generateUUID,
  createJwtForIdfv,
} from '../test-utils';

describe('Unique Auth/User Cases', () => {
  let testSetup: TestSetup;
  const createdUsers: { fcid: string; idfv: string }[] = [];

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  function trackOrUpdateUser(fcid: string, idfv: string) {
    const idx = createdUsers.findIndex(u => u.fcid === fcid);
    if (idx !== -1) {
      createdUsers[idx].idfv = idfv;
    } else {
      createdUsers.push({ fcid, idfv });
    }
  }

  it('should create user with idfv from token if /users called without FCID', async () => {
    const idfv = generateUUID();
    const userData = {
      identifiers: { idfv: [idfv] },
      installed_at: new Date(),
    };
    const createResponse = await createUserWithIdfv(userData, idfv);
    const createdUser = validateUsersEndpointResponse(createResponse);
    trackOrUpdateUser(createdUser.fcid, idfv);
    expect(createdUser.identifiers.idfv).toEqual([]); // API clears identifiers
  });

  it('should fail if /users called with idfa but token was for idfv', async () => {
    const idfv = generateUUID();
    const idfa = generateUUID();
    const userData = {
      identifiers: { idfa: [idfa] },
      installed_at: new Date(),
    };
    // Deliberate mismatch: JWT idfv, request idfa
    await expect(createUserWithIdfv(userData, idfv)).rejects.toMatchObject({
      response: {
        status: 403,
        data: expect.anything(),
      },
    });
  });

  it('should create new anonymous user with FCID and new IDFV but no IDFA', async () => {
    const idfv = generateUUID();
    const fcid = generateUUID();
    const userData = {
      fcid,
      identifiers: { idfv: [idfv] },
      installed_at: new Date(),
    };
    const createResponse = await createUserWithIdfv(userData, idfv);
    const createdUser = validateUsersEndpointResponse(createResponse);
    trackOrUpdateUser(createdUser.fcid, idfv);
    expect(createdUser.identifiers.idfv).toEqual([]);
  });

  it('should use FCAID if provided with FCID and new IDFV', async () => {
    const idfv = generateUUID();
    const fcid = generateUUID();
    const fcaid = 'TEST_' + Math.floor(Math.random() * 1000);
    const userData = {
      fcid,
      fcaid,
      identifiers: { idfv: [idfv] },
      installed_at: new Date(),
    };
    const createResponse = await createUserWithIdfv(userData, idfv);
    const createdUser = validateUsersEndpointResponse(createResponse);
    trackOrUpdateUser(createdUser.fcid, idfv);
    expect(createdUser.fcaid).toBe(fcaid);
  });

  it('should add device to user bound to FCAID if FCAID and new device ID provided', async () => {
    const idfv = generateUUID();
    const fcid = generateUUID();
    const fcaid = 'TEST_' + Math.floor(Math.random() * 1000);
    const userData = {
      fcid,
      fcaid,
      identifiers: { idfv: [idfv] },
      installed_at: new Date(),
    };
    const createResponse = await createUserWithIdfv(userData, idfv);
    const createdUser = validateUsersEndpointResponse(createResponse);
    trackOrUpdateUser(createdUser.fcid, idfv);
    expect(createdUser.fcaid).toBe(fcaid);
  });

  it('should return matched user if FCID, new IDFV, and match statement with old IDFV', async () => {
    const idfv1 = generateUUID();
    const idfa = generateUUID();
    const fcid = generateUUID();
    // Create user with idfv1 and idfa
    const userData1 = {
      fcid,
      identifiers: { idfv: [idfv1], idfa: [idfa] },
      installed_at: new Date(),
    };
    const createResponse1 = await createUserWithIdfv(userData1, idfv1);
    const createdUser1 = validateUsersEndpointResponse(createResponse1);
    trackOrUpdateUser(createdUser1.fcid, idfv1);
    // Create again with same idfv and idfa
    const userData2 = {
      fcid: createdUser1.fcid,
      identifiers: { idfv: [idfv1], idfa: [idfa] },
      installed_at: new Date(),
    };
    const createResponse2 = await createUserWithIdfv(userData2, idfv1);
    const createdUser2 = validateUsersEndpointResponse(createResponse2);
    trackOrUpdateUser(createdUser2.fcid, idfv1);
    expect(createdUser2.fcid).toBe(createdUser1.fcid);
  });
});
