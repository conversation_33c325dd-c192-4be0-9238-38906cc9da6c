import { UserType } from '../../src/users/user.dto';

import { TestSetup, createUser, validateUsersEndpointResponse, generateUUID } from '../test-utils';

describe('Endpoint /users: Calculate Age', () => {
  const testSetup: TestSetup = TestSetup.getInstance();

  afterAll(async () => {
    await testSetup.cleanup();
  });

  let registered = 0;
  const getFcaid = () => `test-fcaid-${registered++}`;
  const now = new Date();

  const birthDate = '06-26-1986';
  const calculatedAge = new Date().getUTCFullYear() - new Date(birthDate).getUTCFullYear();

  describe('Registered User: Calculate via birthDate property', () => {
    it('should calculate current and declared age for a user created', async () => {
      const inputUser = {
        fcaid: getFcaid(),
        identifiers: {
          idfv: [generateUUID()],
        },
        properties: {
          birthDate,
        },
        installed_at: now,
      };
      const response = await createUser(inputUser);
      const createdUserResponse = validateUsersEndpointResponse(response);
      expect(createdUserResponse.type).toBe(UserType.REGISTERED);
      expect(createdUserResponse.properties).toMatchObject(
        expect.objectContaining({
          birthDate,
          userCurrentAge: calculatedAge,
          userDeclaredAge: calculatedAge,
        }),
      );
      expect(createdUserResponse.modified_properties).toMatchObject(
        expect.arrayContaining([
          {
            affected_at: now.toISOString(),
            affected_property: 'birthDate',
            affected_value: birthDate,
          },
          {
            affected_at: now.toISOString(),
            affected_property: 'userCurrentAge',
            affected_value: calculatedAge,
          },
          {
            affected_at: now.toISOString(),
            affected_property: 'userDeclaredAge',
            affected_value: calculatedAge,
          },
        ]),
      );
    });

    it('should calculate current and declared age for a user updated', async () => {
      const idfv = generateUUID();
      const inputUser = {
        identifiers: {
          idfv: [idfv],
        },
        installed_at: now,
      };
      const createResponse = await createUser(inputUser);
      const createdUser = validateUsersEndpointResponse(createResponse);
      const updateUser = {
        fcid: createdUser.fcid,
        fcaid: getFcaid(),
        identifiers: {
          idfv: [idfv],
        },
        properties: {
          birthDate,
        },
      };
      const updateResponse = await createUser(updateUser);
      const updatedUserResponse = validateUsersEndpointResponse(updateResponse);
      expect(updatedUserResponse.type).toBe(UserType.REGISTERED);
      expect(updatedUserResponse.properties).toMatchObject(
        expect.objectContaining({
          birthDate,
          userCurrentAge: calculatedAge,
          userDeclaredAge: calculatedAge,
        }),
      );
      expect(updatedUserResponse.modified_properties).toMatchObject(
        expect.arrayContaining([
          {
            // affected_at: updatedUserResponse.updated_at,
            affected_at: expect.any(String),
            affected_property: 'birthDate',
            affected_value: birthDate,
          },
          {
            // affected_at: updatedUserResponse.updated_at,
            affected_at: expect.any(String),
            affected_property: 'userCurrentAge',
            affected_value: calculatedAge,
          },
          {
            // affected_at: updatedUserResponse.updated_at,
            affected_at: expect.any(String),
            affected_property: 'userDeclaredAge',
            affected_value: calculatedAge,
          },
        ]),
      );
    });

    it('should calculate current and declared age for a user that did not have the properties previosly saved', async () => {
      const idfv = generateUUID();
      const fcaid = getFcaid();
      const inputUser = {
        fcaid,
        identifiers: {
          idfv: [idfv],
        },
        properties: {
          birthDate: '1986-26-06',
        },
        installed_at: now,
      };
      const response = await createUser(inputUser);
      const createdUserResponse = validateUsersEndpointResponse(response);
      expect(createdUserResponse.type).toBe(UserType.REGISTERED);
      expect(createdUserResponse.properties?.birthDate).toBe(undefined);
      expect(createdUserResponse.modified_properties).toBe(undefined);
      const updateUser = {
        fcid: createdUserResponse.fcid,
        fcaid,
        identifiers: {
          idfv: [idfv],
        },
        properties: {
          birthDate: birthDate,
        },
      };
      const updateResponse = await createUser(updateUser);
      const updatedUserResponse = validateUsersEndpointResponse(updateResponse);
      expect(updatedUserResponse.properties).toMatchObject(
        expect.objectContaining({
          birthDate: birthDate,
          userCurrentAge: calculatedAge,
          userDeclaredAge: calculatedAge,
        }),
      );
      expect(updatedUserResponse.modified_properties).toMatchObject(
        expect.arrayContaining([
          {
            // affected_at: updatedUserResponse.updated_at,
            affected_at: expect.any(String),
            affected_property: 'birthDate',
            affected_value: birthDate,
          },
          {
            // affected_at: updatedUserResponse.updated_at,
            affected_at: expect.any(String),
            affected_property: 'userCurrentAge',
            affected_value: calculatedAge,
          },
          {
            // affected_at: updatedUserResponse.updated_at,
            affected_at: expect.any(String),
            affected_property: 'userDeclaredAge',
            affected_value: calculatedAge,
          },
        ]),
      );
    });

    it('should not calculate current and declared age if birthDate format is wrong', async () => {
      const inputUser = {
        fcaid: getFcaid(),
        identifiers: {
          idfv: [generateUUID()],
        },
        properties: {
          birthDate: '1986-26-06',
        },
        installed_at: now,
      };
      const response = await createUser(inputUser);
      const createdUserResponse = validateUsersEndpointResponse(response);
      expect(createdUserResponse.type).toBe(UserType.REGISTERED);
      expect(createdUserResponse.properties?.birthDate).toBe(undefined);
      expect(createdUserResponse.modified_properties).toBe(undefined);
    });

    it('birthDate property should be immutable', async () => {
      const idfv = generateUUID();
      const fcaid = getFcaid();
      const inputUser = {
        fcaid,
        identifiers: {
          idfv: [idfv],
        },
        properties: {
          birthDate,
        },
        installed_at: now,
      };
      const response = await createUser(inputUser);
      const createdUser = validateUsersEndpointResponse(response);
      const updateUser = {
        fcaid,
        fcid: createdUser.fcid,
        identifiers: {
          idfv: [idfv],
        },
        properties: {
          birthDate: '06-26-2022',
        },
      };
      const updateResponse = await createUser(updateUser);
      const updatedUser = validateUsersEndpointResponse(updateResponse);
      expect(updatedUser.type).toBe(UserType.REGISTERED);
      expect(updatedUser.properties).toMatchObject(
        expect.objectContaining({
          birthDate,
          userCurrentAge: calculatedAge,
          userDeclaredAge: calculatedAge,
        }),
      );
    });
  });
});
