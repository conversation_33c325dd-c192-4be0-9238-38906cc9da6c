import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  generateAdid,
  generateUUID,
  createJwtForIdentifier,
} from '../test-utils';
import axios from 'axios';
import { config } from '../config';

describe('Anonymous User', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('iOS Scenarios', () => {
    describe('iOS < 14', () => {
      it('should create a new user with valid IDFA and IDFV', async () => {
        const idfa = generateUUID();
        const idfv = generateUUID();
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: {
            idfa: [idfa],
            idfv: [idfv],
          },
          installed_at: new Date(),
        };

        const response = await createUser(userData);
        const createdUser = validateUsersEndpointResponse(response);
        // expect(createdUser.identifiers.idfa).toEqual([idfa]);
        expect(createdUser.identifiers.idfa).toEqual([]);
        // expect(createdUser.identifiers.idfa).toEqual([idfa]);
        expect(createdUser.identifiers.idfv).toEqual([]);
      });

      it('should filter out dummy IDFA values', async () => {
        const idfv = generateUUID();
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: {
            idfa: ['00000000-0000-0000-0000-000000000000'],
            idfv: [idfv],
          },
          installed_at: new Date(),
        };

        const response = await createUser(userData);
        const createdUser = validateUsersEndpointResponse(response);
        // expect(createdUser.identifiers.idfa).toBeUndefined();
        expect(createdUser.identifiers.idfa).toEqual([]);
        // expect(createdUser.identifiers.idfv).toEqual([idfv]);
        expect(createdUser.identifiers.idfv).toEqual([]);
      });
    });

    describe('iOS 14+', () => {
      it('should create user with only IDFV when IDFA is not available', async () => {
        const idfv = generateUUID();
        const userData = {
          type: UserType.ANONYMOUS,
          identifiers: {
            idfv: [idfv],
          },
          installed_at: new Date(),
        };

        const response = await createUser(userData);
        const createdUser = validateUsersEndpointResponse(response);
        // expect(createdUser.identifiers.idfv).toEqual([idfv]);
        expect(createdUser.identifiers.idfv).toEqual([]);
        // expect(createdUser.identifiers.idfa).toBeUndefined();
        expect(createdUser.identifiers.idfa).toEqual([]);
      });
    });
  });

  describe('Android Scenarios', () => {
    it('should create a new user with valid GAID and ADID', async () => {
      const gaid = generateUUID();
      const adid = generateAdid();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          gaid: [gaid],
          adid: [adid],
        },
        installed_at: new Date(),
      };

      const response = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(response);
      // expect(createdUser.identifiers.gaid).toEqual([gaid]);
      expect(createdUser.identifiers.gaid).toEqual([]);
      // expect(createdUser.identifiers.adid).toEqual([adid]);
      expect(createdUser.identifiers.adid).toEqual([]);
    });

    it('should create a new user with only ADID when GAID is not available', async () => {
      const adid = generateAdid();
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          adid: [adid],
        },
        installed_at: new Date(),
      };

      const response = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(response);
      // expect(createdUser.identifiers.adid).toEqual([adid]);
      expect(createdUser.identifiers.adid).toEqual([]);
      // expect(createdUser.identifiers.gaid).toBeUndefined();
      expect(createdUser.identifiers.gaid).toEqual([]);
    });
  });

  describe('Cross-Platform Scenarios', () => {
    it('should ignore fcid provided by the app on user creation', async () => {
      const forcedFcid = `${generateUUID()}-99`;
      const idfv = generateUUID();

      const userData = {
        fcid: forcedFcid,
        type: UserType.ANONYMOUS,
        identifiers: {
          idfv: [idfv],
        },
        isFreshInstall: true,
        installed_at: new Date(),
      };

      const response = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(response);

      expect(createdUser.fcid !== forcedFcid).toBeTruthy();
    });
  });

  describe('Error Scenarios', () => {
    it(`should return 400 for invalid identifiers`, async () => {
      const inputData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          invalid_key: ['invalid-format'],
        } as any,
        installed_at: new Date(),
      };

      const token = await createJwtForIdentifier('idfv', generateUUID());
      const headers = { Authorization: `Bearer ${token}` };
      await expect(
        axios.post(`${config.roshiUrl}/users`, inputData, { headers }),
      ).rejects.toMatchObject({
        response: {
          status: 400,
          data: {
            statusCode: 400,
            message: expect.anything(),
          },
        },
      });
    });

    it(`should return 400 for empty identifiers`, async () => {
      const inputData = {
        type: UserType.ANONYMOUS,
        identifiers: {},
        installed_at: new Date(),
      };

      const token = await createJwtForIdentifier('idfv', generateUUID());
      const headers = { Authorization: `Bearer ${token}` };
      await expect(
        axios.post(`${config.roshiUrl}/users`, inputData, { headers }),
      ).rejects.toMatchObject({
        response: {
          status: 400,
          data: {
            statusCode: 400,
            message: expect.anything(),
          },
        },
      });
    });

    // Endpoint /users ignores type prop: it should create an anonymous user
    // it('should return 400 when trying to create merged user', async () => {
    //   const userData = {
    //     type: UserType.MERGED,
    //     identifiers: {
    //       idfa: [generateUUID()],
    //       idfv: [generateUUID()],
    //     },
    //     installed_at: new Date(),
    //   };

    //   await expect(createUser(userData)).rejects.toMatchObject({
    //     response: {
    //       status: 400,
    //       data: {
    //         statusCode: 400,
    //         message: expect.anything(),
    //       },
    //     },
    //   });
    // });

    it('should fail to create user with empty identifiers', async () => {
      const userData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          adid: [],
          idfv: [],
          gaid: [],
          idfa: [],
        },
        installed_at: new Date(),
      };

      const token = await createJwtForIdentifier('idfv', generateUUID());
      const headers = { Authorization: `Bearer ${token}` };
      await expect(
        axios.post(`${config.roshiUrl}/users`, userData, { headers }),
      ).rejects.toMatchObject({
        response: {
          status: 400,
          data: expect.anything(),
        },
      });
    });
  });
});
