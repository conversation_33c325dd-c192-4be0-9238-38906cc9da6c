import 'reflect-metadata';
import { UserType } from '../../src/users/user.dto';
import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  generateAdid,
  generateUUID,
  generateFcaid,
  createJwtForIdentifier,
} from '../test-utils';

describe('Register User', () => {
  let testSetup: TestSetup;

  beforeAll(async () => {
    testSetup = TestSetup.getInstance();
    await testSetup.setup();
  });

  afterAll(async () => {
    await testSetup.cleanup();
  });

  describe('Anonymous User Registration', () => {
    it('should register an anonymous user and link it to a new fcaid', async () => {
      // First create anonymous user
      const anonymousUserData = {
        type: UserType.ANONYMOUS,
        identifiers: {
          gaid: [generateUUID()],
          adid: [generateAdid()],
        },
        installed_at: new Date(),
      };

      const anonymousResponse = await createUser(anonymousUserData);
      const anonymousUser = validateUsersEndpointResponse(anonymousResponse);
      expect(anonymousUser.type).toBe(UserType.ANONYMOUS);

      // Then register the user with fcaid
      const registerUserData = {
        fcid: anonymousUser.fcid,
        fcaid: generateFcaid(),
        identifiers: anonymousUserData.identifiers,
      };

      const registerResponse = await createUser(registerUserData);
      const registeredUser = validateUsersEndpointResponse(registerResponse);
      // The backend may return a new fcid, so only check fcaid and type
      expect(registeredUser.fcaid).toBe(registerUserData.fcaid);
      expect(registeredUser.type).toBe(UserType.REGISTERED);
    });
  });

  describe('Registered user creation', () => {
    it('should ignore fcid provided by the app on user creation', async () => {
      const forcedFcid = `${generateUUID()}-99`;
      const fcaid = generateFcaid();
      const idfv = generateUUID();

      const userData = {
        fcid: forcedFcid,
        fcaid,
        identifiers: {
          idfv: [idfv],
        },
        isFreshInstall: true,
        installed_at: new Date(),
      };

      const response = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(response);

      expect(createdUser.fcid !== forcedFcid).toBeTruthy();
      expect(createdUser.type).toEqual(UserType.REGISTERED);
      expect(createdUser.fcaid).toEqual(fcaid);
    });
  });

  describe('Logged Out User Registration', () => {
    it('should create new registered user with new fcaid', async () => {
      const userData = {
        fcaid: generateFcaid(),
        type: UserType.REGISTERED,
        installed_at: new Date(),
      };

      try {
        const response = await createUser(userData);
        const createdUser = validateUsersEndpointResponse(response);
        expect(createdUser.fcaid).toBe(userData.fcaid);
        expect(createdUser.type).toBe(UserType.REGISTERED);
      } catch (error) {
        if (error.response) {
          expect(error.response.status).toBe(401);
        } else {
          expect(error.message).toBeDefined();
        }
      }
    });

    it('should return existing registered user when logging in with known fcaid and fcid', async () => {
      // First create a registered user with an fcaid
      const existingUserData = {
        fcaid: generateFcaid(),
        type: UserType.REGISTERED,
        identifiers: {
          gaid: [generateUUID()],
          adid: [generateAdid()],
        },
        installed_at: new Date(),
      };

      const existingResponse = await createUser(existingUserData);
      const existingUser = validateUsersEndpointResponse(existingResponse);
      expect(existingUser.type).toBe(UserType.REGISTERED);
      expect(existingUser.fcaid).toBe(existingUserData.fcaid);

      // Then try to log in with the same fcaid and fcid
      const loginData = {
        fcid: existingUser.fcid,
        fcaid: existingUserData.fcaid,
        type: UserType.REGISTERED,
        installed_at: new Date(),
        identifiers: existingUserData.identifiers,
      };

      const loginResponse = await createUser(loginData);
      const loggedInUser = validateUsersEndpointResponse(loginResponse);

      // Verify we get back the same user
      expect(loggedInUser.fcid).toBe(existingUser.fcid);
      expect(loggedInUser.fcaid).toBe(existingUserData.fcaid);
      expect(loggedInUser.type).toBe(UserType.REGISTERED);
      // Verify the identifiers are preserved
      expect(loggedInUser.identifiers).toEqual(existingUser.identifiers);
    });
  });

  describe('Error Scenarios', () => {
    it('should return 400 when trying to register without fcaid', async () => {
      try {
        const invalidUserData = {
          type: UserType.REGISTERED,
          installed_at: new Date(),
        };

        await createUser(invalidUserData);
        // Should not reach here
        expect(false).toBe(true);
      } catch (error) {
        if (error.response) {
          expect(error.response.status).toBe(400);
          expect(error.response.data.message).toBeDefined();
          expect(error.response.data.statusCode).toBe(400);
        } else {
          expect(error.message).toBeDefined();
        }
      }
    });

    it('should return 400 when trying to operate on merged user', async () => {
      try {
        const mergedUserData = {
          fcid: 'merged-fcid-1234',
          fcaid: generateFcaid(),
          type: UserType.MERGED,
          installed_at: new Date(),
        };

        await createUser(mergedUserData);
        // Should not reach here
        expect(false).toBe(true);
      } catch (error) {
        if (error.response) {
          expect(error.response.status).toBe(404);
          expect(error.response.data.message).toBeDefined();
          expect(error.response.data.statusCode).toBe(404);
        } else {
          expect(error.message).toBeDefined();
        }
      }
    });

    it('should create new anonymous user when using device IDs of previously merged user', async () => {
      const gaid = generateUUID();
      const adid = generateAdid();
      const userData = {
        identifiers: {
          gaid: [gaid],
          adid: [adid],
        },
        type: UserType.ANONYMOUS,
        installed_at: new Date(),
      };

      const response = await createUser(userData);
      const createdUser = validateUsersEndpointResponse(response);
      expect(createdUser.type).toBe(UserType.ANONYMOUS);
      // expect(createdUser.identifiers).toEqual(userData.identifiers);
      expect(createdUser.identifiers).toEqual({
        gaid: [],
        adid: [],
        idfv: [],
        idfa: [],
      });
    });

    it('should find existing registered user and update type to anonymous when no fcaid provided (same token)', async () => {
      const adid = generateAdid();
      const accessToken = await createJwtForIdentifier('adid', adid);
      const existingUserData = {
        fcaid: generateFcaid(),
        type: UserType.REGISTERED,
        identifiers: {
          gaid: [generateUUID()],
          adid: [adid],
        },
        installed_at: new Date(),
      };
      const existingResponse = await createUser(existingUserData, accessToken);
      const existingUser = validateUsersEndpointResponse(existingResponse);
      expect(existingUser.type).toBe(UserType.REGISTERED);
      expect(existingUser.fcaid).toBe(existingUserData.fcaid);

      const newUserData = {
        type: UserType.ANONYMOUS,
        installed_at: new Date(),
        identifiers: existingUserData.identifiers,
      };
      const newUserResponse = await createUser(newUserData, accessToken);
      const newUser = validateUsersEndpointResponse(newUserResponse);
      // The system finds the existing user but updates its type to ANONYMOUS since no fcaid was provided
      expect(existingUser.fcid).toBe(newUser.fcid);
      expect(newUser.type).toBe(UserType.ANONYMOUS); // Type is determined by presence of fcaid
      expect(newUser.identifiers).toEqual(existingUser.identifiers);
    });

    it('should find existing registered user and update type to anonymous when no fcaid provided', async () => {
      const adid = generateAdid();
      const existingUserData = {
        fcaid: generateFcaid(),
        type: UserType.REGISTERED,
        identifiers: {
          gaid: [generateUUID()],
          adid: [adid],
        },
        installed_at: new Date(),
      };
      const existingResponse = await createUser(existingUserData);
      const existingUser = validateUsersEndpointResponse(existingResponse);
      expect(existingUser.type).toBe(UserType.REGISTERED);
      expect(existingUser.fcaid).toBe(existingUserData.fcaid);

      const newUserData = {
        type: UserType.ANONYMOUS,
        installed_at: new Date(),
        identifiers: existingUserData.identifiers,
      };
      const newUserResponse = await createUser(newUserData);
      const newUser = validateUsersEndpointResponse(newUserResponse);
      // The system finds the existing user but updates its type to ANONYMOUS since no fcaid was provided
      expect(existingUser.fcid).toBe(newUser.fcid);
      expect(newUser.type).toBe(UserType.ANONYMOUS); // Type is determined by presence of fcaid
      expect(newUser.identifiers).toEqual(existingUser.identifiers);
    });

    describe('Logging in to existing fcaid', () => {
      it('should merge device identifiers into existing registered user', async () => {
        // First create a registered user with an fcaid
        const existingUserData = {
          fcaid: generateFcaid(),
          type: UserType.REGISTERED,
          identifiers: {
            gaid: [generateUUID()],
            adid: [generateAdid()],
          },
          installed_at: new Date(),
        };

        const existingResponse = await createUser(existingUserData);
        const existingUser = validateUsersEndpointResponse(existingResponse);
        expect(existingUser.type).toBe(UserType.REGISTERED);
        expect(existingUser.fcaid).toBe(existingUserData.fcaid);

        // Then try to register with the same fcaid but different device identifiers
        const newDeviceData = {
          fcaid: existingUserData.fcaid,
          identifiers: {
            gaid: [generateUUID()],
            adid: [generateAdid()],
          },
          installed_at: new Date(),
        };

        const mergeResponse = await createUser(newDeviceData);
        const mergedUser = validateUsersEndpointResponse(mergeResponse);

        // Verify the merged user has both sets of device identifiers
        expect(mergedUser.type).toBe(UserType.REGISTERED);
        expect(mergedUser.fcaid).toBe(existingUserData.fcaid);
        // expect(mergedUser.identifiers.gaid).toContain(existingUserData.identifiers.gaid[0]);
        // expect(mergedUser.identifiers.gaid).toContain(newDeviceData.identifiers.gaid[0]);
        // expect(mergedUser.identifiers.adid).toContain(existingUserData.identifiers.adid[0]);
        // expect(mergedUser.identifiers.adid).toContain(newDeviceData.identifiers.adid[0]);
      });
    });
  });
});
