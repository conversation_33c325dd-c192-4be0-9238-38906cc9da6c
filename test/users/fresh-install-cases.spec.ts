import { UserType } from '../../src/users/user.dto';

import {
  TestSetup,
  createUser,
  validateUsersEndpointResponse,
  generateAdid,
  generateUUID,
} from '../test-utils';

describe('Endpoint /users: Fresh Install Scenarios', () => {
  const testSetup: TestSetup = TestSetup.getInstance();

  afterAll(async () => {
    await testSetup.cleanup();
  });

  it('should create a user when isFreshInstall === true (returns newUser on payload)', async () => {
    const idfa = generateUUID();
    const idfv = generateUUID();
    const inputUser = {
      type: UserType.ANONYMOUS,
      identifiers: {
        idfa: [idfa],
        idfv: [idfv],
      },
      installed_at: new Date(),
      isFreshInstall: true,
    };
    const response = await createUser(inputUser);
    const createdUser = validateUsersEndpointResponse(response);
    expect(createdUser.newUser).toBe(true);
    expect(createdUser.properties).toMatchObject(
      expect.objectContaining({
        reinstallCount: 0,
      }),
    );
  });

  it('should create a user when isFreshInstall === false', async () => {
    const idfa = generateUUID();
    const idfv = generateUUID();
    const inputUser = {
      type: UserType.ANONYMOUS,
      identifiers: {
        idfa: [idfa],
        idfv: [idfv],
      },
      installed_at: new Date(),
      isFreshInstall: false,
    };
    const response = await createUser(inputUser);
    const createdUser = validateUsersEndpointResponse(response);
    expect(createdUser.newUser).toBe(undefined);
    expect(createdUser.properties).toMatchObject(
      expect.objectContaining({
        reinstallCount: 0,
      }),
    );
  });

  it('should update a user when isFreshInstall === true (adds 1 to reinstallCount prop)', async () => {
    const idfv = generateUUID();
    const inputUser = {
      type: UserType.ANONYMOUS,
      identifiers: {
        idfa: [generateUUID()],
        idfv: [idfv],
      },
      properties: {
        reinstallCount: 10,
      },
      installed_at: new Date(),
    };
    const createResponse = await createUser(inputUser);
    const createdUser = validateUsersEndpointResponse(createResponse);
    const updateUser = {
      fcid: createdUser.fcid,
      identifiers: {
        idfv: [idfv],
      },
      fcaid: 'test-fcaid',
      isFreshInstall: true,
    };
    const updateResponse = await createUser(updateUser);
    const updatedUser = validateUsersEndpointResponse(updateResponse);
    expect(updatedUser.type).toBe(UserType.REGISTERED);
    expect(updatedUser.newUser).toBe(undefined);
    expect(updatedUser.properties).toMatchObject(
      expect.objectContaining({
        reinstallCount: 11,
      }),
    );
  });

  it('should never update reinstallCount user property directly', async () => {
    const adid = generateAdid();
    const inputUser = {
      identifiers: {
        adid: [adid],
      },
      properties: {
        reinstallCount: 999,
      },
      installed_at: new Date(),
    };
    const createResponse = await createUser(inputUser);
    const createdUser = validateUsersEndpointResponse(createResponse);
    const updateUser = {
      fcid: createdUser.fcid,
      identifiers: {
        adid: [adid],
      },
      properties: {
        reinstallCount: 2000,
      },
    };
    const updateResponse = await createUser(updateUser);
    const updatedUser = validateUsersEndpointResponse(updateResponse);
    expect(updatedUser.newUser).toBe(undefined);
    expect(updatedUser.properties).toMatchObject(
      expect.objectContaining({
        reinstallCount: 999,
      }),
    );
  });

  it('should never update reinstallCount user property directly', async () => {
    const adid = generateAdid();
    const inputUser = {
      type: UserType.ANONYMOUS,
      identifiers: {
        adid: [adid],
      },
      properties: {
        reinstallCount: 999,
      },
      installed_at: new Date(),
    };
    const createResponse = await createUser(inputUser);
    const createdUser = validateUsersEndpointResponse(createResponse);
    const updateUser = {
      fcid: createdUser.fcid,
      identifiers: {
        adid: [adid],
      },
      properties: {
        reinstallCount: 2000,
      },
      isFreshInstall: true,
    };
    const updateResponse = await createUser(updateUser);
    const updatedUser = validateUsersEndpointResponse(updateResponse);
    expect(updatedUser.type).toBe(UserType.ANONYMOUS);
    expect(updatedUser.newUser).toBe(undefined);
    expect(updatedUser.properties).toMatchObject(
      expect.objectContaining({
        reinstallCount: 1000,
      }),
    );
  });
});
