import { Test, TestingModule } from '@nestjs/testing';
import { KwsController } from '../../src/kws/kws.controller';
import { KwsService } from '../../src/kws/kws.service';

describe('KwsController', () => {
  let controller: KwsController;
  let service: KwsService;

  const mockKwsService = {
    getAgeGate: jest.fn(),
  };

  const mockAgeGateResponse = {
    data: {
      response: {
        country: 'US',
        region: 'FL',
        consentAge: 13,
        userAge: 13,
        underAgeOfDigitalConsent: false,
      },
      meta: {
        requestId: 'test-request-id',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [KwsController],
      providers: [
        {
          provide: KwsService,
          useValue: mockKwsService,
        },
      ],
    }).compile();

    controller = module.get<KwsController>(KwsController);
    service = module.get<KwsService>(KwsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAgeGate', () => {
    const testParams = {
      ip: '*************',
      age: 13,
    };

    it('should return age gate data', async () => {
      mockKwsService.getAgeGate.mockResolvedValue({
        status: 200,
        data: mockAgeGateResponse.data,
      });

      const result = await controller.getAgeGate(testParams);

      expect(result).toEqual(mockAgeGateResponse.data);
      expect(mockKwsService.getAgeGate).toHaveBeenCalledWith(testParams);
    });

    it('should propagate service errors', async () => {
      const error = new Error('Service error');
      mockKwsService.getAgeGate.mockRejectedValue(error);

      await expect(controller.getAgeGate(testParams)).rejects.toThrow('Service error');
    });
  });
});
