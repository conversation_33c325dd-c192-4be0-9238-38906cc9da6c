import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { of, throwError } from 'rxjs';
import { KwsService } from '../../src/kws/kws.service';
import { KwsTokenService } from '../../src/kws/kws-token.service';
import { KwsAgeGateResponse } from '../../src/kws/interfaces/kws-response.interface';
import { testConfig } from '../../src/config/environments/test.config';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';

describe('KwsService', () => {
  let service: KwsService;
  let httpService: HttpService;
  let tokenService: KwsTokenService;
  let configService: ConfigService;
  let errorLogger: ErrorLoggerService;
  const mockHttpService = {
    get: jest.fn(),
  };

  const mockTokenService = {
    getToken: jest.fn(),
  };

  const mockAgeGateResponse: KwsAgeGateResponse = {
    country: 'US',
    region: 'FL',
    consentAge: 13,
    userAge: 13,
    underAgeOfDigitalConsent: false,
  };

  const mockKwsResponse = {
    data: {
      response: mockAgeGateResponse,
      meta: {
        requestId: 'test-request-id',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KwsService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: KwsTokenService,
          useValue: mockTokenService,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              // Use the actual test configuration values
              if (key === 'kws.apiUrl') return testConfig.kws.apiUrl;
              if (key === 'kws.productClientId') return testConfig.kws.productClientId;
              if (key === 'kws.apiKey') return testConfig.kws.apiKey;
              if (key === 'kws.authUrl') return testConfig.kws.authUrl;
              return null;
            }),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: {
            log: jest.fn(),
            logError: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<KwsService>(KwsService);
    httpService = module.get<HttpService>(HttpService);
    tokenService = module.get<KwsTokenService>(KwsTokenService);
    configService = module.get<ConfigService>(ConfigService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
    // Default mock implementations
    mockTokenService.getToken.mockResolvedValue('test-token');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAgeGateData', () => {
    const testParams = {
      ip: '*************',
      age: 13,
    };

    it('should successfully get age gate data', async () => {
      mockHttpService.get.mockReturnValue(of(mockKwsResponse));

      const result = await service.getAgeGateData(testParams);

      expect(result).toEqual(mockAgeGateResponse);
      expect(mockTokenService.getToken).toHaveBeenCalledWith('age-gate');
      expect(mockHttpService.get).toHaveBeenCalledWith(testConfig.kws.apiUrl, {
        params: testParams,
        headers: {
          Authorization: 'Bearer test-token',
          'User-Agent': 'Flipaclip Age Gate Client',
        },
      });
    });

    it('should throw error when token service fails', async () => {
      const error = new Error('Token service error');
      mockTokenService.getToken.mockRejectedValue(error);

      await expect(service.getAgeGateData(testParams)).rejects.toThrow('Token service error');
      expect(mockHttpService.get).not.toHaveBeenCalled();
    });

    it('should throw error when API URL is not configured', async () => {
      // Override the config service to return null for this specific test
      jest.spyOn(configService, 'get').mockReturnValue(null);

      await expect(service.getAgeGateData(testParams)).rejects.toThrow(
        'KWS API URL not configured',
      );
      expect(mockHttpService.get).not.toHaveBeenCalled();
    });

    it('should throw error when API call fails', async () => {
      mockHttpService.get.mockReturnValue(throwError(() => new Error('API error')));

      await expect(service.getAgeGateData(testParams)).rejects.toThrow('API error');
    });

    it('should throw error when response format is invalid', async () => {
      mockHttpService.get.mockReturnValue(of({ data: { invalid: 'format' } }));

      await expect(service.getAgeGateData(testParams)).rejects.toThrow(
        'Invalid response format from KWS API',
      );
    });
  });

  describe('getAgeGate', () => {
    const testParams = {
      ip: '*************',
      age: 13,
    };

    it('should successfully get age gate response', async () => {
      mockHttpService.get.mockReturnValue(of(mockKwsResponse));

      const result = await service.getAgeGate(testParams);

      expect(result).toEqual({
        status: 200,
        data: mockKwsResponse.data,
      });
    });

    it('should handle API errors gracefully', async () => {
      mockHttpService.get.mockReturnValue(
        throwError(() => ({
          response: { status: 500, data: { message: 'API Error' } },
        })),
      );

      await expect(service.getAgeGate(testParams)).rejects.toThrow('Failed to fetch Age Gate data');
    });

    it('should throw error when API URL is not configured', async () => {
      // Override the config service to return null for this specific test
      jest.spyOn(configService, 'get').mockReturnValue(null);

      await expect(service.getAgeGate(testParams)).rejects.toThrow('Failed to fetch Age Gate data');
    });
  });
});
