import { Test, TestingModule } from '@nestjs/testing';
import { TokenValidationGuard } from '../../src/guards/token-validation.guard';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { createMock } from '@golevelup/ts-jest';
import { ConfigService } from '@nestjs/config';
import { testConfig } from '../../src/config/environments/test.config';
import { WinstonLoggerService } from '../../src/common/services/winston-logger.service';

describe('TokenValidationGuard', () => {
  let guard: TokenValidationGuard;
  let logger: WinstonLoggerService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TokenValidationGuard,
        {
          provide: WinstonLoggerService,
          useValue: {
            warn: jest.fn(),
            log: jest.fn(),
            error: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'jwt') {
                return testConfig.jwt;
              }
              return testConfig[key as keyof typeof testConfig];
            }),
          },
        },
      ],
    }).compile();

    guard = module.get<TokenValidationGuard>(TokenValidationGuard);
    logger = module.get<WinstonLoggerService>(WinstonLoggerService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should allow requests with valid token matching slack token', () => {
    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
            token: testConfig.slack.token,
          },
        }),
      }),
    });

    expect(guard.canActivate(context)).toBe(true);
    expect(logger.log).toHaveBeenCalledWith(
      'Token validation successful for delete request',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when token does not match slack token', () => {
    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
            token: 'invalid-token',
          },
        }),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Delete request contains invalid token',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when token is missing', () => {
    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
          },
        }),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Delete request missing required token field',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when token is empty', () => {
    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
            token: '',
          },
        }),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Delete request contains empty token field',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when token is not a string', () => {
    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
            token: 123,
          },
        }),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Delete request contains invalid token type',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when body is missing', () => {
    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({}),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Delete request missing required body',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when slack token is not configured', () => {
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'slack') {
        return undefined;
      }
      if (key === 'jwt') {
        return testConfig.jwt;
      }
      return testConfig[key as keyof typeof testConfig];
    });

    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
            token: 'any-token',
          },
        }),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Slack token is not configured',
      'TokenValidationGuard',
    );
  });

  it('should throw UnauthorizedException when slack token is empty', () => {
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'slack') {
        return { token: '' };
      }
      if (key === 'jwt') {
        return testConfig.jwt;
      }
      return testConfig[key as keyof typeof testConfig];
    });

    const context = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          body: {
            fcid: 'test-fcid',
            token: 'any-token',
          },
        }),
      }),
    });

    expect(() => guard.canActivate(context)).toThrow(UnauthorizedException);
    expect(logger.warn).toHaveBeenCalledWith(
      'Slack token is not configured',
      'TokenValidationGuard',
    );
  });
});
