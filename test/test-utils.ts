import { faker } from '@faker-js/faker';
import axios, { AxiosResponse, AxiosError } from 'axios';

import {
  CreateUserInputDto,
  DeviceIdentifiersDto,
  UsersEndpointInputDTO,
  UsersEndpointOutputDTO,
  UserType,
} from '../src/users/user.dto';
import { config } from './config';

import type { User } from '../src/users/entities/user.entity';

// type UserResponse = User & {
//   isFreshInstall?: boolean;
//   newUser?: boolean;
// };

// Custom error class for API errors
class ApiError extends Error {
  response: {
    status: number;
    data: {
      message: string;
      statusCode: number;
    };
  };

  constructor(message: string, status: number) {
    super(message);
    this.response = {
      status,
      data: {
        message,
        statusCode: status,
      },
    };
  }
}

// Configure axios defaults
axios.defaults.baseURL = config.roshiUrl;
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.common['user-agent'] = 'Android/4.2.8 (756)';

// Add axios interceptor to handle circular references
axios.interceptors.request.use(
  config => {
    if (process.env.VERBOSE) {
      console.log('Making request to:', config.url);
      console.log('Request method:', config.method);
      console.log('Request headers:', JSON.stringify(config.headers, null, 2));
      console.log('Request data:', JSON.stringify(config.data, null, 2));
    }
    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  },
);

axios.interceptors.response.use(
  (response: AxiosResponse) => {
    if (process.env.VERBOSE) {
      console.log('Response status:', response.status);
      console.log('Response data:', JSON.stringify(response.data, null, 2));
    }
    // Create a clean response object
    const cleanResponse = {
      ...response,
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    };
    return cleanResponse;
  },
  (error: AxiosError) => {
    if (process.env.VERBOSE) {
      console.error('Response error:', error.message);
      if (error.response) {
        console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', JSON.stringify(error.response.headers, null, 2));
      }
    }
    // Create a clean error object
    const cleanError = {
      message: error.message,
      response: error.response
        ? {
            data: error.response.data,
            status: error.response.status,
            statusText: error.response.statusText,
            headers: error.response.headers,
          }
        : undefined,
    };
    return Promise.reject(cleanError);
  },
);

// Utility function to generate unique IDs
export const generateUUID = () => faker.string.uuid();
export const generateAdid = () => faker.string.hexadecimal({ length: 16 }).slice(2); // Remove the 0x at start
export const generateFcaid = () => faker.string.alphanumeric({ length: 10 });
// Utility function to delay execution
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const validateUsersEndpointResponse = (
  response: any,
  expectedStatus = 201,
): UsersEndpointOutputDTO => {
  expect(response.status).toBe(expectedStatus);
  expect(response.data.data).toBeDefined();
  return response.data.data;
};

// Mock data store for test mode
const mockDataStore = new Map<string, User>();

// Generate a valid UUID for idfv
const idfv = faker.string.uuid();
export class TestSetup {
  private static instance: TestSetup;
  private createdUsers: Set<string> = new Set();
  private jwtToken: string | null = null;
  private tokenExpiryTime: Date | null = null;

  private constructor() {
    // Private constructor for singleton pattern
  }

  static getInstance(): TestSetup {
    if (!TestSetup.instance) {
      TestSetup.instance = new TestSetup();
    }
    return TestSetup.instance;
  }

  private async ensureValidToken() {
    if (!this.jwtToken || !this.tokenExpiryTime || new Date() >= this.tokenExpiryTime) {
      try {
        await this.getInitialToken();
      } catch (error) {
        console.error('Token refresh failed:', error);
        await this.getInitialToken();
      }
    }
  }

  private async getInitialToken() {
    if (!config.apiAuthToken) {
      throw new Error('API_AUTH_TOKEN is required for live tests');
    }

    try {
      // First create a JWT token with both device IDs
      const response = await axios.post(
        `${config.roshiUrl}/auth/createJwt`,
        {
          idfv,
        },
        {
          headers: {
            'x-api-key': config.apiAuthToken,
            'Content-Type': 'application/json',
            'user-agent': 'Android/4.2.8 (756)',
          },
        },
      );

      if (!response.data?.data?.accessToken) {
        console.error(
          'Failed to get initial token. Response:',
          JSON.stringify(response.data, null, 2),
        );
        throw new Error('Failed to get initial token: No access token in response');
      }

      this.jwtToken = response.data.data.accessToken;
      this.tokenExpiryTime = new Date(response.data.data.accessTokenExpiry);
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.jwtToken}`;

      const userResponse = await axios.get(`${config.roshiUrl}/users?identifiers.idfv=${idfv}`);
      if (userResponse.data.data?.fcid) this.trackCreatedUser(userResponse.data.data.fcid);

      if (process.env.VERBOSE)
        console.log('Successfully obtained JWT token. Expiry:', this.tokenExpiryTime);
    } catch (error) {
      console.error('Failed to get initial JWT token:', {
        error: error.response?.data || error.message,
        status: error.response?.status,
        headers: error.response?.headers,
      });
      throw new Error('Failed to get initial JWT token for live tests');
    }
  }

  async setup() {
    // Don't override TEST_ENV if it's already set
    if (!process.env.TEST_ENV) {
      process.env.TEST_ENV = 'test';
    }

    // Set up JWT token based on environment
    if (process.env.TEST_ENV === 'test') {
      this.jwtToken = 'mock-jwt-token';
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.jwtToken}`;
    } else {
      await this.getInitialToken();
    }
  }

  async cleanup() {
    // In live environment, delete endpoint is disabled
    // if (process.env.TEST_ENV === 'live') {
    //   this.createdUsers.clear();
    //   return;
    // }

    // In test environment, attempt to delete users
    for (const userId of this.createdUsers) {
      try {
        await this.deleteUser(userId);
      } catch (error) {
        console.error(`Failed to delete user ${userId}:`, error);
      }
    }
    this.createdUsers.clear();
  }

  private async deleteUser(userId: string) {
    try {
      await axios.delete(`${config.roshiUrl}/users`, {
        data: {
          fcid: userId,
          token: process.env.SLACK_TOKEN,
        },
      });
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error.response?.data || error.message);
      throw error;
    }
  }

  public trackCreatedUser(fcid: string) {
    this.createdUsers.add(fcid);
  }

  async createUser(userData: UsersEndpointInputDTO): Promise<AxiosResponse<any>> {
    try {
      await this.ensureValidToken();

      const response = await this.retryOnRateLimit(async () => {
        const res = await axios.post('/users', userData);
        if (res.data.data?.fcid) {
          this.trackCreatedUser(res.data.data.fcid);
        }
        return res;
      });

      return response;
    } catch (error) {
      console.error('Error creating user:', error.response?.data || error.message);
      throw error;
    }
  }

  private async retryOnRateLimit<T>(fn: () => Promise<T>): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (error.response?.status === 429) {
        const retryAfter = parseInt(error.response.headers['retry-after'] || '60', 10);
        await delay(retryAfter * 1000);
        return this.retryOnRateLimit(fn);
      }
      throw error;
    }
  }

  async getUser(userId: string) {
    if (process.env.TEST_ENV === 'test') {
      const user = mockDataStore.get(userId);
      if (!user) {
        throw new ApiError('User not found', 404);
      }
      return { status: 200, data: { data: user } };
    }
    return axios.get(`/users/${userId}`);
  }
}

// Helper functions that use the singleton instance
export async function createUser(
  userData: CreateUserInputDto | UsersEndpointInputDTO,
  token?: string,
) {
  const { type, value } = extractAuthIdentifier(userData.identifiers || {});
  if (!type || !value) {
    throw new Error('Cannot create or update user: no valid device identifier found in userData.');
  }
  const accessToken = token || (await createJwtForIdentifier(type, value));
  const headers: Record<string, string> = { Authorization: `Bearer ${accessToken}` };
  const response = await axios.post(`${config.roshiUrl}/users`, userData, { headers });
  const testSetup = TestSetup.getInstance();
  if (response.data.data?.fcid) {
    testSetup.trackCreatedUser(response.data.data.fcid);
  }
  return response;
}

export async function getUser(userId: string) {
  const testSetup = TestSetup.getInstance();
  return testSetup.getUser(userId);
}

// Helper to create a JWT for a given idfv
export async function createJwtForIdfv(idfv: string): Promise<string> {
  if (!config.apiAuthToken) {
    throw new Error('API_AUTH_TOKEN is required for live tests');
  }
  const response = await axios.post(
    `${config.roshiUrl}/auth/createJwt`,
    { idfv },
    {
      headers: {
        'x-api-key': config.apiAuthToken,
        'Content-Type': 'application/json',
        'user-agent': 'Android/4.2.8 (756)',
      },
    },
  );
  if (!response.data?.data?.accessToken) {
    throw new Error('Failed to get access token for idfv: ' + idfv);
  }
  return response.data.data.accessToken;
}

// Track created users with their idfv
const createdUsersMap = new Map<string, { idfv: string }>();

// Enhanced createUser that stores idfv used
export async function createUserWithIdfv(userData: UsersEndpointInputDTO, idfv: string) {
  const accessToken = await createJwtForIdfv(idfv);
  const response = await axios.post(`${config.roshiUrl}/users`, userData, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  if (response.data.data?.fcid) {
    createdUsersMap.set(response.data.data.fcid, { idfv });
    TestSetup.getInstance().trackCreatedUser(response.data.data.fcid);
  }
  return response;
}

function extractAuthIdentifier(identifiers: DeviceIdentifiersDto): {
  type: 'idfv' | 'adid' | null;
  value: string | null;
} {
  if (identifiers) {
    if (identifiers.idfv && identifiers.idfv.length > 0) {
      return { type: 'idfv', value: identifiers.idfv[0] };
    }
    if (identifiers.adid && identifiers.adid.length > 0) {
      return { type: 'adid', value: identifiers.adid[0] };
    }
  }
  return { type: null, value: null };
}

export async function createJwtForIdentifier(
  type: 'idfv' | 'adid',
  value: string,
): Promise<string> {
  if (!config.apiAuthToken) {
    throw new Error('API_AUTH_TOKEN is required for live tests');
  }
  const payload: any = {};
  payload[type] = value;
  const response = await axios.post(`${config.roshiUrl}/auth/createJwt`, payload, {
    headers: {
      'x-api-key': config.apiAuthToken,
      'Content-Type': 'application/json',
      'user-agent': 'Android/4.2.8 (756)',
    },
  });
  if (!response.data?.data?.accessToken) {
    throw new Error(`Failed to get access token for ${type}: ${value}`);
  }
  return response.data.data.accessToken;
}

// Helper to create JWT with optional FCID
export async function createJwtWithPayload(payload: {
  idfv?: string;
  adid?: string;
  fcid?: string;
}): Promise<{ accessToken: string; accessTokenExpiry: string }> {
  if (!config.apiAuthToken) {
    throw new Error('API_AUTH_TOKEN is required for live tests');
  }

  const response = await axios.post(`${config.roshiUrl}/auth/createJwt`, payload, {
    headers: {
      'x-api-key': config.apiAuthToken,
      'Content-Type': 'application/json',
      'user-agent': 'Android/4.2.8 (756)',
    },
  });

  if (!response.data?.data?.accessToken) {
    throw new Error('Failed to get access token from JWT creation');
  }

  return {
    accessToken: response.data.data.accessToken,
    accessTokenExpiry: response.data.data.accessTokenExpiry,
  };
}

// Helper to validate JWT creation response
export const validateJwtResponse = (
  response: any,
  expectedStatus = 201,
): { accessToken: string; accessTokenExpiry: string } => {
  expect(response.status).toBe(expectedStatus);
  expect(response.data).toBeDefined();
  expect(response.data.data).toBeDefined();
  expect(response.data.data.accessToken).toBeDefined();
  expect(response.data.data.accessTokenExpiry).toBeDefined();
  return response.data.data;
};

// Helper to test Redis connectivity (if needed for debugging)
export async function testRedisConnection(): Promise<boolean> {
  try {
    // This would require access to Redis client, which we don't have in E2E tests
    // For now, we'll assume Redis is working if JWT creation works
    const testIdfv = generateUUID();
    const response = await createJwtWithPayload({ idfv: testIdfv });
    return !!response.accessToken;
  } catch (error) {
    console.error('Redis connection test failed:', error);
    return false;
  }
}

// Helper to create multiple JWTs for load testing
export async function createMultipleJwts(
  count: number,
  deviceIdGenerator: () => { idfv?: string; adid?: string },
): Promise<Array<{ accessToken: string; accessTokenExpiry: string; deviceId: string }>> {
  const promises = Array.from({ length: count }, async () => {
    const deviceData = deviceIdGenerator();
    const deviceId = deviceData.idfv || deviceData.adid || '';
    const response = await createJwtWithPayload(deviceData);
    return { ...response, deviceId };
  });

  return Promise.all(promises);
}

// Helper to simulate user journey from JWT creation to user creation
export async function simulateUserJourney(deviceData: {
  idfv?: string;
  adid?: string;
  fcid?: string;
}): Promise<{
  jwtResponse: { accessToken: string; accessTokenExpiry: string };
  userResponse: any;
  user: any;
}> {
  // Step 1: Create JWT
  const jwtResponse = await createJwtWithPayload(deviceData);

  // Step 2: Create user
  const userData = {
    type: UserType.ANONYMOUS,
    identifiers: {
      ...(deviceData.idfv && { idfv: [deviceData.idfv] }),
      ...(deviceData.adid && { adid: [deviceData.adid] }),
    },
    installed_at: new Date(),
  };

  const userResponse = await axios.post(`${config.roshiUrl}/users`, userData, {
    headers: {
      Authorization: `Bearer ${jwtResponse.accessToken}`,
    },
  });

  const user = validateUsersEndpointResponse(userResponse);

  // Track for cleanup
  if (user.fcid) {
    TestSetup.getInstance().trackCreatedUser(user.fcid);
  }

  return { jwtResponse, userResponse, user };
}
