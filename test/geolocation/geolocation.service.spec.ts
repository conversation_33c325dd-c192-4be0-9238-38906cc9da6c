import { Test, TestingModule } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';

import { GeolocationService } from '../../src/geolocation/geolocation.service';
import { KwsService } from '../../src/kws/kws.service';
import { KwsAgeGateResponse } from '../../src/kws/interfaces/kws-response.interface';
import { PostgresUserService } from '../../src/common/services/postgres-user.service';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';

describe('GeolocationService', () => {
  let service: GeolocationService;
  let postgresUserService: PostgresUserService;
  let geoQueue: any;
  let dlqQueue: any;
  let kwsService: KwsService;
  let errorLogger: ErrorLoggerService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GeolocationService,
        {
          provide: PostgresUserService,
          useValue: {
            findByFcid: jest.fn().mockResolvedValue({
              fcid: 'test-fcid',
              properties: {},
            }),
            findByAttribute: jest.fn().mockResolvedValue({
              fcid: 'test-fcid',
              properties: {},
            }),
            updateUser: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: getQueueToken('geolocation'),
          useValue: {
            add: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: getQueueToken('geolocation-dlq'),
          useValue: {
            add: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation(key => {
              if (key === 'geolocation') {
                return {
                  fallbackIp: '*******',
                  skipLocalIps: true,
                };
              }
              if (key === 'redis') {
                return {
                  queue: {
                    defaultJobOptions: {
                      attempts: 3,
                      backoff: {
                        type: 'exponential',
                        delay: 1000,
                      },
                    },
                  },
                };
              }
              return null;
            }),
          },
        },
        {
          provide: KwsService,
          useValue: {
            getAgeGateData: jest.fn().mockImplementation(async params => {
              if (params.ip === '***********' || params.ip === '*******') {
                return {
                  country: 'US',
                  region: 'CA',
                  consentAge: 13,
                  userAge: 18,
                  underAgeOfDigitalConsent: false,
                } as KwsAgeGateResponse;
              } else if (params.ip === 'invalid-ip') {
                throw new Error('Invalid IP address');
              } else if (params.ip === 'empty-response') {
                return null;
              } else {
                return {
                  country: 'FR',
                  region: 'IDF',
                  consentAge: 15,
                  userAge: 20,
                  underAgeOfDigitalConsent: false,
                } as KwsAgeGateResponse;
              }
            }),
          },
        },
        {
          provide: ErrorLoggerService,
          useValue: { logError: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<GeolocationService>(GeolocationService);
    postgresUserService = module.get<PostgresUserService>(PostgresUserService);
    geoQueue = module.get(getQueueToken('geolocation'));
    dlqQueue = module.get(getQueueToken('geolocation-dlq'));
    kwsService = module.get<KwsService>(KwsService);
    errorLogger = module.get<ErrorLoggerService>(ErrorLoggerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('queueGeolocation', () => {
    it('should add a job to the geolocation queue with fallback IP for local IPs', async () => {
      const fcid = 'test-fcid';
      const localIp = '***********';

      await service.queueGeolocation(fcid, localIp);

      expect(geoQueue.add).toHaveBeenCalledWith(
        'process-geolocation',
        { fcid, ip: '*******' }, // Should use fallback IP
        expect.any(Object),
      );
    });

    it('should use the original IP for non-local IPs', async () => {
      const fcid = 'test-fcid';
      const publicIp = '***********'; // From TEST-NET-3 block for documentation

      await service.queueGeolocation(fcid, publicIp);

      expect(geoQueue.add).toHaveBeenCalledWith(
        'process-geolocation',
        { fcid, ip: publicIp }, // Should use original IP
        expect.any(Object),
      );
    });
  });

  describe('processGeolocation', () => {
    it('should process geolocation and update user country', async () => {
      const fcid = 'test-fcid';
      const ip = '***********';
      const country = 'United States of America';

      await service.processGeolocation({ fcid, ip });

      // Verify KWS service call
      expect(kwsService.getAgeGateData).toHaveBeenCalledWith({
        ip,
      });

      // Verify user update
      expect(postgresUserService.updateUser).toHaveBeenCalledWith(
        fcid,
        expect.objectContaining({
          properties: { countryKws: country },
        }),
      );
    });

    it('should use default country when API returns invalid response', async () => {
      const fcid = 'test-fcid';
      const ip = 'empty-response';

      // This should now return 'Unknown' instead of throwing
      const result = await (service as any).getCountryFromIp(ip);

      // Verify result is the default country
      expect(result).toBe('Unknown');

      // Verify user update with default country
      expect(postgresUserService.updateUser).not.toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      const fcid = 'test-fcid';
      const ip = 'invalid-ip';

      await expect(service.processGeolocation({ fcid, ip })).rejects.toThrow('Invalid IP address');
    });

    it('should convert country codes to country names', async () => {
      const fcid = 'test-fcid';
      const ip = '***********'; // Will return FR from mock

      await service.processGeolocation({ fcid, ip });

      // Verify user update with converted country name
      expect(postgresUserService.updateUser).toHaveBeenCalledWith(
        fcid,
        expect.objectContaining({
          properties: { countryKws: 'France' },
        }),
      );
    });
  });

  describe('processGeolocationWithFallback', () => {
    it('should update user with default country', async () => {
      const fcid = 'test-fcid';
      const ip = '***********';

      await service.processGeolocationWithFallback({ fcid, ip });

      // Verify user update with default country
      expect(postgresUserService.updateUser).toHaveBeenCalledWith(
        fcid,
        expect.objectContaining({
          properties: { countryKws: 'Unknown' },
        }),
      );
    });
  });

  describe('addToDlq', () => {
    it('should add a failed job to the DLQ', async () => {
      const fcid = 'test-fcid';
      const ip = '***********';
      const error = new Error('Test error');

      await service.addToDlq({ fcid, ip }, error);

      expect(dlqQueue.add).toHaveBeenCalledWith(
        'process-dlq',
        expect.objectContaining({
          fcid,
          ip,
          error: expect.objectContaining({
            message: 'Test error',
          }),
        }),
        expect.any(Object),
      );
    });
  });

  describe('isLocalIp', () => {
    it('should correctly identify local IPv4 addresses', () => {
      // Access private method for testing
      const isLocalIp = (service as any).isLocalIp.bind(service);

      // Local IPs should return true
      expect(isLocalIp('127.0.0.1')).toBe(true);
      expect(isLocalIp('********')).toBe(true);
      expect(isLocalIp('**********')).toBe(true);
      expect(isLocalIp('***********')).toBe(true);
      expect(isLocalIp('::ffff:127.0.0.1')).toBe(true);

      // Public IPs should return false
      expect(isLocalIp('*******')).toBe(false);
      expect(isLocalIp('***********')).toBe(false);
      expect(isLocalIp('2001:db8::1')).toBe(false);
    });
  });
});
