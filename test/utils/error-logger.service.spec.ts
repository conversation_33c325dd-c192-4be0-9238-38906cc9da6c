import { Test, TestingModule } from '@nestjs/testing';
import { Request } from 'express';
import { ErrorLoggerService } from '../../src/common/services/error-logger.service';
import { WinstonLoggerService } from '../../src/common/services/winston-logger.service';

describe('ErrorLoggerService', () => {
  let service: ErrorLoggerService;
  let mockWinstonLogger: jest.Mocked<WinstonLoggerService>;

  beforeEach(async () => {
    const mockWinstonLoggerService = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ErrorLoggerService,
        {
          provide: WinstonLoggerService,
          useValue: mockWinstonLoggerService,
        },
      ],
    }).compile();

    service = module.get<ErrorLoggerService>(ErrorLoggerService);
    mockWinstonLogger = module.get(WinstonLoggerService);
  });

  describe('logError with user agent', () => {
    it('should include parsed user agent information when available', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {
          'user-agent': 'Android/7.2.3 (757)',
        },
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logError(error, mockRequest, { includeUserAgent: true });

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          userAgent: {
            family: 'Android',
            appVersion: '7.2.3',
            buildNumber: '757',
          },
        }),
      );
    });

    it('should include iOS user agent information', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {
          'user-agent': 'iOS/4.2.12 (757)',
        },
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logError(error, mockRequest, { includeUserAgent: true });

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          userAgent: {
            family: 'iOS',
            appVersion: '4.2.12',
            buildNumber: '757',
          },
        }),
      );
    });

    it('should handle Vegeta user agent', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {
          'user-agent': 'Vegeta',
        },
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logError(error, mockRequest, { includeUserAgent: true });

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          userAgent: {
            family: 'Vegeta',
            appVersion: null,
            buildNumber: null,
          },
        }),
      );
    });

    it('should not include user agent when includeUserAgent is false', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {
          'user-agent': 'Android/7.2.3 (757)',
        },
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logError(error, mockRequest, { includeUserAgent: false });

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.not.objectContaining({
          userAgent: expect.anything(),
        }),
      );
    });

    it('should not include user agent when no user-agent header is present', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {},
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logError(error, mockRequest, { includeUserAgent: true });

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.not.objectContaining({
          userAgent: expect.anything(),
        }),
      );
    });

    it('should handle unknown user agent format', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {
          'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        },
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logError(error, mockRequest, { includeUserAgent: true });

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          userAgent: {
            family: 'Unknown',
            appVersion: null,
            buildNumber: null,
          },
        }),
      );
    });
  });

  describe('logErrorWithUserAgent convenience method', () => {
    it('should always include user agent information', () => {
      const error = new Error('Test error');
      const mockRequest = {
        headers: {
          'user-agent': 'Android/7.2.3 (757)',
        },
        method: 'POST',
        url: '/test',
        body: {},
        query: {},
      } as Request;

      service.logErrorWithUserAgent(error, mockRequest);

      expect(mockWinstonLogger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          userAgent: {
            family: 'Android',
            appVersion: '7.2.3',
            buildNumber: '757',
          },
        }),
      );
    });
  });
});
