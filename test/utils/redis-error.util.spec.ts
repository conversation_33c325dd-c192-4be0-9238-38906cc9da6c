import {
  isRedisError,
  isRedisConnectionError,
  isRedisTimeoutError,
} from '../../src/common/utils/redis-error.util';

describe('Redis Error Utilities', () => {
  describe('isRedisError', () => {
    it('should return true for Redis connection refused error', () => {
      const error = new Error('Connection refused') as any;
      error.code = 'ECONNREFUSED';

      expect(isRedisError(error)).toBe(true);
    });

    it('should return true for Redis timeout error', () => {
      const error = new Error('Connection timeout') as any;
      error.code = 'ETIMEDOUT';

      expect(isRedisError(error)).toBe(true);
    });

    it('should return true for Redis network error', () => {
      const error = new Error('Network error') as any;
      error.code = 'ENETUNREACH';

      expect(isRedisError(error)).toBe(true);
    });

    it('should return true for error with Redis in message', () => {
      const error = new Error('Redis server is down');

      expect(isRedisError(error)).toBe(true);
    });

    it('should return true for error with connection in message', () => {
      const error = new Error('Connection failed');

      expect(isRedisError(error)).toBe(true);
    });

    it('should return false for non-Redis errors', () => {
      const error = new Error('Database query failed');

      expect(isRedisError(error)).toBe(false);
    });

    it('should return false for null/undefined errors', () => {
      expect(isRedisError(null)).toBe(false);
      expect(isRedisError(undefined)).toBe(false);
    });

    it('should return true for ReplyError constructor', () => {
      const error = { constructor: { name: 'ReplyError' } };

      expect(isRedisError(error)).toBe(true);
    });

    it('should return true for AbortError constructor', () => {
      const error = { constructor: { name: 'AbortError' } };

      expect(isRedisError(error)).toBe(true);
    });
  });

  describe('isRedisConnectionError', () => {
    it('should return true for connection refused error', () => {
      const error = new Error('Connection refused') as any;
      error.code = 'ECONNREFUSED';

      expect(isRedisConnectionError(error)).toBe(true);
    });

    it('should return true for connection timeout error', () => {
      const error = new Error('Connection timeout') as any;
      error.code = 'ETIMEDOUT';

      expect(isRedisConnectionError(error)).toBe(true);
    });

    it('should return true for connection-related message', () => {
      const error = new Error('Connection failed');

      expect(isRedisConnectionError(error)).toBe(true);
    });

    it('should return false for non-connection Redis errors', () => {
      const error = new Error('Redis command failed');

      expect(isRedisConnectionError(error)).toBe(false);
    });
  });

  describe('isRedisTimeoutError', () => {
    it('should return true for timeout error code', () => {
      const error = new Error('Connection timeout') as any;
      error.code = 'ETIMEDOUT';

      expect(isRedisTimeoutError(error)).toBe(true);
    });

    it('should return true for timeout in message', () => {
      const error = new Error('Connection timeout');

      expect(isRedisTimeoutError(error)).toBe(true);
    });

    it('should return false for non-timeout errors', () => {
      const error = new Error('Connection refused') as any;
      error.code = 'ECONNREFUSED';

      expect(isRedisTimeoutError(error)).toBe(false);
    });
  });
});
