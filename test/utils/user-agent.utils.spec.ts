import { parseUserAgent, formatUserAgent } from '../../src/common/utils/user-agent.utils';

describe('UserAgent Utils', () => {
  describe('parseUserAgent', () => {
    it('should parse Android user agent correctly', () => {
      const result = parseUserAgent('Android/7.2.3 (757)');

      expect(result).toEqual({
        family: 'Android',
        appVersion: '7.2.3',
        buildNumber: '757',
      });
    });

    it('should parse iOS user agent correctly', () => {
      const result = parseUserAgent('iOS/4.2.12 (757)');

      expect(result).toEqual({
        family: 'iOS',
        appVersion: '4.2.12',
        buildNumber: '757',
      });
    });

    it('should handle Vegeta user agent', () => {
      const result = parseUserAgent('Vegeta');

      expect(result).toEqual({
        family: 'Vegeta',
        appVersion: null,
        buildNumber: null,
      });
    });

    it('should handle Android without version info', () => {
      const result = parseUserAgent('Android');

      expect(result).toEqual({
        family: 'Android',
        appVersion: null,
        buildNumber: null,
      });
    });

    it('should handle iOS without version info', () => {
      const result = parseUserAgent('iOS');

      expect(result).toEqual({
        family: 'iOS',
        appVersion: null,
        buildNumber: null,
      });
    });

    it('should handle unknown user agents', () => {
      const result = parseUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64)');

      expect(result).toEqual({
        family: 'Unknown',
        appVersion: null,
        buildNumber: null,
      });
    });

    it('should handle empty or null user agent', () => {
      expect(parseUserAgent('')).toEqual({
        family: 'Unknown',
        appVersion: null,
        buildNumber: null,
      });

      expect(parseUserAgent(null as any)).toEqual({
        family: 'Unknown',
        appVersion: null,
        buildNumber: null,
      });
    });
  });

  describe('formatUserAgent', () => {
    it('should format Android user agent correctly', () => {
      const result = formatUserAgent('Android/7.2.3 (757)');

      expect(result).toEqual({
        family: 'Android',
        app_ver: '7.2.3',
      });
    });

    it('should format iOS user agent correctly', () => {
      const result = formatUserAgent('iOS/4.2.12 (757)');

      expect(result).toEqual({
        family: 'iOS',
        app_ver: '4.2.12',
      });
    });

    it('should format Vegeta user agent correctly', () => {
      const result = formatUserAgent('Vegeta');

      expect(result).toEqual({
        family: 'Vegeta',
        app_ver: null,
      });
    });

    it('should format unknown user agent correctly', () => {
      const result = formatUserAgent('Unknown User Agent');

      expect(result).toEqual({
        family: 'Unknown',
        app_ver: null,
      });
    });
  });
});
