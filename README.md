![image](https://github.com/user-attachments/assets/c2b78656-4a1d-4f9d-ad99-6aa5cd985975)

# Roshi User Management Service

A robust service for managing user identities, device identifiers, and revenue metrics across multiple platforms. The service handles both anonymous and registered users, providing seamless identity management and tracking capabilities.

## Quick Start

1. **Documentation**: For detailed API usage and examples, refer to:
   - [API Documentation](docs/api-documentation.md)
   - [Validation Rules](docs/validation-rules.md)
   - [Geolocation Service](docs/geolocation/geolocation-service.md)
   - [Bull Queues](docs/queues/bull-queues.md)
   - [Dead Letter Queue System](docs/queues/dead-letter-queue.md)

## Requirements

- Node.js 18+
- Docker (for running DynamoDB locally via LocalStack)
- Postgres + pg_partman extension
- Valid authentication credentials
- Valid Github Personal Access Token (PAT) with package read access. Needed to access VB private libraries.

See the [API Documentation](docs/api-documentation.md) for detailed setup instructions.

### pg_partman setup

pg_partman is an extension to create and manage both time-based and number-based table partition sets. In this project, we use **hash partitioning** for the `device_ids` table to ensure high performance and scalability as the table grows to hundreds of millions of rows.

#### Partitioning Strategy

- The `device_ids` table is **partitioned by HASH on `device_id`** using 32 partitions. This distributes data evenly and ensures fast lookups by device.
- The table uses a **composite primary key**: (`device_id`, `id_type`, `fcid`). This enforces uniqueness for each device/user/type combination and is required by Postgres for partitioned tables.
- Partition management is handled by `pg_partman`.

#### Indexes on `device_ids`

To optimize for the most common query patterns, the following indexes are created (and automatically propagated to all partitions):

- `PRIMARY KEY (device_id, id_type, fcid)`: Ensures uniqueness and supports fast lookups by device, type, and user.
- `idx_device_ids_fcid (fcid)`: Fast lookup of all device IDs for a user.
- `idx_device_ids_device_id (device_id)`: Fast lookup of which user a device belongs to.
- `idx_device_ids_device_id_type (device_id, id_type)`: Fast lookup of a device by its value and type (most common for device-centric queries).
- `idx_device_ids_fcid_type (fcid, id_type)`: Fast lookup of all device IDs of a certain type for a user.
- `idx_device_ids_fcid_device_id (fcid, device_id)`: Fast existence checks or lookups by both user and device.

These indexes are chosen based on the query patterns in the service, ensuring that both device-centric and user-centric queries are efficient even at very large scale.

#### Ensuring pg_partman is available

- On local Postgres, you can install pg_partman as described below.
- On AWS RDS, you must enable the `pg_partman` extension via the RDS console or with superuser privileges. If you cannot enable it, you can still use native Postgres partitioning, but you will not have pg_partman's management features.

1. Clone the pg_partman GitHub repo

```bash
git clone https://github.com/pgpartman/pg_partman.git
cd pg_partman
```

2. Checkout the latest supported tag

```bash
git checkout 5.2-STABLE
```

3. Set your environment for Postgres

```bash
export PATH=/POSTGRES/16/bin:$PATH
export PG_CONFIG=/POSTGRES/16/bin/pg_config
```

4. Compile and Install

```bash
make
make build
```

This will place pg_partman.control, .sql files, and .so files into Postgres internal folders

### Local Development

0. mv `.env.sample` to `.env` use the provided credentials
1. Add `export GH_AUTH_PACKAGE_TOKEN=PAT` to `./.zshrc` or `./bashrc`
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure LocalStack for AWS Services

   The following configuration is needed for local development with DynamoDB:

   Create or update `~/.aws/config`:

   ```bash
   [profile localstack]
   region = us-east-1
   output = json
   ```

   Create or update `~/.aws/credentials`:

   ```bash
   [localstack]
   aws_access_key_id = test
   aws_secret_access_key = test
   ```

4. Startredis via docker

   ```bash
   docker compose up
   ```

5. Start the service:
   ```bash
   npm run start:dev
   ```

The service will be available at `http://localhost:3000`

### Develoment Env

To connect to RDS you need to run a local tunnel first

```nano ~/.ssh/config

```

Host roshi-dev
HostName ec2-44-203-0-146.compute-1.amazonaws.com
User ec2-user
IdentityFile ~/path-to-key.pem
LocalForward 5432 awseb-e-dnsz54vyiz-stack-awsebrdsdatabase-4hrgakkrpr00.crwfdeazvmx3.us-east-1.rds.amazonaws.com:5432

````

Now, you can just run: `ssh roshi-dev`

Connect to RDS Using DBeaver

1. Open DBeaver.
2. Click New Database Connection.
3. Select PostgreSQL (or MySQL if applicable).
4. Use the following settings:
   - Host: localhost
   - Port: 5432
   - Database: <your-db-name>
   - Username: <your-db-username>
   - Password: <your-db-password>

Click Test Connection → If successful, Save & Connect.

### Connect to Redis

To connect to Redis in AWS, you need to create a tunnel through the bastion host using AWS SSM:

1. Ensure the bastion host's security group has access to Redis (port 6379)

2. Start the port forwarding session:
```bash
aws ssm start-session \
    --target i-0c8b4d793e4a48b4a \
    --document-name AWS-StartPortForwardingSessionToRemoteHost \
    --parameters "host=[REDIS_ENDPOINT],portNumber=6379,localPortNumber=6379" \
    --profile dev
````

Replace `[REDIS_ENDPOINT]` with your ElastiCache endpoint (e.g., `master.flipaclip-dev-redis.XXXXX.use1.cache.amazonaws.com`)

3. In a new terminal, connect to Redis using redis-cli:

```bash
redis-cli --tls \
    --sni [REDIS_ENDPOINT] \
    -h localhost \
    -p 6379 \
    -a 'your_redis_password'
```

Note:

- Replace `[REDIS_ENDPOINT]` with your actual Redis endpoint
- Replace `your_redis_password` with the actual Redis auth token
- The bastion host must have network access to the Redis cluster (check security groups)

Now, you can just run: `ssh roshi-dev`

Connect to RDS Using DBeaver

1. Open DBeaver.
2. Click New Database Connection.
3. Select PostgreSQL (or MySQL if applicable).
4. Use the following settings:
   - Host: localhost
   - Port: 5432
   - Database: <your-db-name>
   - Username: <your-db-username>
   - Password: <your-db-password>

Click Test Connection → If successful, Save & Connect.

### Connect to Redis

To connect to Redis in AWS, you need to create a tunnel through the bastion host using AWS SSM:

1. Ensure the bastion host's security group has access to Redis (port 6379)

2. Start the port forwarding session:

```bash
aws ssm start-session \
    --target i-0c8b4d793e4a48b4a \
    --document-name AWS-StartPortForwardingSessionToRemoteHost \
    --parameters "host=[REDIS_ENDPOINT],portNumber=6379,localPortNumber=6379" \
    --profile dev
``
```
