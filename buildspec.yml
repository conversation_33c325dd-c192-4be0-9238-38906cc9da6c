version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - npm install -g npm@latest
      - npm install -g typescript
      - npm install -g @nestjs/cli

  pre_build:
    commands:
      - echo Installing dependencies...
      - npm install --include=dev
      # - echo Checking code quality...
      # - npm run lint || true

  build:
    commands:
      - echo Build started...
      - npm run build
      # - echo Running tests...
      # - npm run test || true

  post_build:
    commands:
      - echo Build completed
      - echo Copying necessary files...
      - mkdir -p dist/cert
      - cp package*.json dist/
      - cp -r cert/* dist/cert/
      - echo Running migrations...
      - npm run migration:run

artifacts:
  files:
    - dist/**/*
    - node_modules/**/*
    - package*.json
    - cert/**/*
  base-directory: '.'
  discard-paths: no

cache:
  paths:
    - node_modules/**/*
    - ~/.npm/**/*