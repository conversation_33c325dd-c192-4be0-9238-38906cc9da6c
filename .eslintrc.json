{"parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "plugins": ["@typescript-eslint/eslint-plugin"], "extends": ["plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "root": true, "env": {"node": true, "jest": true}, "rules": {"@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "indent": ["off", 2], "no-tabs": ["error"], "prettier/prettier": ["error", {"tabWidth": 2, "useTabs": false}]}}