# Design Document

## Overview

This design outlines the refactoring of the PostgresUserService by extracting utility functions and helper methods into a separate PostgresUtils class. The current service has grown to over 1000 lines with mixed responsibilities. The refactoring will improve maintainability, testability, and code organization while preserving all existing functionality.

## Architecture

### Current State
- Single large PostgresUserService class (~1000+ lines)
- Mixed responsibilities: core operations, caching, validation, metrics, batch operations
- Private utility methods embedded within the service class
- Difficult to test individual utility functions in isolation

### Target State
- Lean PostgresUserService focused on core user operations
- Separate PostgresUtils class containing extracted utility functions
- Clear separation of concerns with grouped functionality
- Improved testability with isolated utility functions

## Components and Interfaces

### PostgresUtils Class

The new PostgresUtils class will be organized into the following functional groups:

#### 1. Data Transformation Utilities
```typescript
export class PostgresUtils {
  // Object manipulation
  static removeNullValues<T extends object>(obj: T): Partial<T>
  
  // Device identifier processing
  static createDeviceIdEntries(identifiers?: Partial<DeviceIdentifiers>): string[]
  
  // User data merging
  static mergeUserIdentifiers(existingUser: User, newUser: CreateUserDto | UserResponseDto): User
  
  // FCID generation
  static generateFcid(existingFcid?: string | null): string
}
```

#### 2. Validation Utilities
```typescript
export class PostgresValidationUtils {
  // FCID validation
  static validateFcid(fcid: string): boolean
  
  // FCID sanitization
  static sanitizeFcid(fcid: string): string | null
}
```

#### 3. Cache Key Utilities
```typescript
export class PostgresCacheUtils {
  // Standard cache key generation
  static getCacheKey(prefix: string, attribute: string, value: string): string
  
  // Merged user cache key generation
  static getMergedUserCacheKey(prefix: string, fcid: string): string
}
```

#### 4. Performance Tracking Utilities
```typescript
export class PostgresMetricsUtils {
  // Query performance tracking
  static async trackQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    logger: Logger,
    errorLogger: ErrorLoggerService,
    options?: TrackingOptions
  ): Promise<T>
  
  // Metrics data processing
  static processMetricsData(metrics: MergedUserQueryMetrics): ProcessedMetrics
}
```

### Updated PostgresUserService

The refactored service will:
- Import and use the utility classes
- Focus on core database operations and business logic
- Maintain the same public interface
- Delegate utility operations to the appropriate utility classes

```typescript
@Injectable()
export class PostgresUserService {
  // Core user operations
  async createUser(createUserDto: CreateUserDto): Promise<User>
  async updateUser(fcid: string, updateUserDto: UserResponseDto): Promise<User>
  async deleteUser(identifier: string): Promise<void>
  async findMergedUser(fcid: string): Promise<User | null>
  
  // Database query methods
  async findByAttribute(attribute: keyof User, value: string): Promise<User | null>
  async findAnonUser(value: string): Promise<User | null>
  
  // Cache management methods (using utility classes)
  private async cacheUser(user: User): Promise<void>
  private async invalidateUserCache(user: User): Promise<void>
}
```

## Data Models

### Interfaces to Extract
```typescript
// Move to separate interfaces file
export interface UserUpdate {
  fcid: string;
  device_ids?: string[];
  merged_fcids?: string[];
  properties?: Record<string, any>;
  updated_at?: Date;
}

export interface MergedUserQueryMetrics {
  executionTime: number;
  cacheHit: boolean;
  resultFound: boolean;
  inputValidation: boolean;
}

export interface TrackingOptions {
  cacheHit?: boolean;
  cacheKey?: string;
  inputValidation?: boolean;
  resultFound?: boolean;
}
```

### Constants to Extract
```typescript
// Move to constants file
export const POSTGRES_CONSTANTS = {
  CACHE_TTL: 1800,
  NEGATIVE_CACHE_TTL: 60,
  CACHE_PREFIX: 'user:',
  MERGED_USER_CACHE_TTL: 300,
  MERGED_USER_NEGATIVE_CACHE_TTL: 60,
  MERGED_USER_CACHE_PREFIX: 'merged_user:',
  BATCH_SIZE: 100,
  UPDATE_QUEUE_KEY: 'user-update-queue',
  MERGED_USER_METRICS_KEY: 'merged_user_metrics'
} as const;
```

## Error Handling

### Utility Function Error Handling
- Static utility functions will not handle errors internally
- Error handling responsibility remains with the calling service
- Validation functions return boolean/null values for invalid inputs
- Data transformation functions assume valid inputs or return safe defaults

### Service Error Handling
- Existing error handling patterns will be preserved
- Error logging will continue to use the ErrorLoggerService
- Utility function failures will be handled at the service level

## Testing Strategy

### Unit Tests for PostgresUtils
```typescript
describe('PostgresUtils', () => {
  describe('removeNullValues', () => {
    // Test null/undefined removal
    // Test preservation of valid values
    // Test empty object handling
  });
  
  describe('createDeviceIdEntries', () => {
    // Test with various identifier combinations
    // Test with empty/null identifiers
    // Test array flattening
  });
  
  describe('mergeUserIdentifiers', () => {
    // Test identifier merging logic
    // Test immutable field preservation
    // Test type changes based on fcaid
  });
});
```

### Integration Tests
- Existing PostgresUserService tests will be updated to work with the refactored structure
- New tests will verify that utility functions integrate correctly
- Performance tests will ensure no regression in query performance

### Test Coverage Requirements
- Maintain existing test coverage percentage
- Add comprehensive unit tests for extracted utility functions
- Ensure all edge cases are covered in utility function tests

## Migration Strategy

### Phase 1: Extract Utility Classes
1. Create PostgresUtils class with static methods
2. Create separate interfaces file
3. Create constants file
4. Implement comprehensive unit tests for utilities

### Phase 2: Update PostgresUserService
1. Import utility classes
2. Replace private methods with utility class calls
3. Remove extracted private methods
4. Update existing tests to work with new structure

### Phase 3: Validation and Cleanup
1. Run full test suite to ensure no regressions
2. Verify performance benchmarks
3. Update documentation
4. Remove any unused imports or code

## Performance Considerations

### No Performance Impact
- Static utility functions have minimal overhead
- No additional object instantiation for utility calls
- Existing caching and optimization strategies preserved
- Database query patterns remain unchanged

### Potential Benefits
- Improved code organization may lead to better optimization opportunities
- Isolated utility functions easier to profile and optimize
- Reduced service class size may improve JIT compilation