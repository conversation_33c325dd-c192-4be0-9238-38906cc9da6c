# Implementation Plan

- [ ] 1. Create utility classes and interfaces
  - Create PostgresUtils class with data transformation utilities
  - Create PostgresValidationUtils class with validation methods
  - Create PostgresCacheUtils class with cache key generation methods
  - Create interfaces file for UserUpdate and MergedUserQueryMetrics
  - Create constants file for PostgreSQL-related constants
  - _Requirements: 2.1, 2.2, 3.1, 3.2, 3.3, 3.4_

- [ ] 2. Implement data transformation utilities
  - [ ] 2.1 Create removeNullValues static method
    - Write static method to filter null/undefined values from objects
    - Maintain generic typing for type safety
    - Write comprehensive unit tests covering edge cases
    - _Requirements: 2.3, 4.1, 4.4_

  - [ ] 2.2 Create createDeviceIdEntries static method
    - Write static method to extract device IDs from identifiers
    - Handle all device identifier types (idfa, idfv, gaid, adid)
    - Write unit tests for various identifier combinations
    - _Requirements: 2.3, 4.1, 4.4_

  - [ ] 2.3 Create mergeUserIdentifiers static method
    - Write static method to merge user identifiers while preserving immutable fields
    - Handle device ID merging and deduplication
    - Handle user type changes based on fcaid presence
    - Write comprehensive unit tests for merging logic
    - _Requirements: 2.3, 4.1, 4.4_

  - [ ] 2.4 Create generateFcid static method
    - Write static method to generate fcid using uuidv4 with random suffix
    - Handle existing fcid preservation
    - Write unit tests for fcid generation patterns
    - _Requirements: 2.3, 4.1, 4.4_

- [ ] 3. Implement validation utilities
  - [ ] 3.1 Create validateFcid static method
    - Write static method to validate fcid format using UUID pattern
    - Handle null, undefined, and empty string validation
    - Write unit tests for valid and invalid fcid formats
    - _Requirements: 2.3, 3.2, 4.1, 4.4_

  - [ ] 3.2 Create sanitizeFcid static method
    - Write static method to sanitize fcid by trimming and normalizing
    - Convert to lowercase for consistency
    - Write unit tests for sanitization edge cases
    - _Requirements: 2.3, 3.2, 4.1, 4.4_

- [ ] 4. Implement cache utilities
  - [ ] 4.1 Create getCacheKey static method
    - Write static method to generate standard cache keys
    - Accept prefix, attribute, and value parameters
    - Write unit tests for cache key generation
    - _Requirements: 2.3, 3.1, 4.1, 4.4_

  - [ ] 4.2 Create getMergedUserCacheKey static method
    - Write static method to generate merged user cache keys
    - Accept prefix and fcid parameters
    - Write unit tests for merged user cache key generation
    - _Requirements: 2.3, 3.1, 4.1, 4.4_

- [ ] 5. Create interfaces and constants files
  - [ ] 5.1 Create postgres-interfaces.ts file
    - Define UserUpdate interface with proper typing
    - Define MergedUserQueryMetrics interface
    - Define TrackingOptions interface for performance monitoring
    - _Requirements: 2.2, 2.4_

  - [ ] 5.2 Create postgres-constants.ts file
    - Extract all constants from PostgresUserService
    - Use const assertions for type safety
    - Group related constants logically
    - _Requirements: 2.2, 2.4, 3.1_

- [ ] 6. Update PostgresUserService to use utility classes
  - [ ] 6.1 Import utility classes and interfaces
    - Add imports for all utility classes
    - Add imports for interfaces and constants
    - Remove duplicate constant definitions
    - _Requirements: 1.1, 2.1, 2.3_

  - [ ] 6.2 Replace removeNullValues method calls
    - Replace private method calls with PostgresUtils.removeNullValues
    - Update all references in createUser and updateUser methods
    - Remove private removeNullValues method
    - _Requirements: 1.1, 1.3, 2.3_

  - [ ] 6.3 Replace createDeviceIdEntries method calls
    - Replace private method calls with PostgresUtils.createDeviceIdEntries
    - Update all references in user creation and merging logic
    - Remove private createDeviceIdEntries method
    - _Requirements: 1.1, 1.3, 2.3_

  - [ ] 6.4 Replace mergeUserIdentifiers method calls
    - Replace private method calls with PostgresUtils.mergeUserIdentifiers
    - Update all references in createUser and updateUser methods
    - Remove private mergeUserIdentifiers method
    - _Requirements: 1.1, 1.3, 2.3_

  - [ ] 6.5 Replace validation method calls
    - Replace validateFcid calls with PostgresValidationUtils.validateFcid
    - Replace sanitizeFcid calls with PostgresValidationUtils.sanitizeFcid
    - Remove private validation methods
    - _Requirements: 1.1, 1.3, 2.3_

  - [ ] 6.6 Replace cache key generation method calls
    - Replace getCacheKey calls with PostgresCacheUtils.getCacheKey
    - Replace getMergedUserCacheKey calls with PostgresCacheUtils.getMergedUserCacheKey
    - Update all cache-related operations
    - Remove private cache key methods
    - _Requirements: 1.1, 1.3, 2.3_

  - [ ] 6.7 Replace generateFcid method calls
    - Replace private method calls with PostgresUtils.generateFcid
    - Update all references in user creation logic
    - Remove private generateFcid method
    - _Requirements: 1.1, 1.3, 2.3_

- [ ] 7. Update existing tests
  - [ ] 7.1 Update PostgresUserService tests
    - Modify existing tests to work with refactored service structure
    - Ensure all test cases continue to pass
    - Update any test mocks or stubs as needed
    - _Requirements: 1.4, 4.2, 4.3_

  - [ ] 7.2 Add integration tests
    - Write tests to verify utility classes integrate correctly with service
    - Test that extracted functions work properly when called from service
    - Verify no functionality regression in integration scenarios
    - _Requirements: 4.1, 4.2, 4.3_

- [ ] 8. Verify refactoring completion
  - [ ] 8.1 Run full test suite
    - Execute all existing tests to ensure no regressions
    - Verify test coverage meets requirements
    - Fix any failing tests due to refactoring changes
    - _Requirements: 1.4, 4.2, 4.3_

  - [ ] 8.2 Verify line count reduction
    - Confirm PostgresUserService is under 800 lines
    - Verify utility classes are properly organized
    - Check that all functionality has been preserved
    - _Requirements: 1.1, 3.1, 3.2, 3.3, 3.4_

  - [ ] 8.3 Update imports and cleanup
    - Remove any unused imports from PostgresUserService
    - Ensure all utility class imports are properly added
    - Clean up any remaining dead code
    - _Requirements: 1.1, 2.1_