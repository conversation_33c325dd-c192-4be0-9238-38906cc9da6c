# Design Document

## Overview

The `findMergedUser` query optimization focuses on improving the performance of searching for users that contain a specific fcid in their merged_fcids array. Based on the existing performance analysis, this query currently averages 108.87ms per execution with 496,159 total executions, making it a significant performance bottleneck.

The optimization strategy involves multiple approaches: query pattern improvements, index utilization enhancements, caching strategies, and early validation optimizations.

## Architecture

### Current Implementation Analysis

The existing `findMergedUser` method uses:
- TypeORM QueryBuilder with array containment operator `@>`
- GIN index `idx_users_merged_fcids_optimized` 
- Type filtering for `REGISTERED` users only
- Performance tracking wrapper

**Current Query Pattern:**
```sql
SELECT * FROM users 
WHERE type = 'REGISTERED' 
AND merged_fcids @> ARRAY[$1]
```

### Optimization Strategy

The optimization will implement a multi-layered approach:

1. **Query Pattern Optimization**: Replace array containment with more efficient patterns
2. **Enhanced Index Utilization**: Ensure optimal index usage with query hints
3. **Input Validation**: Early return for invalid inputs
4. **Caching Layer**: Redis-based result caching for frequent lookups
5. **Monitoring Enhancement**: Improved performance tracking

## Components and Interfaces

### 1. Enhanced Query Patterns

#### Option A: EXISTS with UNNEST (Recommended)
```sql
SELECT * FROM users u
WHERE u.type = 'REGISTERED'
AND EXISTS (
  SELECT 1 FROM unnest(u.merged_fcids) AS merged_fcid 
  WHERE merged_fcid = $1
)
```

**Benefits:**
- Better query planner optimization
- More efficient for sparse arrays
- Consistent performance across array sizes

#### Option B: Optimized Array Containment with Hints
```sql
/*+ IndexScan(users idx_users_merged_fcids_optimized) */
SELECT * FROM users 
WHERE type = 'REGISTERED' 
AND merged_fcids @> ARRAY[$1]::text[]
```

**Benefits:**
- Explicit index usage
- Maintains existing query pattern
- Type casting for better performance

#### Option C: Composite Index Query
```sql
SELECT * FROM users 
WHERE (type, merged_fcids) = ('REGISTERED', merged_fcids)
AND merged_fcids @> ARRAY[$1]
```

**Benefits:**
- Leverages composite indexing
- Reduces index scan overhead

### 2. Caching Strategy

#### Cache Key Structure
```typescript
interface MergedUserCacheKey {
  prefix: 'merged_user';
  fcid: string;
}

// Example: "merged_user:fcid123-456-789"
```

#### Cache Implementation
```typescript
interface CachedMergedUserResult {
  user: User | null;
  timestamp: number;
  ttl: number;
}
```

**Cache TTL Strategy:**
- Positive results: 300 seconds (5 minutes)
- Negative results: 60 seconds (1 minute)
- Cache invalidation on user updates affecting merged_fcids

### 3. Input Validation Layer

```typescript
interface InputValidation {
  validateFcid(fcid: string): boolean;
  sanitizeFcid(fcid: string): string;
  isValidFormat(fcid: string): boolean;
}
```

**Validation Rules:**
- Non-empty string validation
- UUID format validation (if applicable)
- Length constraints
- Character set validation

### 4. Enhanced Performance Tracking

```typescript
interface QueryMetrics {
  queryName: string;
  executionTime: number;
  cacheHit: boolean;
  resultFound: boolean;
  inputValidation: boolean;
}
```

## Data Models

### Enhanced User Query Result

```typescript
interface OptimizedMergedUserResult {
  user: User | null;
  metadata: {
    executionTime: number;
    cacheUsed: boolean;
    indexUsed: string;
    queryPattern: string;
  };
}
```

### Performance Metrics Model

```typescript
interface MergedUserQueryMetrics {
  totalQueries: number;
  averageExecutionTime: number;
  cacheHitRate: number;
  slowQueryCount: number;
  indexUsageRate: number;
}
```

## Error Handling

### Input Validation Errors

```typescript
class InvalidFcidError extends Error {
  constructor(fcid: string) {
    super(`Invalid fcid format: ${fcid}`);
    this.name = 'InvalidFcidError';
  }
}
```

### Database Connection Errors

```typescript
interface DatabaseErrorHandling {
  retryStrategy: {
    maxRetries: 3;
    backoffMs: [100, 500, 1000];
  };
  fallbackBehavior: 'return_null' | 'throw_error';
  circuitBreaker: {
    failureThreshold: 5;
    resetTimeoutMs: 30000;
  };
}
```

### Cache Errors

```typescript
interface CacheErrorHandling {
  onCacheFailure: 'proceed_to_db' | 'return_cached_fallback';
  cacheTimeoutMs: 100;
  fallbackToDatabase: true;
}
```

## Testing Strategy

### Unit Tests

1. **Query Pattern Tests**
   - Test each query pattern with various input scenarios
   - Verify correct SQL generation
   - Test edge cases (empty arrays, null values)

2. **Caching Tests**
   - Cache hit/miss scenarios
   - Cache invalidation logic
   - TTL expiration handling

3. **Input Validation Tests**
   - Valid/invalid fcid formats
   - Edge cases (empty strings, special characters)
   - Performance impact of validation

### Integration Tests

1. **Database Performance Tests**
   - Query execution time benchmarks
   - Index usage verification
   - Concurrent query handling

2. **Cache Integration Tests**
   - Redis connectivity
   - Cache consistency
   - Failover scenarios

### Performance Tests

1. **Load Testing**
   - High-volume query scenarios
   - Concurrent user lookups
   - Memory usage under load

2. **Benchmark Comparisons**
   - Before/after optimization metrics
   - Different query pattern performance
   - Cache effectiveness measurement

### Test Data Requirements

```typescript
interface TestDataSet {
  users: {
    withMergedFcids: User[];
    withoutMergedFcids: User[];
    registeredUsers: User[];
    anonymousUsers: User[];
  };
  fcids: {
    existing: string[];
    nonExisting: string[];
    invalid: string[];
  };
}
```

## Implementation Phases

### Phase 1: Query Pattern Optimization
- Implement EXISTS with UNNEST query pattern
- Add input validation layer
- Enhance performance tracking

### Phase 2: Caching Implementation
- Add Redis-based result caching
- Implement cache invalidation logic
- Add cache performance metrics

### Phase 3: Advanced Optimizations
- Implement query hints if needed
- Add circuit breaker pattern
- Optimize for specific usage patterns

### Phase 4: Monitoring and Alerting
- Add comprehensive metrics collection
- Implement performance alerting
- Create optimization dashboard

## Performance Targets

### Query Execution Time
- **Target**: <10ms for 95% of queries
- **Acceptable**: <50ms for 99% of queries
- **Alert Threshold**: >100ms

### Cache Performance
- **Hit Rate Target**: >80%
- **Cache Response Time**: <5ms
- **Cache Memory Usage**: <100MB

### System Impact
- **CPU Reduction**: 50% reduction in CPU usage for this query
- **Memory Usage**: <10% increase due to caching
- **Throughput**: 3x improvement in queries per second

## Monitoring and Metrics

### Key Performance Indicators

1. **Query Performance**
   - Average execution time
   - 95th percentile execution time
   - Slow query count (>50ms)

2. **Cache Effectiveness**
   - Cache hit rate
   - Cache miss rate
   - Cache invalidation frequency

3. **System Health**
   - Database connection pool usage
   - Memory consumption
   - Error rate

### Alerting Thresholds

- Query time >100ms: Warning
- Query time >500ms: Critical
- Cache hit rate <70%: Warning
- Error rate >1%: Critical

## Migration Strategy

### Database Changes
- No schema changes required
- Existing indexes will be utilized
- Optional: Create additional composite index if needed

### Application Changes
- Backward compatible implementation
- Feature flag for gradual rollout
- A/B testing capability for query patterns

### Rollback Plan
- Immediate rollback capability
- Performance monitoring during deployment
- Automated rollback triggers based on metrics