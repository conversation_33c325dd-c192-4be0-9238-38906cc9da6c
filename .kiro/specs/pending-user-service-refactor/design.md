# Design Document

## Overview

The Pending User Service Refactor introduces a new architecture that moves pending users from database storage to Redis-only storage, implements stricter business rules for user creation, and provides a dedicated service that can be easily extracted into a separate library. The design addresses performance bottlenecks in the current buma-based implementation while ensuring data consistency and integrity.

## Architecture

### Current Architecture Issues
- Pending users are stored in PostgreSQL database, causing unnecessary database load
- The buma library creates complexity with mixed storage patterns
- Business rules for device ID and FCID validation are scattered across multiple services
- Concurrent user creation can lead to race conditions and duplicate users

### New Architecture
The new system follows Clean Architecture principles with clear separation of concerns and introduces a dedicated `PendingUserService` that:
- Stores pending users exclusively in Redis with configurable TTL
- Implements atomic operations using Redis distributed locks
- Enforces strict business rules for user lookup and creation
- Provides clean interfaces for future library extraction
- Maintains backward compatibility with existing JWT creation flow
- Leverages Redis for handling duplicate requests from mobile apps (iOS/Android) that may retry token creation due to network conditions

## Components and Interfaces

### Clean Architecture Layers

#### Domain Layer (Core Business Logic)
- **Entities**: PendingUser, UserForJwt
- **Use Cases**: FindOrCreateUserForJwtUseCase, CreatePendingUserUseCase, TransitionPendingUserUseCase
- **Repository Interfaces**: IPendingUserRepository, IUserRepository
- **Business Rules**: DeviceIdUniquenessRule, FcidValidationRule

#### Application Layer (Orchestration)
- **Services**: PendingUserService (orchestrates use cases)
- **DTOs**: JwtCreationDto, PendingUserDto
- **Interfaces**: IPendingUserService

#### Infrastructure Layer (External Dependencies)
- **Repositories**: RedisPendingUserRepository, PostgresUserRepository
- **Adapters**: RedisAdapter, DatabaseAdapter
- **External Services**: RedisService, PostgresUserService

#### Presentation Layer (Controllers)
- **Controllers**: AuthController (modified to use new service)
- **Validation**: Input validation and sanitization

### PendingUserService (Application Layer)
The core service responsible for orchestrating pending user operations:

```typescript
interface IPendingUserService {
  // Main JWT creation flow - handles mobile app retry scenarios
  findOrCreateUserForJwt(dto: JwtCreationDto): Promise<UserForJwt>;
  
  // Pending user management
  createPendingUser(deviceId: string, identifiers?: DeviceIdentifiers): Promise<PendingUser>;
  findPendingUser(deviceId: string): Promise<PendingUser | null>;
  removePendingUser(deviceId: string): Promise<void>;
  
  // Transition operations
  transitionPendingToAnonymous(fcid: string, userData: CreateUserDto): Promise<User>;
}
```

### Use Cases (Domain Layer)
```typescript
interface IFindOrCreateUserForJwtUseCase {
  execute(dto: JwtCreationDto): Promise<UserForJwt>;
}

interface ICreatePendingUserUseCase {
  execute(deviceId: string, identifiers?: DeviceIdentifiers): Promise<PendingUser>;
}

interface ITransitionPendingUserUseCase {
  execute(fcid: string, userData: CreateUserDto): Promise<User>;
}
```

### Repository Interfaces (Domain Layer)
```typescript
interface IPendingUserRepository {
  findByDeviceId(deviceId: string): Promise<PendingUser | null>;
  findByFcid(fcid: string): Promise<PendingUser | null>;
  save(pendingUser: PendingUser): Promise<void>;
  remove(deviceId: string): Promise<void>;
  exists(deviceId: string): Promise<boolean>;
}

interface IUserRepository {
  findByDeviceId(deviceId: string): Promise<User | null>;
  findByFcid(fcid: string): Promise<User | null>;
  findAnonUser(deviceId: string): Promise<User | null>;
  findUniqueUser(fcid: string, deviceId: string): Promise<User | null>;
}
```

### Data Models

#### PendingUser (Redis-only)
```typescript
interface PendingUser {
  fcid: string;                    // Deterministic UUID
  deviceId: string;                // Primary device identifier
  identifiers: DeviceIdentifiers;  // All known device identifiers
  createdAt: Date;                 // Creation timestamp
  ttl: number;                     // Time to live in seconds
}
```

#### JwtCreationDto
```typescript
interface JwtCreationDto {
  deviceId: string;    // idfv or adid
  fcid?: string;       // Optional provided FCID
  idfv?: string;       // iOS identifier for vendor
  adid?: string;       // Android advertising ID
}
```

#### UserForJwt
```typescript
interface UserForJwt {
  fcid: string;
  type: 'ANONYMOUS' | 'REGISTERED' | 'PENDING';
  source: 'database' | 'redis';
}
```

### Business Rule Engine
A dedicated component that enforces the strict business rules:

```typescript
interface BusinessRuleEngine {
  validateDeviceIdUniqueness(deviceId: string): Promise<ValidationResult>;
  validateFcidAndDeviceId(fcid: string, deviceId: string): Promise<ValidationResult>;
  enforceOneUserPerDevice(deviceId: string): Promise<void>;
}
```

### Redis Storage Adapter
Handles all Redis operations for pending users:

```typescript
interface PendingUserRedisAdapter {
  store(pendingUser: PendingUser): Promise<void>;
  retrieve(deviceId: string): Promise<PendingUser | null>;
  remove(deviceId: string): Promise<void>;
  exists(deviceId: string): Promise<boolean>;
  acquireLock(key: string, ttlMs: number): Promise<boolean>;
  releaseLock(key: string): Promise<void>;
}
```

## Data Models

### Redis Storage Schema
Pending users are stored in Redis with the following key patterns:

- **Pending User Data**: `pending:user:{deviceId}` → JSON serialized PendingUser
- **FCID Mapping**: `pending:fcid:{fcid}` → deviceId (for reverse lookups)
- **Lock Keys**: `pending:lock:{deviceId}` → temporary lock for atomic operations

### TTL Strategy
- Default TTL: 24 hours for pending users
- Lock TTL: 5 seconds for atomic operations
- Configurable via environment variables

### Database Constraints
The system enforces that for any given device ID:
- Maximum 1 ANONYMOUS user OR 1 PENDING user exists in PostgreSQL
- Never both ANONYMOUS and PENDING for the same device ID
- Monitoring queries to detect and alert on violations

## Error Handling

### Redis Unavailability
When Redis is unavailable, the system falls back to:
1. Direct database lookup for existing users
2. Graceful degradation without pending user creation
3. Detailed error logging for monitoring

### Concurrent Access
- Distributed locks prevent race conditions during user creation
- Lock acquisition failures result in "user being created" errors
- Automatic lock cleanup prevents deadlocks
- Redis-based idempotency ensures mobile app retries don't create duplicate users
- Request deduplication using device ID + timestamp windows

### Data Consistency
- Atomic operations ensure pending users are created or not at all
- Cleanup processes remove orphaned data
- Monitoring detects and alerts on data inconsistencies

## Testing Strategy

### Unit Tests
- PendingUserService methods with mocked dependencies
- Business rule validation logic
- Redis adapter operations
- Error handling scenarios

### Integration Tests
- End-to-end JWT creation flow
- Redis storage and retrieval operations
- Database constraint enforcement
- Concurrent user creation scenarios

### Performance Tests
- Load testing with high concurrent user creation
- Redis performance under various TTL configurations
- Memory usage monitoring for Redis storage

### Monitoring Tests
- Data consistency validation queries
- Redis availability and performance metrics
- Business rule violation detection

## Migration Strategy

### Phase 1: Service Implementation
- Implement PendingUserService alongside existing buma implementation
- Add feature flag to control which implementation is used
- Comprehensive testing in development environment

### Phase 2: Gradual Rollout
- Deploy with feature flag disabled (using existing implementation)
- Enable for small percentage of traffic
- Monitor performance and error rates
- Gradually increase traffic percentage

### Phase 3: Full Migration
- Switch all traffic to new implementation
- Remove buma dependency and old code
- Clean up any remaining pending users in database

### Phase 4: Library Extraction
- Extract PendingUserService to separate npm package
- Update imports and dependencies
- Maintain backward compatibility during transition

## Performance Considerations

### Redis Optimization
- Use Redis pipelining for batch operations
- Implement connection pooling for high concurrency
- Monitor memory usage and implement cleanup processes

### Database Load Reduction
- Eliminate database writes for pending users
- Reduce database queries through better caching
- Optimize existing user lookup queries

### Caching Strategy
- Cache database user lookups in Redis
- Implement negative caching for non-existent users
- Use appropriate TTL values to balance performance and consistency

## Security Considerations

### Data Protection
- Pending users contain minimal PII (only device identifiers)
- Redis data is encrypted in transit and at rest
- Implement proper access controls for Redis

### Rate Limiting
- Implement rate limiting for pending user creation
- Prevent abuse through device ID enumeration
- Monitor for suspicious patterns

### Audit Logging
- Log all pending user operations for audit trails
- Include relevant context for debugging
- Implement log retention policies