# Requirements Document

## Introduction

The current pending user creation system uses the buma library and stores pending users directly in the database. This approach has limitations in terms of performance, data consistency, and maintainability. The new system will move pending users to Redis-only storage and implement stricter business rules to ensure data integrity. The system will enforce that only one user (either ANONYMOUS or PENDING) exists per device ID in the database, with pending users living temporarily in Redis until they become permanent users.

## Requirements

### Requirement 1

**User Story:** As a system architect, I want pending users to be stored in Redis only, so that we can improve performance and reduce database load for temporary user states.

#### Acceptance Criteria

1. WHEN a pending user is created THEN the system SHALL store it in Redis with a configurable TTL
2. WHEN a pending user is created THEN the system SHALL NOT store it in the PostgreSQL database
3. WHEN a pending user expires in Redis THEN the system SHALL handle the expiration gracefully
4. W<PERSON><PERSON> looking up pending users THEN the system SHALL query Redis first and only Redis
5. WHEN a pending user transitions from PENDING to ANONYMOUS THEN the system SHALL remove it from Redis

### Requirement 2

**User Story:** As a developer, I want the JWT creation flow to follow strict business rules for device ID and FCID handling, so that data consistency is maintained across the system.

#### Acceptance Criteria

1. WHEN /createJwt is called with only device ID THEN the system SHALL look for ANONYMOUS users first, then PENDING users
2. WHEN /createJwt is called with device ID + FCID THEN the system SHALL validate the FCID exists and contains the device ID
3. WHEN provided FCID doesn't exist THEN the system SHALL ignore the FCID and follow device-ID-only flow
4. WHEN provided FCID exists but doesn't contain the device ID THEN the system SHALL ignore the FCID and follow device-ID-only flow
5. WHEN provided FCID exists and contains the device ID THEN the system SHALL generate the token successfully

### Requirement 3

**User Story:** As a data integrity specialist, I want to enforce that only one user (ANONYMOUS or PENDING) exists per device ID in the database, so that data consistency is maintained.

#### Acceptance Criteria

1. WHEN searching for users by device ID THEN the system SHALL find at most one ANONYMOUS or one PENDING user, never both
2. WHEN a new user is created THEN the system SHALL verify no conflicting user exists for the same device ID
3. WHEN database cleanup runs THEN the system SHALL identify and resolve any violations of the one-user-per-device rule
4. WHEN monitoring checks run THEN the system SHALL report any violations of the one-user-per-device rule

### Requirement 4

**User Story:** As a developer, I want a dedicated service for pending user operations, so that the logic is encapsulated and can be easily moved to a separate library later.

#### Acceptance Criteria

1. WHEN the service is implemented THEN it SHALL be a standalone PendingUserService class
2. WHEN the service is used THEN it SHALL be easily embeddable in existing services
3. WHEN the service is designed THEN it SHALL have clear interfaces that support future library extraction
4. WHEN the service handles operations THEN it SHALL provide comprehensive error handling and logging

### Requirement 5

**User Story:** As a system operator, I want the new pending user system to be resilient and maintain backward compatibility, so that existing functionality continues to work during the transition.

#### Acceptance Criteria

1. WHEN Redis is unavailable THEN the system SHALL handle the failure gracefully with appropriate fallback behavior
2. WHEN the new service is deployed THEN existing JWT creation functionality SHALL continue to work
3. WHEN errors occur in pending user operations THEN the system SHALL log detailed error information
4. WHEN the system transitions from old to new implementation THEN no existing users SHALL be lost or corrupted

### Requirement 6

**User Story:** As a performance analyst, I want the new system to provide better performance than the current implementation, so that user creation operations are faster and more scalable.

#### Acceptance Criteria

1. WHEN pending users are created THEN the operation SHALL complete faster than the current database-based approach
2. WHEN multiple concurrent requests create pending users THEN the system SHALL handle them efficiently without conflicts
3. WHEN the system is under load THEN pending user operations SHALL not become a bottleneck
4. WHEN performance is measured THEN the new system SHALL show measurable improvements over the current implementation