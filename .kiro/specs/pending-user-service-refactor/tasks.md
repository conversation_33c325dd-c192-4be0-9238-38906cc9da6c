# Implementation Plan

- [x] 1. Create domain layer entities and interfaces

  - Implement PendingUser entity with proper TypeScript types
  - Create UserForJwt entity for JWT creation responses
  - Define repository interfaces (IPendingUserRepository, IUserRepository)
  - Create use case interfaces following Clean Architecture
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 2. Implement business rule validation classes

  - Create DeviceIdUniquenessRule class to enforce one-user-per-device rule
  - Implement FcidValidationRule class for FCID and device ID validation
  - Write BusinessRuleEngine class to orchestrate validation rules
  - Add comprehensive unit tests for all business rules
  - _Requirements: 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 3.4_

- [x] 3. Create Redis-based pending user repository

  - Implement RedisPendingUserRepository class with IPendingUserRepository interface
  - Add Redis key management for pending users and FCID mappings
  - Implement TTL management and automatic cleanup
  - Create distributed locking mechanism for atomic operations
  - Write E2E unit tests for Redis repository operations
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 4. Implement use case classes

  - Create FindOrCreateUserForJwtUseCase with mobile app retry handling
  - Implement CreatePendingUserUseCase with atomic operations
  - Build TransitionPendingUserUseCase for pending to anonymous conversion
  - Add comprehensive error handling and logging
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 6.1, 6.2_

- [x] 5. Build PendingUserService application layer

  - Create PendingUserService class implementing IPendingUserService interface
  - Orchestrate use cases and handle cross-cutting concerns
  - Implement request deduplication for mobile app scenarios
  - Add performance monitoring and metrics collection
  - Write integration tests for service operations
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 6.1, 6.2, 6.3_

- [x] 6. Create database adapter for existing user operations

  - Implement PostgresUserAdapter wrapping existing PostgresUserService
  - Create IUserRepository implementation for database operations
  - Ensure compatibility with existing caching mechanisms
  - Add monitoring for database constraint violations
  - Write tests for database adapter functionality
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 7. Modify AuthController to use new service

  - Update createJwt endpoint to use PendingUserService
  - Implement feature flag for gradual rollout
  - Maintain backward compatibility with existing JWT creation
  - Add proper error handling and response formatting
  - Update endpoint tests to cover new implementation
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 5.2_

- [x] 8. Add configuration and environment setup

  - Create configuration classes for Redis TTL and lock settings
  - Add environment variables for feature flags and Redis settings
  - Implement configuration validation and defaults
  - Create configuration schema documentation
  - _Requirements: 1.1, 1.3, 5.1_

- [x] 9. Implement Redis unavailability fallback mechanisms

  - Create fallback service that handles Redis unavailability gracefully
  - Implement direct database lookup when Redis is down
  - Add circuit breaker pattern for Redis operations
  - Create custom exception classes for Redis failures
  - _Requirements: 5.1, 5.3, 5.4, 5.5_

- [ ] 10. Enhance health checks to include pending user service status

  - Update HealthController to include pending user service health checks
  - Add Redis connectivity monitoring to health endpoint
  - Implement database constraint violation monitoring
  - Create automated cleanup process for expired pending users
  - _Requirements: 3.3, 3.4, 6.3, 6.4_

- [x] 11. Create integration tests for end-to-end flows

  - Write integration tests for complete JWT creation flow with new service
  - Test concurrent user creation scenarios and race conditions
  - Verify Redis storage, retrieval, and cleanup operations
  - Test database constraint enforcement and violation detection
  - Add performance tests for high-load scenarios with mobile app retries
  - Test feature flag switching between old and new implementations
  - Test Redis fallback mechanisms when Redis is unavailable
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 5.1, 6.1, 6.2, 6.3_

- [ ] 12. Create monitoring and alerting infrastructure

  - Set up monitoring dashboards for pending user service metrics
  - Implement alerting for Redis failures and business rule violations
  - Create data validation scripts to verify system integrity
  - Add performance monitoring for Redis operations and fallback scenarios
  - _Requirements: 5.2, 5.5, 6.4_

- [ ] 13. Create deployment documentation and procedures

  - Create deployment runbook with rollback procedures
  - Document feature flag rollout strategy
  - Create troubleshooting guide for Redis and fallback scenarios
  - Document operational procedures for monitoring and maintenance
  - _Requirements: 5.2, 5.5_
