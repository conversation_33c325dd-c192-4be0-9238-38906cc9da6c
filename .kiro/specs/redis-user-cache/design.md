# Design Document

## Overview

This design implements a Redis cache layer for the UsersService to optimize user lookup operations. The solution will intercept user queries at the service level, check Redis cache first, and fall back to database queries when necessary. The cache will maintain consistency through strategic invalidation and support multiple identifier types for comprehensive coverage.

## Architecture

### High-Level Flow

```mermaid
sequenceDiagram
    participant US as UsersService
    participant UC as UserCacheService
    participant RS as RedisService
    participant PUS as PostgresUserService
    participant DB as Database

    US->>UC: findUser(identifier)
    UC->>RS: get(cacheKey)
    alt Cache Hit
        RS-->>UC: userData
        UC-->>US: userData
    else Cache Miss
        UC->>PUS: findByAttribute(identifier)
        PUS->>DB: query
        DB-->>PUS: userData
        PUS-->>UC: userData
        UC->>RS: set(cacheKey, userData, TTL)
        UC-->>US: userData
    end
```

### Component Integration

The cache layer will be implemented as a new `UserCacheService` that sits between the `UsersService` and `PostgresUserService`. This approach maintains separation of concerns and allows for easy testing and configuration.

## Components and Interfaces

### UserCacheService

A new service that handles all user caching operations:

```typescript
interface UserCacheService {
  // Core cache operations
  findUserByFcid(fcid: string): Promise<User | null>;
  findUserByFcaid(fcaid: string): Promise<User | null>;
  findUserByDeviceId(deviceId: string): Promise<User | null>;
  
  // Cache management
  cacheUser(user: User): Promise<void>;
  invalidateUser(user: User): Promise<void>;
  invalidateUserByFcid(fcid: string): Promise<void>;
  
  // Metrics and monitoring
  getCacheStats(): Promise<CacheStats>;
}
```

### Cache Key Strategy

The cache will use a hierarchical key structure:

- Primary keys: `user:fcid:{fcid}`, `user:fcaid:{fcaid}`
- Device ID keys: `user:device:{deviceId}`
- Reference keys: Device ID keys will store the user's fcid, requiring a second lookup

### Cache Data Structure

```typescript
interface CachedUser {
  fcid: string;
  fcaid?: string;
  type: UserType;
  device_ids: string[];
  identifiers: DeviceIdentifiers;
  properties: Record<string, any>;
  merged_fcids?: string[];
  installed_at: Date;
  created_at: Date;
  updated_at: Date;
  // Cache metadata
  cached_at: number;
  version: string;
}
```

## Data Models

### Cache Configuration

```typescript
interface CacheConfig {
  ttl: number; // Time to live in seconds (default: 1800 = 30 minutes)
  negativeTtl: number; // TTL for negative cache entries (default: 60 seconds)
  keyPrefix: string; // Cache key prefix (default: 'user:')
  maxRetries: number; // Max retries for Redis operations (default: 3)
  fallbackToDb: boolean; // Whether to fallback to DB on Redis failure (default: true)
}
```

### Cache Metrics

```typescript
interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalOperations: number;
  errors: number;
  lastReset: Date;
}
```

## Implementation Strategy

### Phase 1: Core Cache Service

1. Create `UserCacheService` with basic get/set operations
2. Implement cache key generation for all identifier types
3. Add fallback logic for Redis failures
4. Integrate with existing `RedisService`

### Phase 2: Service Integration

1. Modify `UsersService` to use `UserCacheService` for lookups
2. Update `findExistingUser` method to check cache first
3. Ensure cache invalidation on user updates/deletes
4. Add cache warming for frequently accessed users

### Phase 3: Advanced Features

1. Implement cache metrics and monitoring
2. Add cache preloading strategies
3. Optimize cache key patterns for better performance
4. Add cache compression for large user objects

## Error Handling

### Redis Unavailability

When Redis is unavailable, the system will:

1. Log the error with appropriate context
2. Fall back to direct database queries
3. Continue normal operation without caching
4. Attempt to reconnect on subsequent operations

### Cache Corruption

If cached data is corrupted or invalid:

1. Log the corruption event
2. Remove the corrupted cache entry
3. Fetch fresh data from the database
4. Cache the fresh data for future requests

### Serialization Errors

For JSON serialization/deserialization errors:

1. Log the error with user context
2. Skip caching for that specific user
3. Return database result directly
4. Attempt to cache on next successful operation

## Testing Strategy

### Unit Tests

- Test cache hit/miss scenarios
- Verify cache key generation for all identifier types
- Test fallback behavior when Redis is unavailable
- Validate cache invalidation logic

### Integration Tests

- Test end-to-end user lookup with caching
- Verify cache consistency during user updates
- Test performance improvements with cache enabled
- Validate error handling in Redis failure scenarios

### Performance Tests

- Measure response time improvements
- Test cache performance under high load
- Validate memory usage patterns
- Monitor cache hit rates in production-like scenarios

## Configuration

### Environment Variables

```bash
# Cache TTL in seconds (default: 1800)
USER_CACHE_TTL=1800

# Negative cache TTL in seconds (default: 60)
USER_CACHE_NEGATIVE_TTL=60

# Enable/disable user caching (default: true)
USER_CACHE_ENABLED=true

# Cache key prefix (default: 'user:')
USER_CACHE_PREFIX=user:

# Max cache operation retries (default: 3)
USER_CACHE_MAX_RETRIES=3
```

### Cache Policies

- **TTL Policy**: 30 minutes for positive cache entries
- **Negative Cache**: 1 minute for "user not found" results
- **Eviction Policy**: LRU (handled by Redis configuration)
- **Compression**: Enable for user objects > 1KB

## Monitoring and Observability

### Metrics to Track

1. **Cache Performance**
   - Hit rate percentage
   - Average response time
   - Cache size and memory usage

2. **Error Rates**
   - Redis connection failures
   - Serialization errors
   - Cache corruption incidents

3. **Business Metrics**
   - Database query reduction percentage
   - User lookup performance improvement
   - System throughput increase

### Logging Strategy

- **Debug Level**: Cache hits, misses, and key operations
- **Info Level**: Cache statistics and performance metrics
- **Warn Level**: Redis connection issues and fallback usage
- **Error Level**: Cache corruption and critical failures

## Migration Plan

### Phase 1: Non-Breaking Implementation
1. Deploy `UserCacheService` alongside existing code
2. Enable caching for read operations only
3. Monitor cache performance and hit rates
4. Gradually increase cache coverage

### Phase 2: Full Integration
1. Enable cache invalidation on write operations
2. Optimize cache key patterns based on usage data
3. Implement advanced caching strategies
4. Monitor system performance improvements

### Rollback Strategy

If issues arise:
1. Disable caching via feature flag
2. System continues with database-only operations
3. Investigate and fix cache-related issues
4. Re-enable caching after validation