#!/bin/bash
set -euo pipefail

# Load environment variables
export $(grep -v '^#' .env | xargs)

echo "🔍 Starting LocalStack resource initialization..."

# Wait for Secrets Manager to be ready
echo "⏳ Waiting for Secrets Manager..."
until awslocal secretsmanager list-secrets &>/dev/null; do
  sleep 1
done

# Secrets Manager Secret
echo "🔄 Initializing Secrets Manager..."
if ! awslocal secretsmanager describe-secret --secret-id "$AWS_SECRET_NAME" &>/dev/null; then
  echo "⏳ Creating secret..."
  awslocal secretsmanager create-secret --name "$AWS_SECRET_NAME" --secret-string '{"API_AUTH_TOKEN":"7b35ebb8a7614c8e3085b30d3afb05408d6b64b7ab33681a7e6e703af6d3d8d4","JWT_SECRET":"0d67f29c-951b-40cd-8438-4cddeb5ed641", "JWT_REFRESH_SECRET" :"799c7f90-a265-4310-9c19-9afd55eae69a" }'
  echo "✅ Secret created"
else
  echo "ℹ️  Secret exists"
fi

# Final output
cat <<EOF

🎉 Resource Initialization Complete

🔐 Secrets Manager Configuration:
   - Secret Name: $AWS_SECRET_NAME

EOF
