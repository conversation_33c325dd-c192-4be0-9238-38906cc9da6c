#!/usr/bin/env ts-node

/**
 * Database Performance Stress Test
 *
 * This script stress tests the database optimizations by simulating real-world usage patterns:
 * 1. User creation (anonymous and registered)
 * 2. User lookups by device IDs (the main performance bottleneck)
 * 3. User merging operations
 * 4. Batch operations
 *
 * The test runs for the specified duration (testDurationMinutes) with concurrent users.
 * Each user performs workflows continuously until the time limit is reached.
 * A throttling mechanism prevents excessive load (max 30 workflows/minute per user).
 *
 * Usage: npm run stress-test
 */

import * as dotenv from 'dotenv';
// Load environment variables first
dotenv.config();

import axios, { AxiosInstance } from 'axios';
import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';

interface TestConfig {
  baseUrl: string;
  apiAuthToken: string;
  concurrentUsers: number;
  operationsPerUser: number; // Legacy parameter - now used for backwards compatibility only
  testDurationMinutes: number; // Primary constraint - test runs for this duration
}

interface PerformanceMetrics {
  operation: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errors: string[];
}

interface TestResults {
  testStartTime: Date;
  testEndTime: Date;
  totalDuration: number;
  metrics: PerformanceMetrics[];
  databaseMetrics: {
    before: any;
    after: any;
  };
  summary: {
    totalRequests: number;
    totalSuccessful: number;
    totalFailed: number;
    overallRPS: number;
    averageResponseTime: number;
  };
}

class StressTestRunner {
  private client: AxiosInstance;
  private config: TestConfig;
  private results: TestResults;
  private responseTimes: Map<string, number[]> = new Map();

  constructor(config: TestConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Android/4.2.8 (756)',
      },
    });

    this.results = {
      testStartTime: new Date(),
      testEndTime: new Date(),
      totalDuration: 0,
      metrics: [],
      databaseMetrics: { before: null, after: null },
      summary: {
        totalRequests: 0,
        totalSuccessful: 0,
        totalFailed: 0,
        overallRPS: 0,
        averageResponseTime: 0,
      },
    };
  }

  /**
   * Generate random device identifiers
   */
  private generateDeviceIds() {
    return {
      adid: this.generateRandomId(16),
      gaid: this.generateUUID(), // GAID must be UUID format
      idfv: this.generateUUID(),
      idfa: this.generateUUID(),
    };
  }

  private generateRandomId(length: number): string {
    return Array.from({ length }, () => Math.floor(Math.random() * 16).toString(16)).join('');
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  /**
   * Create JWT token for authentication
   */
  private async createJwtToken(deviceId: string, fcid?: string): Promise<string> {
    const payload: any = { adid: deviceId };
    if (fcid) payload.fcid = fcid;

    const response = await this.client.post('/auth/createJwt', payload, {
      headers: { 'x-api-key': this.config.apiAuthToken },
    });

    return response.data.data.accessToken;
  }

  /**
   * Measure operation performance
   */
  private async measureOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();

    try {
      const result = await operation();
      const duration = performance.now() - startTime;

      if (!this.responseTimes.has(operationName)) {
        this.responseTimes.set(operationName, []);
      }
      this.responseTimes.get(operationName)!.push(duration);

      return { result, duration };
    } catch (error) {
      const duration = performance.now() - startTime;
      this.responseTimes.get(operationName)?.push(duration);
      throw error;
    }
  }

  /**
   * Test user creation (anonymous)
   */
  private async testUserCreation(deviceIds: any): Promise<any> {
    const token = await this.createJwtToken(deviceIds.adid);

    const { result } = await this.measureOperation('user_creation', async () => {
      return this.client.post(
        '/users',
        {
          identifiers: {
            adid: [deviceIds.adid],
            gaid: [deviceIds.gaid],
          },
          installed_at: Date.now(),
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
    });

    return result.data.data;
  }

  /**
   * Test user lookup by device ID (main performance bottleneck)
   */
  private async testUserLookup(deviceId: string): Promise<any> {
    const token = await this.createJwtToken(deviceId);

    const { result } = await this.measureOperation('user_lookup_device_id', async () => {
      return this.client.get(`/users?identifiers.adid=${deviceId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
    });

    return result.data.data;
  }

  /**
   * Test user merge operation
   */
  private async testUserMerge(user: any, deviceIds: any): Promise<any> {
    const fcaid = this.generateUUID();
    const token = await this.createJwtToken(deviceIds.adid, user.fcid);

    const { result } = await this.measureOperation('user_merge', async () => {
      return this.client.post(
        '/users',
        {
          fcaid,
          fcid: user.fcid,
          identifiers: {
            adid: [deviceIds.adid],
            gaid: [deviceIds.gaid],
          },
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        },
      );
    });

    return result.data.data;
  }

  /**
   * Test batch operations by creating multiple users rapidly
   */
  private async testBatchOperations(count: number): Promise<void> {
    const promises: Promise<any>[] = [];

    for (let i = 0; i < count; i++) {
      const deviceIds = this.generateDeviceIds();
      promises.push(this.testUserCreation(deviceIds));
    }

    await this.measureOperation('batch_user_creation', async () => {
      await Promise.all(promises);
    });
  }

  /**
   * Simulate realistic user workflow
   */
  private async simulateUserWorkflow(): Promise<void> {
    const deviceIds = this.generateDeviceIds();

    try {
      // 1. Create anonymous user
      const user = await this.testUserCreation(deviceIds);

      // 2. Multiple lookups (simulating app usage)
      for (let i = 0; i < 5; i++) {
        await this.testUserLookup(deviceIds.adid);
        await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
      }

      // 3. Merge user (registration)
      await this.testUserMerge(user, deviceIds);

      // 4. More lookups after merge
      for (let i = 0; i < 3; i++) {
        await this.testUserLookup(deviceIds.adid);
      }
    } catch (error) {
      console.error('Workflow error:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }

  /**
   * Get database metrics
   */
  private async getDatabaseMetrics(): Promise<any> {
    try {
      const response = await this.client.get('/health/database-metrics');
      return response.data;
    } catch (error) {
      console.warn('Could not fetch database metrics:', error.message);
      return null;
    }
  }

  /**
   * Calculate performance metrics
   */
  private calculateMetrics(): void {
    this.results.metrics = [];

    for (const [operation, times] of this.responseTimes.entries()) {
      if (times.length === 0) continue;

      const sortedTimes = times.sort((a, b) => a - b);
      const successCount = times.length;

      const metrics: PerformanceMetrics = {
        operation,
        totalRequests: successCount,
        successfulRequests: successCount,
        failedRequests: 0, // We'll track this separately
        averageResponseTime: times.reduce((a, b) => a + b, 0) / times.length,
        minResponseTime: sortedTimes[0],
        maxResponseTime: sortedTimes[sortedTimes.length - 1],
        p95ResponseTime: sortedTimes[Math.floor(sortedTimes.length * 0.95)],
        p99ResponseTime: sortedTimes[Math.floor(sortedTimes.length * 0.99)],
        requestsPerSecond: successCount / (this.results.totalDuration / 1000),
        errors: [],
      };

      this.results.metrics.push(metrics);
    }

    // Calculate summary
    const totalRequests = this.results.metrics.reduce((sum, m) => sum + m.totalRequests, 0);
    const totalSuccessful = this.results.metrics.reduce((sum, m) => sum + m.successfulRequests, 0);
    const avgResponseTime =
      this.results.metrics.reduce((sum, m) => sum + m.averageResponseTime * m.totalRequests, 0) /
      totalRequests;

    this.results.summary = {
      totalRequests,
      totalSuccessful,
      totalFailed: totalRequests - totalSuccessful,
      overallRPS: totalRequests / (this.results.totalDuration / 1000),
      averageResponseTime: avgResponseTime,
    };
  }

  /**
   * Run the stress test
   */
  async run(): Promise<TestResults> {
    console.log('🚀 Starting Database Performance Stress Test...');
    console.log(`📊 Configuration:`);
    console.log(`   - Base URL: ${this.config.baseUrl}`);
    console.log(`   - Concurrent Users: ${this.config.concurrentUsers}`);
    console.log(`   - Test Duration: ${this.config.testDurationMinutes} minutes`);
    console.log(`   - Test Type: Time-based (users run continuously for full duration)`);
    console.log(`   - Throttling: Max 30 workflows/minute per user (prevents overload)`);

    // Get initial database metrics
    console.log('\n📈 Collecting initial database metrics...');
    this.results.databaseMetrics.before = await this.getDatabaseMetrics();

    this.results.testStartTime = new Date();
    const testEndTime = new Date(Date.now() + this.config.testDurationMinutes * 60 * 1000);

    console.log('\n🔥 Starting stress test operations...');

    const workflows: Promise<void>[] = [];

    // Start concurrent user workflows
    for (let i = 0; i < this.config.concurrentUsers; i++) {
      workflows.push(this.runConcurrentWorkflow(testEndTime));
    }

    // Wait for all workflows to complete
    await Promise.all(workflows);

    this.results.testEndTime = new Date();
    this.results.totalDuration =
      this.results.testEndTime.getTime() - this.results.testStartTime.getTime();

    // Get final database metrics
    console.log('\n📈 Collecting final database metrics...');
    this.results.databaseMetrics.after = await this.getDatabaseMetrics();

    // Calculate performance metrics
    this.calculateMetrics();

    // Generate report
    this.generateReport();

    return this.results;
  }

  /**
   * Run concurrent workflow until test end time
   */
  private async runConcurrentWorkflow(endTime: Date): Promise<void> {
    let workflowCount = 0;
    const startTime = Date.now();

    // Run for the full duration - this is a time-based test
    while (Date.now() < endTime.getTime()) {
      // Safety check to prevent excessive load (only if running for a very long time)
      const elapsedMinutes = (Date.now() - startTime) / (1000 * 60);
      const maxWorkflowsPerMinute = 30; // Reasonable limit: ~30 workflows per minute per user
      const maxWorkflows = Math.ceil(elapsedMinutes * maxWorkflowsPerMinute);

      if (workflowCount >= maxWorkflows && elapsedMinutes > 1) {
        // Only throttle if we've been running for more than 1 minute and are going too fast
        console.log(
          `⚡ User ${workflowCount} workflows completed in ${elapsedMinutes.toFixed(
            1,
          )} minutes - throttling to prevent overload...`,
        );
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        continue;
      }

      await this.simulateUserWorkflow();
      workflowCount++;

      // Small random delay to simulate realistic usage
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
    }

    const actualDuration = (Date.now() - startTime) / 1000;
    console.log(
      `✅ User workflow completed: ${workflowCount} workflows in ${actualDuration.toFixed(1)}s (${(
        workflowCount /
        (actualDuration / 60)
      ).toFixed(1)} workflows/min)`,
    );
  }

  /**
   * Generate performance report
   */
  private generateReport(): void {
    console.log('\n' + '='.repeat(80));
    console.log('📊 STRESS TEST RESULTS');
    console.log('='.repeat(80));

    const actualDurationSeconds = this.results.totalDuration / 1000;
    const expectedDurationSeconds = this.config.testDurationMinutes * 60;
    const durationDifference = Math.abs(actualDurationSeconds - expectedDurationSeconds);

    console.log(
      `\n⏱️  Test Duration: ${actualDurationSeconds.toFixed(
        2,
      )} seconds (expected: ${expectedDurationSeconds}s)`,
    );
    if (durationDifference > 5) {
      console.log(`⚠️  Duration difference: ${durationDifference.toFixed(2)}s`);
    }
    console.log(`📈 Total Requests: ${this.results.summary.totalRequests}`);
    console.log(`✅ Successful: ${this.results.summary.totalSuccessful}`);
    console.log(`❌ Failed: ${this.results.summary.totalFailed}`);
    console.log(`🚀 Overall RPS: ${this.results.summary.overallRPS.toFixed(2)}`);
    console.log(
      `⚡ Average Response Time: ${this.results.summary.averageResponseTime.toFixed(2)}ms`,
    );

    console.log('\n📋 Operation Breakdown:');
    console.log('-'.repeat(80));

    for (const metric of this.results.metrics) {
      console.log(`\n🔸 ${metric.operation.toUpperCase()}`);
      console.log(`   Requests: ${metric.totalRequests}`);
      console.log(`   RPS: ${metric.requestsPerSecond.toFixed(2)}`);
      console.log(`   Avg: ${metric.averageResponseTime.toFixed(2)}ms`);
      console.log(`   Min: ${metric.minResponseTime.toFixed(2)}ms`);
      console.log(`   Max: ${metric.maxResponseTime.toFixed(2)}ms`);
      console.log(`   P95: ${metric.p95ResponseTime.toFixed(2)}ms`);
      console.log(`   P99: ${metric.p99ResponseTime.toFixed(2)}ms`);
    }

    // Database metrics comparison
    if (this.results.databaseMetrics.before && this.results.databaseMetrics.after) {
      console.log('\n🗄️  Database Metrics Comparison:');
      console.log('-'.repeat(80));

      const before =
        this.results.databaseMetrics.before.data || this.results.databaseMetrics.before;
      const after = this.results.databaseMetrics.after.data || this.results.databaseMetrics.after;

      console.log(`Cache Hit Ratio: ${before.cacheHitRatio}% → ${after.cacheHitRatio}%`);
      console.log(`Active Connections: ${before.connectionCount} → ${after.connectionCount}`);
      console.log(
        `Slow Queries: ${before.slowQueries?.length || 0} → ${after.slowQueries?.length || 0}`,
      );

      if (after.slowQueries?.length > 0) {
        console.log('\n🐌 Slow Queries Detected:');
        after.slowQueries.slice(0, 3).forEach((query: any, index: number) => {
          console.log(
            `   ${index + 1}. ${query.mean_exec_time.toFixed(2)}ms avg - ${query.calls} calls`,
          );
        });
      }
    } else {
      console.log('\n🗄️  Database Metrics Comparison:');
      console.log('-'.repeat(80));
      console.log('Cache Hit Ratio: undefined% → undefined%');
      console.log('Active Connections: undefined → undefined');
      console.log('Slow Queries: 0 → 0');
    }

    // Save detailed results to file
    const reportPath = path.join(process.cwd(), './db-performance/stress-test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n💾 Detailed results saved to: ${reportPath}`);

    console.log('\n' + '='.repeat(80));
  }
}

// Configuration
console.log('Raw ROSHI_URL from env:', JSON.stringify(process.env.ROSHI_URL));

// Fix URL encoding issues if present
let baseUrl = process.env.ROSHI_URL || 'http://localhost:3000';

// Handle various encoding issues that might occur
if (baseUrl.includes('\\x3a')) {
  console.log('Detected hex encoding issue, fixing...');
  baseUrl = baseUrl.replace(/\\x3a/g, ':');
  console.log('Fixed URL:', baseUrl);
}

// Validate the URL format
try {
  new URL(baseUrl);
  console.log('URL validation passed:', baseUrl);
} catch (error) {
  console.error('Invalid URL detected:', baseUrl);
  console.error('Falling back to default URL');
  baseUrl = 'http://localhost:3000';
}

const config: TestConfig = {
  baseUrl,
  apiAuthToken: process.env.API_AUTH_TOKEN || '',
  concurrentUsers: parseInt(process.env.CONCURRENT_USERS || '10'),
  operationsPerUser: parseInt(process.env.OPERATIONS_PER_USER || '50'),
  testDurationMinutes: parseInt(process.env.TEST_DURATION_MINUTES || '5'),
};

// Run the stress test if this script is executed directly
if (require.main === module) {
  const runner = new StressTestRunner(config);

  runner
    .run()
    .then(results => {
      console.log('\n✅ Stress test completed successfully!');

      // Exit with error code if too many failures
      const failureRate = results.summary.totalFailed / results.summary.totalRequests;
      if (failureRate > 0.05) {
        // More than 5% failure rate
        console.error(`❌ High failure rate detected: ${(failureRate * 100).toFixed(2)}%`);
        process.exit(1);
      }

      // Exit with error code if average response time is too high
      if (results.summary.averageResponseTime > 500) {
        // More than 500ms average
        console.error(
          `❌ High response times detected: ${results.summary.averageResponseTime.toFixed(
            2,
          )}ms average`,
        );
        process.exit(1);
      }

      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Stress test failed:', error);
      process.exit(1);
    });
}

export { StressTestRunner, TestConfig, TestResults };
