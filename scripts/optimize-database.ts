#!/usr/bin/env ts-node

/**
 * Database optimization script
 * Run this script to apply immediate performance optimizations
 *
 * Usage: npm run optimize-db
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { DatabaseMetricsService } from '../src/common/services/database-metrics.service';
import { PostgresUserService } from '../src/common/services/postgres-user.service';

async function optimizeDatabase() {
  console.log('🚀 Starting database optimization...');

  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const databaseMetrics = app.get(DatabaseMetricsService);
    const userService = app.get(PostgresUserService);

    console.log('📊 Generating initial performance report...');
    const initialReport = await databaseMetrics.generatePerformanceReport();

    console.log('\n📈 Initial Performance Metrics:');
    console.log(`- Cache Hit Ratio: ${initialReport.cacheHitRatio}%`);
    console.log(`- Active Connections: ${initialReport.connectionCount}`);
    console.log(`- Slow Queries: ${initialReport.slowQueries.length}`);
    console.log(
      `- Users Table: ${initialReport.tableStats.users.rowCount} rows (${initialReport.tableStats.users.size})`,
    );
    console.log(
      `- Webhooks Table: ${initialReport.tableStats.webhooks.rowCount} rows (${initialReport.tableStats.webhooks.size})`,
    );

    // Connection pool analysis
    console.log('\n🔌 Connection Pool Analysis:');
    const poolStats = await databaseMetrics.getConnectionPoolStats();
    const healthCheck = await databaseMetrics.checkConnectionPoolHealth();

    console.log(`- Total Connections: ${poolStats.totalConnections}`);
    console.log(`- Active Connections: ${poolStats.activeConnections}`);
    console.log(`- Idle Connections: ${poolStats.idleConnections}`);
    console.log(
      `- Pool Utilization: ${(
        (poolStats.activeConnections / poolStats.maxConnections) *
        100
      ).toFixed(1)}%`,
    );
    console.log(`- Pool Health: ${healthCheck.isHealthy ? '✅ Healthy' : '❌ Issues detected'}`);

    if (!healthCheck.isHealthy) {
      console.log('\n⚠️  Connection Pool Issues:');
      healthCheck.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
      console.log('\n💡 Recommendations:');
      healthCheck.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }

    if (initialReport.slowQueries.length > 0) {
      console.log('\n🐌 Top 5 Slow Queries:');
      initialReport.slowQueries.slice(0, 5).forEach((query, index) => {
        console.log(
          `${index + 1}. ${query.mean_exec_time.toFixed(2)}ms avg - ${query.calls} calls`,
        );
        console.log(`   ${query.query.substring(0, 100)}...`);
      });
    }

    console.log('\n🔧 Running database analysis...');
    await databaseMetrics.analyzeDatabase();

    console.log('🧹 Flushing pending batch updates...');
    await userService.flushUpdateQueue();
    console.log(`   Queue size before flush: ${await userService.getUpdateQueueSize()}`);

    console.log('🔄 Resetting query statistics...');
    await databaseMetrics.resetQueryStats();

    console.log('\n📊 Generating final performance report...');
    const finalReport = await databaseMetrics.generatePerformanceReport();
    const finalPoolStats = await databaseMetrics.getConnectionPoolStats();

    console.log('\n📈 Final Performance Metrics:');
    console.log(`- Cache Hit Ratio: ${finalReport.cacheHitRatio}%`);
    console.log(`- Active Connections: ${finalReport.connectionCount}`);
    console.log(`- Slow Queries: ${finalReport.slowQueries.length}`);
    console.log(
      `- Pool Utilization: ${(
        (finalPoolStats.activeConnections / finalPoolStats.maxConnections) *
        100
      ).toFixed(1)}%`,
    );

    console.log('\n✅ Database optimization completed successfully!');

    console.log('\n📋 Next Steps:');
    console.log('1. Run the migration: npm run migration:run');
    console.log('2. Monitor performance: GET /health/database-metrics');
    console.log('3. Check connection pool: GET /health/connection-pool-metrics');
    console.log('4. Monitor pg.connect latency in SigNoz');
    console.log('5. Consider implementing connection pooling with PgBouncer if latency persists');
  } catch (error) {
    console.error('❌ Database optimization failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run the optimization if this script is executed directly
if (require.main === module) {
  optimizeDatabase().catch(console.error);
}

export { optimizeDatabase };
