#!/bin/bash

# Database Performance Testing Suite
# This script runs various performance test scenarios to measure the impact of optimizations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
DEFAULT_BASE_URL="http://localhost:3000"
DEFAULT_CONCURRENT_USERS=10
DEFAULT_OPERATIONS_PER_USER=50
DEFAULT_TEST_DURATION=5

# Load environment variables safely using set -a approach
if [ -f .env ]; then
    # Use set -a to automatically export variables, then source the file
    set -a
    source .env
    set +a

    # Fix any URL encoding issues that might have occurred
    if [[ "$ROSHI_URL" == *"\\x3a"* ]]; then
        echo "Detected URL encoding issue in ROSHI_URL, fixing..."
        ROSHI_URL=$(echo "$ROSHI_URL" | sed 's/\\x3a/:/g')
        export ROSHI_URL
        echo "Fixed ROSHI_URL: $ROSHI_URL"
    fi
fi

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    local url=${1:-$DEFAULT_BASE_URL}
    print_status "Checking if server is running at $url..."

    if curl -s -f "$url/health" > /dev/null; then
        print_success "Server is running and healthy"
        return 0
    else
        print_error "Server is not responding at $url"
        print_error "Please start the server with: npm run start:dev"
        return 1
    fi
}

# Function to run database optimization
run_optimization() {
    print_status "Running database optimization..."
    npm run optimize-db
    print_success "Database optimization completed"
}

# Function to run stress test with specific parameters
run_stress_test() {
    local scenario_name=$1
    local concurrent_users=$2
    local operations_per_user=$3
    local test_duration=$4
    local base_url=${5:-$DEFAULT_BASE_URL}

    print_status "Running $scenario_name scenario..."
    print_status "  - Concurrent Users: $concurrent_users"
    print_status "  - Operations per User: $operations_per_user"
    print_status "  - Test Duration: $test_duration minutes"
    print_status "  - Base URL: $base_url"

    # Set environment variables for the test
    export ROSHI_URL="$base_url"
    export CONCURRENT_USERS="$concurrent_users"
    export OPERATIONS_PER_USER="$operations_per_user"
    export TEST_DURATION_MINUTES="$test_duration"

    # Run the stress test
    if npm run stress-test; then
        print_success "$scenario_name completed successfully"

        # Move results file with scenario name
        if [ -f "stress-test-results.json" ]; then
            mv "stress-test-results.json" "stress-test-results-$scenario_name.json"
            print_status "Results saved to: stress-test-results-$scenario_name.json"
        fi

        return 0
    else
        print_error "$scenario_name failed"
        return 1
    fi
}

# Function to analyze database performance before and after
analyze_performance() {
    local base_url=${1:-$DEFAULT_BASE_URL}

    print_status "Analyzing database performance..."

    # Get current metrics
    if curl -s -f "$base_url/health/database-metrics" > "./db-performance/db-metrics-$(date +%Y%m%d-%H%M%S).json"; then
        print_success "Database metrics saved"
    else
        print_warning "Could not fetch database metrics"
    fi

    # Trigger database analysis
    if curl -s -f "$base_url/health/analyze-database" > /dev/null; then
        print_success "Database analysis triggered"
    else
        print_warning "Could not trigger database analysis"
    fi
}

# Function to run comprehensive test suite
run_comprehensive_tests() {
    local base_url=${1:-$DEFAULT_BASE_URL}

    print_status "Starting comprehensive performance test suite..."

    # Check server health
    if ! check_server "$base_url"; then
        exit 1
    fi

    # Run optimization first
    run_optimization

    # Analyze initial state
    analyze_performance "$base_url"

    # Test scenarios
    # Format: name:users:legacy_ops_param:duration_minutes
    # Note: This is now a time-based test with automatic throttling
    # The ops_per_user parameter is kept for backwards compatibility but not actively used
    local scenarios=(
        "light-load:5:999:2"      # 5 users, 2 minutes (light load)
        "medium-load:10:999:5"    # 10 users, 5 minutes (normal load)
        "heavy-load:20:999:3"     # 20 users, 3 minutes (heavy load)
        "burst-load:50:999:1"     # 50 users, 1 minute (burst load)
    )

    local failed_tests=0

    for scenario in "${scenarios[@]}"; do
        IFS=':' read -r name users ops duration <<< "$scenario"

        print_status "Starting $name test scenario..."

        if run_stress_test "$name" "$users" "$ops" "$duration" "$base_url"; then
            print_success "$name scenario passed"
        else
            print_error "$name scenario failed"
            ((failed_tests++))
        fi

        # Wait between tests
        print_status "Waiting 30 seconds before next test..."
        sleep 30
    done

    # Final analysis
    analyze_performance "$base_url"

    # Summary
    echo ""
    echo "=========================================="
    echo "COMPREHENSIVE TEST SUITE SUMMARY"
    echo "=========================================="

    if [ $failed_tests -eq 0 ]; then
        print_success "All test scenarios passed! 🎉"
        echo ""
        print_status "Performance optimization appears to be working correctly."
        print_status "Check the individual result files for detailed metrics."
    else
        print_error "$failed_tests test scenario(s) failed"
        echo ""
        print_warning "Some performance issues may still exist."
        print_warning "Review the failed test results and consider additional optimizations."
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Database Performance Testing Suite"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  comprehensive              Run full test suite with multiple scenarios"
    echo "  quick                     Run quick performance test (5 users, 2 minutes)"
    echo "  medium                    Run medium load test (10 users, 5 minutes)"
    echo "  heavy                     Run heavy load test (20 users, 3 minutes)"
    echo "  custom                    Run custom test with specified parameters"
    echo "  optimize                  Run database optimization only"
    echo "  analyze                   Analyze current database performance"
    echo "  check                     Check if server is running"
    echo ""
    echo "Note: Tests are now time-based with automatic throttling (max 30 workflows/min per user)"
    echo ""
    echo "Options:"
    echo "  --url URL                 Base URL (default: $DEFAULT_BASE_URL)"
    echo "  --users N                 Concurrent users (default: $DEFAULT_CONCURRENT_USERS)"
    echo "  --ops N                   Operations per user (default: $DEFAULT_OPERATIONS_PER_USER)"
    echo "  --duration N              Test duration in minutes (default: $DEFAULT_TEST_DURATION)"
    echo ""
    echo "Examples:"
    echo "  $0 comprehensive                                    # Run full test suite"
    echo "  $0 quick --url http://localhost:3000               # Quick test on local server"
    echo "  $0 custom --users 50 --ops 100 --duration 10       # Custom heavy load test"
    echo "  $0 medium --url https://roshi.dev.flipaclip.com    # Medium test on dev server"
    echo ""
}

# Parse command line arguments
COMMAND=""
BASE_URL="$DEFAULT_BASE_URL"
CONCURRENT_USERS="$DEFAULT_CONCURRENT_USERS"
OPERATIONS_PER_USER="$DEFAULT_OPERATIONS_PER_USER"
TEST_DURATION="$DEFAULT_TEST_DURATION"

while [[ $# -gt 0 ]]; do
    case $1 in
        comprehensive|quick|medium|heavy|custom|optimize|analyze|check)
            COMMAND="$1"
            shift
            ;;
        --url)
            BASE_URL="$2"
            shift 2
            ;;
        --users)
            CONCURRENT_USERS="$2"
            shift 2
            ;;
        --ops)
            OPERATIONS_PER_USER="$2"
            shift 2
            ;;
        --duration)
            TEST_DURATION="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Execute command
case $COMMAND in
    comprehensive)
        run_comprehensive_tests "$BASE_URL"
        ;;
    quick)
        check_server "$BASE_URL" && run_stress_test "quick" 5 999 2 "$BASE_URL"
        ;;
    medium)
        check_server "$BASE_URL" && run_stress_test "medium" 10 999 5 "$BASE_URL"
        ;;
    heavy)
        check_server "$BASE_URL" && run_stress_test "heavy" 20 999 3 "$BASE_URL"
        ;;
    custom)
        check_server "$BASE_URL" && run_stress_test "custom" "$CONCURRENT_USERS" "$OPERATIONS_PER_USER" "$TEST_DURATION" "$BASE_URL"
        ;;
    optimize)
        run_optimization
        ;;
    analyze)
        check_server "$BASE_URL" && analyze_performance "$BASE_URL"
        ;;
    check)
        check_server "$BASE_URL"
        ;;
    "")
        print_error "No command specified"
        show_usage
        exit 1
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac