#!/usr/bin/env ts-node

/**
 * Performance Comparison Script
 *
 * This script runs performance tests before and after optimizations
 * to demonstrate the improvements achieved.
 *
 * Usage: npm run compare-performance
 */

import { StressTestRunner, TestConfig, TestResults } from './stress-test-performance';
import * as fs from 'fs';
import * as path from 'path';

interface ComparisonResults {
  before: TestResults;
  after: TestResults;
  improvements: {
    responseTimeImprovement: number;
    throughputImprovement: number;
    errorRateImprovement: number;
    operationImprovements: {
      [operation: string]: {
        responseTimeImprovement: number;
        throughputImprovement: number;
      };
    };
  };
}

class PerformanceComparison {
  private config: TestConfig;

  constructor(config: TestConfig) {
    this.config = config;
  }

  /**
   * Run performance test and return results
   */
  private async runTest(label: string): Promise<TestResults> {
    console.log(`\n🔄 Running ${label} performance test...`);
    const runner = new StressTestRunner(this.config);
    return await runner.run();
  }

  /**
   * Calculate improvement percentage
   */
  private calculateImprovement(before: number, after: number): number {
    if (before === 0) return 0;
    return ((before - after) / before) * 100;
  }

  /**
   * Compare two test results
   */
  private compareResults(before: TestResults, after: TestResults): ComparisonResults {
    const responseTimeImprovement = this.calculateImprovement(
      before.summary.averageResponseTime,
      after.summary.averageResponseTime,
    );

    const throughputImprovement =
      this.calculateImprovement(before.summary.overallRPS, after.summary.overallRPS) * -1; // Negative because higher RPS is better

    const beforeErrorRate = (before.summary.totalFailed / before.summary.totalRequests) * 100;
    const afterErrorRate = (after.summary.totalFailed / after.summary.totalRequests) * 100;
    const errorRateImprovement = this.calculateImprovement(beforeErrorRate, afterErrorRate);

    // Compare individual operations
    const operationImprovements: { [operation: string]: any } = {};

    for (const beforeMetric of before.metrics) {
      const afterMetric = after.metrics.find(m => m.operation === beforeMetric.operation);
      if (afterMetric) {
        operationImprovements[beforeMetric.operation] = {
          responseTimeImprovement: this.calculateImprovement(
            beforeMetric.averageResponseTime,
            afterMetric.averageResponseTime,
          ),
          throughputImprovement:
            this.calculateImprovement(
              beforeMetric.requestsPerSecond,
              afterMetric.requestsPerSecond,
            ) * -1, // Negative because higher RPS is better
        };
      }
    }

    return {
      before,
      after,
      improvements: {
        responseTimeImprovement,
        throughputImprovement,
        errorRateImprovement,
        operationImprovements,
      },
    };
  }

  /**
   * Generate comparison report
   */
  private generateComparisonReport(comparison: ComparisonResults): void {
    console.log('\n' + '='.repeat(80));
    console.log('📊 PERFORMANCE COMPARISON REPORT');
    console.log('='.repeat(80));

    // Overall improvements
    console.log('\n🎯 OVERALL IMPROVEMENTS:');
    console.log('-'.repeat(50));

    const responseImprovement = comparison.improvements.responseTimeImprovement;
    const throughputImprovement = comparison.improvements.throughputImprovement;
    const errorImprovement = comparison.improvements.errorRateImprovement;

    console.log(
      `⚡ Response Time: ${responseImprovement > 0 ? '↓' : '↑'} ${Math.abs(
        responseImprovement,
      ).toFixed(1)}%`,
    );
    console.log(`   Before: ${comparison.before.summary.averageResponseTime.toFixed(2)}ms`);
    console.log(`   After:  ${comparison.after.summary.averageResponseTime.toFixed(2)}ms`);

    console.log(
      `🚀 Throughput: ${throughputImprovement > 0 ? '↑' : '↓'} ${Math.abs(
        throughputImprovement,
      ).toFixed(1)}%`,
    );
    console.log(`   Before: ${comparison.before.summary.overallRPS.toFixed(2)} RPS`);
    console.log(`   After:  ${comparison.after.summary.overallRPS.toFixed(2)} RPS`);

    if (errorImprovement !== 0) {
      console.log(
        `❌ Error Rate: ${errorImprovement > 0 ? '↓' : '↑'} ${Math.abs(errorImprovement).toFixed(
          1,
        )}%`,
      );
      const beforeErrorRate =
        (comparison.before.summary.totalFailed / comparison.before.summary.totalRequests) * 100;
      const afterErrorRate =
        (comparison.after.summary.totalFailed / comparison.after.summary.totalRequests) * 100;
      console.log(`   Before: ${beforeErrorRate.toFixed(2)}%`);
      console.log(`   After:  ${afterErrorRate.toFixed(2)}%`);
    }

    // Operation-specific improvements
    console.log('\n🔍 OPERATION-SPECIFIC IMPROVEMENTS:');
    console.log('-'.repeat(50));

    for (const [operation, improvements] of Object.entries(
      comparison.improvements.operationImprovements,
    )) {
      console.log(`\n🔸 ${operation.toUpperCase()}`);

      const beforeMetric = comparison.before.metrics.find(m => m.operation === operation);
      const afterMetric = comparison.after.metrics.find(m => m.operation === operation);

      if (beforeMetric && afterMetric) {
        console.log(
          `   Response Time: ${improvements.responseTimeImprovement > 0 ? '↓' : '↑'} ${Math.abs(
            improvements.responseTimeImprovement,
          ).toFixed(1)}%`,
        );
        console.log(
          `     Before: ${beforeMetric.averageResponseTime.toFixed(
            2,
          )}ms (P95: ${beforeMetric.p95ResponseTime.toFixed(2)}ms)`,
        );
        console.log(
          `     After:  ${afterMetric.averageResponseTime.toFixed(
            2,
          )}ms (P95: ${afterMetric.p95ResponseTime.toFixed(2)}ms)`,
        );

        console.log(
          `   Throughput: ${improvements.throughputImprovement > 0 ? '↑' : '↓'} ${Math.abs(
            improvements.throughputImprovement,
          ).toFixed(1)}%`,
        );
        console.log(`     Before: ${beforeMetric.requestsPerSecond.toFixed(2)} RPS`);
        console.log(`     After:  ${afterMetric.requestsPerSecond.toFixed(2)} RPS`);
      }
    }

    // Database metrics comparison
    if (comparison.before.databaseMetrics.before && comparison.after.databaseMetrics.after) {
      console.log('\n🗄️  DATABASE METRICS COMPARISON:');
      console.log('-'.repeat(50));

      const beforeDb = comparison.before.databaseMetrics.before;
      const afterDb = comparison.after.databaseMetrics.after;

      console.log(`Cache Hit Ratio: ${beforeDb.cacheHitRatio}% → ${afterDb.cacheHitRatio}%`);
      console.log(`Active Connections: ${beforeDb.connectionCount} → ${afterDb.connectionCount}`);
      console.log(
        `Slow Queries: ${beforeDb.slowQueries?.length || 0} → ${afterDb.slowQueries?.length || 0}`,
      );
    }

    // Performance verdict
    console.log('\n🏆 PERFORMANCE VERDICT:');
    console.log('-'.repeat(50));

    if (responseImprovement > 10 && throughputImprovement > 10) {
      console.log('🎉 EXCELLENT: Significant performance improvements achieved!');
    } else if (responseImprovement > 5 || throughputImprovement > 5) {
      console.log('✅ GOOD: Noticeable performance improvements detected.');
    } else if (responseImprovement > 0 || throughputImprovement > 0) {
      console.log('👍 FAIR: Some performance improvements observed.');
    } else {
      console.log('⚠️  WARNING: No significant performance improvements detected.');
      console.log('   Consider reviewing the optimizations or test conditions.');
    }

    console.log('\n' + '='.repeat(80));
  }

  /**
   * Run complete performance comparison
   */
  async runComparison(): Promise<ComparisonResults> {
    console.log('🚀 Starting Performance Comparison...');
    console.log(`📊 Configuration:`);
    console.log(`   - Base URL: ${this.config.baseUrl}`);
    console.log(`   - Concurrent Users: ${this.config.concurrentUsers}`);
    console.log(`   - Operations per User: ${this.config.operationsPerUser}`);
    console.log(`   - Test Duration: ${this.config.testDurationMinutes} minutes`);

    // Run baseline test (before optimizations)
    const beforeResults = await this.runTest('BASELINE');

    // Wait between tests
    console.log('\n⏳ Waiting 60 seconds between tests...');
    await new Promise(resolve => setTimeout(resolve, 60000));

    // Run optimized test (after optimizations)
    const afterResults = await this.runTest('OPTIMIZED');

    // Compare results
    const comparison = this.compareResults(beforeResults, afterResults);

    // Generate report
    this.generateComparisonReport(comparison);

    // Save comparison results
    const comparisonPath = path.join(process.cwd(), '/db-performance/performance-comparison.json');
    fs.writeFileSync(comparisonPath, JSON.stringify(comparison, null, 2));
    console.log(`\n💾 Comparison results saved to: ${comparisonPath}`);

    return comparison;
  }
}

// Configuration for comparison test
const config: TestConfig = {
  baseUrl: process.env.ROSHI_URL || 'http://localhost:3000',
  apiAuthToken: process.env.API_AUTH_TOKEN || '',
  concurrentUsers: parseInt(process.env.CONCURRENT_USERS || '10'),
  operationsPerUser: parseInt(process.env.OPERATIONS_PER_USER || '30'),
  testDurationMinutes: parseInt(process.env.TEST_DURATION_MINUTES || '3'),
};

// Run comparison if this script is executed directly
if (require.main === module) {
  const comparison = new PerformanceComparison(config);

  comparison
    .runComparison()
    .then(results => {
      console.log('\n✅ Performance comparison completed successfully!');

      // Exit with appropriate code based on results
      const responseImprovement = results.improvements.responseTimeImprovement;
      const throughputImprovement = results.improvements.throughputImprovement;

      if (responseImprovement > 5 || throughputImprovement > 5) {
        console.log('🎉 Performance optimizations are working effectively!');
        process.exit(0);
      } else {
        console.log('⚠️  Performance improvements are minimal. Consider additional optimizations.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Performance comparison failed:', error);
      process.exit(1);
    });
}

export { PerformanceComparison, ComparisonResults };
