#!/usr/bin/env ts-node

/**
 * Performance test for findMergedUser optimization
 * This script tests the performance of the findMergedUser query before and after optimization
 */

import { ConfigService } from '@nestjs/config';
import { createDataSource } from '../src/config/typeorm.config';
import { User, UserType } from '../src/users/entities/user.entity';
import { Repository } from 'typeorm';
import { Config } from '../src/config/interfaces/config.interface';
import configuration from '../src/config/configuration';

async function testFindMergedUserPerformance() {
  console.log('🧪 Testing findMergedUser performance...');

  // Create a temporary ConfigService instance
  const configService = new ConfigService<Config>(configuration());
  const dataSource = createDataSource(configService);

  await dataSource.initialize();

  try {
    const userRepository = dataSource.getRepository(User);

    // Test 1: Find existing merged users using optimized query
    console.log('\n📊 Test 1: Finding existing merged users with optimized query...');

    // Get some users that have mergedFcids
    const usersWithMergedFcids = await userRepository
      .createQueryBuilder('user')
      .where('user.merged_fcids IS NOT NULL')
      .andWhere('array_length(user.merged_fcids, 1) > 0')
      .andWhere('user.type != :type', { type: UserType.MERGED })
      .limit(10)
      .getMany();

    if (usersWithMergedFcids.length === 0) {
      console.log('⚠️ No users with merged_fcids found. Creating test data...');

      // Create a test user with merged_fcids
      const testUser = userRepository.create({
        fcid: `test-${Date.now()}`,
        type: UserType.REGISTERED,
        mergedFcids: [`merged-${Date.now()}-1`, `merged-${Date.now()}-2`],
        device_ids: [],
        created_at: new Date(),
        updated_at: new Date(),
        installed_at: new Date(),
      });

      await userRepository.save(testUser);
      usersWithMergedFcids.push(testUser);
    }

    // Test performance for each user's mergedFcids using optimized query
    for (const user of usersWithMergedFcids.slice(0, 3)) {
      if (user.mergedFcids && user.mergedFcids.length > 0) {
        const testFcid = user.mergedFcids[0];
        console.log(`\n🔍 Testing with fcid: ${testFcid}`);

        // Test materialized view approach first
        console.log('   📊 Testing materialized view approach...');
        const startTime1 = Date.now();
        const result1 = await userRepository
          .createQueryBuilder('user')
          .innerJoin(
            'mv_merged_users_lookup',
            'mv',
            'mv.fcid = user.fcid AND mv.merged_fcid = :fcid',
            { fcid: testFcid },
          )
          .where('user.type != :type', { type: UserType.MERGED })
          .getOne();
        const duration1 = Date.now() - startTime1;

        console.log(`   ✅ Found: ${result1 ? 'Yes' : 'No'}`);
        console.log(`   ⏱️  Duration: ${duration1}ms`);
        console.log(`   📍 Target user: ${result1?.fcid || 'Not found'}`);

        if (duration1 > 100) {
          console.log(`   ⚠️  SLOW QUERY: ${duration1}ms (should be < 100ms)`);
        } else {
          console.log(`   ✅ FAST QUERY: ${duration1}ms`);
        }

        // Test GIN index approach as fallback
        console.log('   📊 Testing GIN index approach...');
        const startTime2 = Date.now();
        const result2 = await userRepository
          .createQueryBuilder('user')
          .where('user.type != :type', { type: UserType.MERGED })
          .andWhere('user.merged_fcids IS NOT NULL')
          .andWhere('array_length(user.merged_fcids, 1) > 0')
          .andWhere('user.merged_fcids @> ARRAY[:fcid]', { fcid: testFcid })
          .getOne();
        const duration2 = Date.now() - startTime2;

        console.log(`   ✅ Found: ${result2 ? 'Yes' : 'No'}`);
        console.log(`   ⏱️  Duration: ${duration2}ms`);
        console.log(`   📍 Target user: ${result2?.fcid || 'Not found'}`);

        if (duration2 > 100) {
          console.log(`   ⚠️  SLOW QUERY: ${duration2}ms (should be < 100ms)`);
        } else {
          console.log(`   ✅ FAST QUERY: ${duration2}ms`);
        }
      }
    }

    // Test 2: Test with non-existent fcid
    console.log('\n📊 Test 2: Testing with non-existent fcid...');
    const nonExistentFcid = `non-existent-${Date.now()}`;

    // Test materialized view approach
    const startTime1 = Date.now();
    const result1 = await userRepository
      .createQueryBuilder('user')
      .innerJoin('mv_merged_users_lookup', 'mv', 'mv.fcid = user.fcid AND mv.merged_fcid = :fcid', {
        fcid: nonExistentFcid,
      })
      .where('user.type != :type', { type: UserType.MERGED })
      .getOne();
    const duration1 = Date.now() - startTime1;

    console.log(`   ✅ Found: ${result1 ? 'Yes' : 'No'}`);
    console.log(`   ⏱️  Duration: ${duration1}ms`);

    if (duration1 > 100) {
      console.log(`   ⚠️  SLOW QUERY: ${duration1}ms (should be < 100ms)`);
    } else {
      console.log(`   ✅ FAST QUERY: ${duration1}ms`);
    }

    // Test 3: Check if materialized view exists and its performance
    console.log('\n📊 Test 3: Checking materialized view...');
    try {
      const mvExists = await userRepository.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'mv_merged_users_lookup'
        );
      `);

      if (mvExists[0]?.exists) {
        console.log('   ✅ Materialized view exists');

        // Check materialized view size
        const mvSize = await userRepository.query(`
          SELECT COUNT(*) as count FROM mv_merged_users_lookup;
        `);
        console.log(`   📊 Materialized view has ${mvSize[0]?.count || 0} rows`);

        // Test materialized view query performance
        const startTime = Date.now();
        const mvQuery = await userRepository.query(`
          SELECT COUNT(*) FROM mv_merged_users_lookup LIMIT 1;
        `);
        const mvDuration = Date.now() - startTime;
        console.log(`   ⏱️  Materialized view query duration: ${mvDuration}ms`);

        if (mvDuration > 50) {
          console.log(`   ⚠️  SLOW MV QUERY: ${mvDuration}ms (should be < 50ms)`);
        } else {
          console.log(`   ✅ FAST MV QUERY: ${mvDuration}ms`);
        }
      } else {
        console.log('   ⚠️  Materialized view does not exist (run migration first)');
      }
    } catch (error) {
      console.log('   ❌ Error checking materialized view:', error.message);
    }

    // Test 4: Check indexes
    console.log('\n📊 Test 4: Checking indexes...');
    try {
      const indexes = await userRepository.query(`
        SELECT indexname, indexdef 
        FROM pg_indexes 
        WHERE tablename = 'users' 
        AND indexname LIKE '%merged_fcids%';
      `);

      if (indexes.length > 0) {
        console.log('   ✅ Found merged_fcids indexes:');
        indexes.forEach((index: any) => {
          console.log(`      - ${index.indexname}`);
        });
      } else {
        console.log('   ⚠️  No merged_fcids indexes found');
      }
    } catch (error) {
      console.log('   ❌ Error checking indexes:', error.message);
    }

    console.log('\n✅ Performance test completed!');
  } catch (error) {
    console.error('❌ Error during performance test:', error);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed.');
  }
}

// Run the test
testFindMergedUserPerformance().catch(console.error);
