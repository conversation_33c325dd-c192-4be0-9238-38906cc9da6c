#!/bin/bash

# Test script for Dead Letter Queue (DLQ) functionality
# This script tests the DLQ implementation by:
# 1. Sending invalid webhook events to trigger failures
# 2. Checking the DLQ using the API endpoints
# 3. Testing retry and delete operations

set -e

# Configuration
BASE_URL="http://localhost:3000"
AUTH_TOKEN=""
WEBHOOK_ENDPOINT="${BASE_URL}/webhooks/batch"
DLQ_ENDPOINT="${BASE_URL}/webhooks/dlq"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Function to get authentication token
get_auth_token() {
  print_message "$BLUE" "Getting authentication token..."

  # Read settings from VSCode settings
  if [ -f ".vscode/settings.json" ]; then
    AUTH_HEADER=$(grep -o '"Authorization": "[^"]*"' .vscode/settings.json | cut -d'"' -f4)
    if [ -n "$AUTH_HEADER" ]; then
      AUTH_TOKEN=$(echo $AUTH_HEADER | sed 's/Bearer //')
      print_message "$GREEN" "Found authentication token in VSCode settings"
    fi
  fi

  # If token not found, prompt user
  if [ -z "$AUTH_TOKEN" ]; then
    print_message "$YELLOW" "No authentication token found in VSCode settings"
    read -p "Please enter your JWT token: " AUTH_TOKEN
  fi

  if [ -z "$AUTH_TOKEN" ]; then
    print_message "$RED" "No authentication token provided. Exiting."
    exit 1
  fi
}

# Function to send a webhook batch with invalid data
send_invalid_webhook() {
  print_message "$BLUE" "Sending invalid webhook batch to trigger DLQ entries..."

  # First, let's create a test user to ensure we have a valid FCID
  print_message "$BLUE" "Creating a test user for webhook testing..."

  TEST_USER_PAYLOAD=$(cat << EOF
{
  "fcid": "test-dlq-user-$(date +%s)",
  "fcaid": "test-fcaid-$(date +%s)",
  "device_ids": ["test-device-dlq-$(date +%s)"],
  "type": "free",
  "properties": {
    "test_property": "test_value"
  }
}
EOF
)

  # Create the test user
  TEST_USER_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    -d "$TEST_USER_PAYLOAD" \
    "${BASE_URL}/users")

  # Extract the FCID from the response
  TEST_FCID=$(echo $TEST_USER_RESPONSE | jq -r '.data.fcid')
  TEST_DEVICE_ID=$(echo $TEST_USER_RESPONSE | jq -r '.data.device_ids[0]')

  if [ "$TEST_FCID" == "null" ] || [ -z "$TEST_FCID" ]; then
    print_message "$RED" "Failed to create test user. Using fallback values."
    TEST_FCID="test-dlq-user-fallback"
    TEST_DEVICE_ID="test-device-dlq-fallback"
  else
    print_message "$GREEN" "Created test user with FCID: $TEST_FCID"
  fi

  # Create a batch with events that will pass validation but fail processing
  # Get current timestamp in seconds and convert to milliseconds
  CURRENT_TIMESTAMP=$(($(date +%s) * 1000))

  cat > /tmp/invalid_webhook_batch.json << EOF
{
  "events": [
    {
      "event_name": "ad_shown",
      "event_control": {
        "device_id": "$TEST_DEVICE_ID",
        "timestamp": $CURRENT_TIMESTAMP
      },
      "provider": "FlipaClip",
      "fcid": "$TEST_FCID",
      "store": "google_play",
      "payload": {
        "ad_network": "test",
        "ad_unit": "banner",
        "revenue": 0.001,
        "currency": "USD",
        "precision": "estimated"
      }
    },
    {
      "event_name": "subscription_offer_shown",
      "event_control": {
        "device_id": "$TEST_DEVICE_ID",
        "timestamp": $((CURRENT_TIMESTAMP + 1000))
      },
      "provider": "FlipaClip",
      "fcid": "$TEST_FCID",
      "store": "apple_app_store",
      "payload": {
        "product_id": "com.flipaclip.premium.monthly",
        "price": 9.99,
        "currency": "USD"
      }
    },
    {
      "event_name": "SUBSCRIPTION_RENEWED",
      "event_control": {
        "device_id": "$TEST_DEVICE_ID",
        "timestamp": $((CURRENT_TIMESTAMP + 2000))
      },
      "provider": "FlipaClip",
      "fcid": "$TEST_FCID",
      "store": "amazon_store",
      "payload": {
        "product_id": "com.flipaclip.premium.yearly",
        "price": 49.99,
        "currency": "USD"
      }
    }
  ]
}
EOF

  # Send the batch
  curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    -d @/tmp/invalid_webhook_batch.json \
    "${WEBHOOK_ENDPOINT}" | jq .

  print_message "$YELLOW" "Waiting for jobs to be processed and moved to DLQ (30 seconds)..."
  sleep 30
}

# Function to check DLQ stats
check_dlq_stats() {
  print_message "$BLUE" "Checking DLQ statistics..."

  curl -s -X GET \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}/stats" | jq .
}

# Function to list DLQ jobs
list_dlq_jobs() {
  print_message "$BLUE" "Listing DLQ jobs..."

  curl -s -X GET \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}?page=0&limit=10" | jq .
}

# Function to get a specific DLQ job
get_dlq_job() {
  local job_id=$1
  print_message "$BLUE" "Getting DLQ job with ID: ${job_id}..."

  curl -s -X GET \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}/${job_id}" | jq .
}

# Function to retry a DLQ job
retry_dlq_job() {
  local job_id=$1
  print_message "$BLUE" "Retrying DLQ job with ID: ${job_id}..."

  curl -s -X POST \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}/${job_id}/retry" | jq .
}

# Function to delete a DLQ job
delete_dlq_job() {
  local job_id=$1
  print_message "$BLUE" "Deleting DLQ job with ID: ${job_id}..."

  curl -s -X DELETE \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}/${job_id}" | jq .
}

# Function to delete all DLQ jobs
delete_all_dlq_jobs() {
  print_message "$BLUE" "Deleting all DLQ jobs..."

  curl -s -X DELETE \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}" | jq .
}

# Function to run an interactive test
run_interactive_test() {
  print_message "$BLUE" "Starting interactive DLQ test..."

  # Get authentication token
  get_auth_token

  # Main menu
  while true; do
    echo ""
    print_message "$GREEN" "=== DLQ Test Menu ==="
    echo "1. Send invalid webhooks to trigger DLQ entries"
    echo "2. Check DLQ statistics"
    echo "3. List DLQ jobs"
    echo "4. Get a specific DLQ job"
    echo "5. Retry a DLQ job"
    echo "6. Delete a DLQ job"
    echo "7. Delete all DLQ jobs"
    echo "8. Run full test sequence"
    echo "9. Exit"
    echo ""
    read -p "Select an option (1-9): " option

    case $option in
      1) send_invalid_webhook ;;
      2) check_dlq_stats ;;
      3) list_dlq_jobs ;;
      4)
        read -p "Enter job ID: " job_id
        get_dlq_job "$job_id"
        ;;
      5)
        read -p "Enter job ID: " job_id
        retry_dlq_job "$job_id"
        ;;
      6)
        read -p "Enter job ID: " job_id
        delete_dlq_job "$job_id"
        ;;
      7) delete_all_dlq_jobs ;;
      8) run_full_test ;;
      9)
        print_message "$GREEN" "Exiting DLQ test script."
        exit 0
        ;;
      *)
        print_message "$RED" "Invalid option. Please try again."
        ;;
    esac
  done
}

# Function to run a full automated test sequence
run_full_test() {
  print_message "$BLUE" "Running full DLQ test sequence..."

  # Get authentication token
  get_auth_token

  # Step 1: Send invalid webhooks
  send_invalid_webhook

  # Step 2: Check DLQ stats
  check_dlq_stats

  # Step 3: List DLQ jobs
  list_dlq_jobs

  # Step 4: Get the first job ID
  job_id=$(curl -s -X GET \
    -H "Authorization: Bearer ${AUTH_TOKEN}" \
    "${DLQ_ENDPOINT}?page=0&limit=1" | jq -r '.data[0].id')

  if [ "$job_id" != "null" ] && [ -n "$job_id" ]; then
    # Step 5: Get job details
    get_dlq_job "$job_id"

    # Step 6: Retry the job
    retry_dlq_job "$job_id"

    # Step 7: Delete the job
    delete_dlq_job "$job_id"
  else
    print_message "$YELLOW" "No jobs found in DLQ to test with."
  fi

  # Step 8: Check stats again
  check_dlq_stats

  print_message "$GREEN" "Full test sequence completed!"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
  print_message "$RED" "Error: jq is not installed. Please install it to run this script."
  print_message "$YELLOW" "On macOS: brew install jq"
  print_message "$YELLOW" "On Ubuntu/Debian: sudo apt-get install jq"
  exit 1
fi

# Main execution
if [ "$1" = "--full" ]; then
  run_full_test
else
  run_interactive_test
fi
