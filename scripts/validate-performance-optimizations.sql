-- Performance Optimization Validation Script
-- Run this after connecting to PostgreSQL to validate recent optimizations
-- Focus: Last hour of activity and optimization impact

\echo '🔍 PERFORMANCE OPTIMIZATION VALIDATION REPORT'
\echo '=============================================='
\echo ''

-- Check if we're connected to the right database
\echo '📊 Database Connection Info:'
SELECT current_database() as database_name, 
       current_user as connected_user,
       version() as postgres_version;

\echo ''
\echo '🕐 Current Time and Analysis Window:'
SELECT now() as current_time,
       now() - interval '1 hour' as analysis_start_time;

\echo ''
\echo '🏗️  OPTIMIZATION INFRASTRUCTURE CHECK'
\echo '======================================'

-- 1. Check if pg_stat_statements extension exists
\echo ''
\echo '1️⃣  pg_stat_statements Extension Status:'
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements') 
        THEN '✅ pg_stat_statements is installed'
        ELSE '❌ pg_stat_statements is NOT installed'
    END as extension_status;

-- 2. Check if our new indexes exist
\echo ''
\echo '2️⃣  New Optimization Indexes Status:'
SELECT 
    schemaname,
    indexname,
    tablename,
    CASE 
        WHEN indexname LIKE '%optimized%' THEN '✅ Optimization index'
        ELSE '📋 Regular index'
    END as index_type
FROM pg_indexes 
WHERE tablename = 'users' 
    AND indexname IN (
        'idx_users_device_ids_type_optimized',
        'idx_users_merged_fcids_optimized', 
        'idx_users_type_fcid',
        'idx_users_fcaid_not_merged'
    )
ORDER BY indexname;

-- 3. Check table statistics freshness
\echo ''
\echo '3️⃣  Table Statistics Freshness:'
SELECT 
    schemaname,
    tablename,
    last_analyze,
    last_autoanalyze,
    CASE 
        WHEN last_analyze > now() - interval '1 hour' THEN '✅ Recently analyzed'
        WHEN last_autoanalyze > now() - interval '1 hour' THEN '⚠️  Auto-analyzed recently'
        ELSE '❌ Statistics may be stale'
    END as analysis_status
FROM pg_stat_user_tables 
WHERE tablename IN ('users', 'webhooks')
ORDER BY tablename;

\echo ''
\echo '📈 QUERY PERFORMANCE ANALYSIS (Last Hour)'
\echo '========================================='

-- 4. Top slow queries in the last hour (if pg_stat_statements available)
\echo ''
\echo '4️⃣  Slowest Queries (Last Hour - if available):'
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements') THEN
        PERFORM 1;
        RAISE NOTICE 'pg_stat_statements is available - showing query stats';
    ELSE
        RAISE NOTICE '❌ pg_stat_statements not available - cannot show detailed query stats';
    END IF;
END $$;

-- Show top queries by total time (only if pg_stat_statements exists)
SELECT 
    round(total_exec_time::numeric, 2) as total_time_ms,
    calls,
    round(mean_exec_time::numeric, 2) as avg_time_ms,
    round((100 * total_exec_time / sum(total_exec_time) OVER())::numeric, 2) as percent_total,
    left(query, 100) || '...' as query_preview
FROM pg_stat_statements 
WHERE query NOT LIKE '%pg_stat_statements%'
    AND query NOT LIKE '%SHOW%'
    AND calls > 0
ORDER BY total_exec_time DESC 
LIMIT 10;

\echo ''
\echo '5️⃣  Array Query Performance (device_ids and merged_fcids):'
-- Look for our optimized array queries
SELECT 
    calls,
    round(total_exec_time::numeric, 2) as total_time_ms,
    round(mean_exec_time::numeric, 2) as avg_time_ms,
    round(max_exec_time::numeric, 2) as max_time_ms,
    CASE 
        WHEN query LIKE '%device_ids%' AND query LIKE '%EXISTS%' THEN '✅ Optimized device_ids query'
        WHEN query LIKE '%merged_fcids%' AND query LIKE '%EXISTS%' THEN '✅ Optimized merged_fcids query'
        WHEN query LIKE '%device_ids%' AND query LIKE '%ANY%' THEN '⚠️  Legacy device_ids query'
        WHEN query LIKE '%merged_fcids%' AND query LIKE '%ANY%' THEN '⚠️  Legacy merged_fcids query'
        ELSE 'Other query'
    END as query_type,
    left(query, 80) || '...' as query_preview
FROM pg_stat_statements 
WHERE (query LIKE '%device_ids%' OR query LIKE '%merged_fcids%')
    AND query NOT LIKE '%pg_stat_statements%'
    AND calls > 0
ORDER BY total_exec_time DESC;

\echo ''
\echo '🎯 INDEX USAGE ANALYSIS'
\echo '======================'

-- 6. Index usage statistics for our new indexes
\echo ''
\echo '6️⃣  New Optimization Index Usage:'
SELECT 
    schemaname,
    tablename,
    indexrelname,
    idx_scan as scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    CASE 
        WHEN idx_scan > 0 THEN '✅ Index is being used'
        ELSE '❌ Index not used yet'
    END as usage_status
FROM pg_stat_user_indexes 
WHERE indexrelname IN (
    'idx_users_device_ids_type_optimized',
    'idx_users_merged_fcids_optimized',
    'idx_users_type_fcid', 
    'idx_users_fcaid_not_merged'
)
ORDER BY idx_scan DESC;

-- 7. Overall index usage for users table
\echo ''
\echo '7️⃣  All Users Table Index Usage:'
SELECT 
    indexrelname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    round(100.0 * idx_scan / GREATEST(seq_scan + idx_scan, 1), 2) as index_usage_percent
FROM pg_stat_user_indexes psi
JOIN pg_stat_user_tables pst ON psi.relid = pst.relid
WHERE pst.relname = 'users'
ORDER BY idx_scan DESC;

\echo ''
\echo '💾 DATABASE HEALTH METRICS'
\echo '=========================='

-- 8. Cache hit ratio
\echo ''
\echo '8️⃣  Cache Hit Ratio:'
SELECT 
    round(100.0 * blks_hit / GREATEST(blks_hit + blks_read, 1), 2) as cache_hit_ratio_percent,
    CASE 
        WHEN round(100.0 * blks_hit / GREATEST(blks_hit + blks_read, 1), 2) >= 95 THEN '✅ Excellent'
        WHEN round(100.0 * blks_hit / GREATEST(blks_hit + blks_read, 1), 2) >= 90 THEN '⚠️  Good'
        ELSE '❌ Needs attention'
    END as status
FROM pg_stat_database 
WHERE datname = current_database();

-- 9. Connection count
\echo ''
\echo '9️⃣  Active Connections:'
SELECT 
    count(*) as total_connections,
    count(*) FILTER (WHERE state = 'active') as active_connections,
    count(*) FILTER (WHERE state = 'idle') as idle_connections,
    CASE 
        WHEN count(*) < 50 THEN '✅ Normal'
        WHEN count(*) < 100 THEN '⚠️  High'
        ELSE '❌ Very high'
    END as connection_status
FROM pg_stat_activity 
WHERE datname = current_database();

-- 10. Table sizes and bloat indicators
\echo ''
\echo '🔟 Table Size Analysis:'
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    round(100.0 * n_dead_tup / GREATEST(n_live_tup + n_dead_tup, 1), 2) as dead_tuple_percent
FROM pg_stat_user_tables 
WHERE tablename IN ('users', 'webhooks')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

\echo ''
\echo '🎯 OPTIMIZATION IMPACT SUMMARY'
\echo '=============================='
\echo ''
\echo 'Key Metrics to Monitor:'
\echo '• Cache hit ratio should be >95%'
\echo '• Average query time for device_ids/merged_fcids should be <50ms'
\echo '• New optimization indexes should show usage (idx_scan > 0)'
\echo '• Connection count should be reasonable (<50 for normal load)'
\echo ''
\echo 'Next Steps:'
\echo '1. If indexes show 0 usage, run some queries to test them'
\echo '2. Monitor the /health/database-metrics endpoint'
\echo '3. Check application logs for slow query warnings'
\echo '4. Run stress tests to validate performance improvements'
\echo ''
\echo '✅ Validation Complete!'
