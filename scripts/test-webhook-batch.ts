import axios from 'axios';
import chalk from 'chalk';
import { faker } from '@faker-js/faker';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import dataSource from '../src/config/typeorm.config';
import { User } from '../src/users/entities/user.entity';

dotenv.config();

// Load VSCode settings for API configuration
const vscodeSettings = JSON.parse(
  fs.readFileSync(path.join(__dirname, '../.vscode/settings.json'), 'utf8'),
);
const API_URL = vscodeSettings['rest-client.environmentVariables'].local.baseUrl;
const API_KEY = vscodeSettings['rest-client.environmentVariables'].local.apiKey;

// Rate limit configuration
const RATE_LIMIT = {
  BATCH_LIMIT_PER_MINUTE: 10,
  BATCH_TTL: 60000, // 60 seconds in ms
};

const MIN_DELAY_BETWEEN_BATCHES = Math.ceil(
  RATE_LIMIT.BATCH_TTL / RATE_LIMIT.BATCH_LIMIT_PER_MINUTE,
);
const MAX_BATCH_SIZE = 30;
const INITIAL_RETRY_DELAY = MIN_DELAY_BETWEEN_BATCHES;
const MAX_RETRY_DELAY = 32000;
const MAX_RETRIES = 3;

// Configuration for test batch sizes
const TEST_CONFIG = {
  MIXED_BATCH_SIZE: process.env.MIXED_BATCH_SIZE ? parseInt(process.env.MIXED_BATCH_SIZE) : 5,
  LARGE_BATCH_SIZE: process.env.LARGE_BATCH_SIZE ? parseInt(process.env.LARGE_BATCH_SIZE) : 20,
};

// Helper function to get the next timestamp
const getNextTimestamp = (lastTimestamp: number, minIncrease = 1000) => {
  const now = Date.now();
  return Math.max(now, lastTimestamp + minIncrease);
};

// Event Templates
const createAdEvent = (
  fcid: string,
  deviceId: string,
  isValid = true,
  lastTimestamp = Date.now(),
) => {
  const timestamp = getNextTimestamp(lastTimestamp);
  return {
    provider: 'FlipaClip',
    event_name: 'ad_shown',
    event_control: {
      device_id: deviceId,
      timestamp: timestamp,
    },
    fcid,
    store: faker.helpers.arrayElement(['apple_app_store', 'google_play']),
    session_id: faker.number.int({ min: 100000000, max: 999999999 }),
    payload: isValid
      ? {
          adUnitId: 'DefaultRewardedVideo',
          adType: 'Rewarded',
          loadTime: faker.number.int({ min: 0, max: 10 }),
          revenue: Number(faker.finance.amount({ min: 0.001, max: 0.01, dec: 4 })),
          publisherNetwork: 'admanager',
          triggerAction: 'Add, Merge or Duplicate Layer',
          isRewardGranted: true,
        }
      : {
          // Invalid payload missing required fields
          adType: 'Invalid',
          loadTime: 'not-a-number', // Invalid type
          revenue: 'invalid-revenue', // Invalid type
        },
  };
};

const createSubscriptionEvent = (
  fcid: string,
  deviceId: string,
  isValid = true,
  lastTimestamp = Date.now(),
) => {
  const timestamp = getNextTimestamp(lastTimestamp);
  return {
    provider: 'FlipaClip',
    event_name: faker.helpers.arrayElement([
      'subscription_offer_shown',
      'subscription_offer_aborted',
    ]),
    event_control: {
      device_id: deviceId,
      timestamp: timestamp,
    },
    fcid,
    store: faker.helpers.arrayElement(['apple_app_store', 'google_play']),
    session_id: faker.number.int({ min: 100000000, max: 999999999 }),
    payload: isValid
      ? {
          paywall_id: 'default_light_qa',
          placement_id: 'home_subscription_button',
          trigger_action: 'app_open',
          plans: [
            {
              id: 'flipaclip_599_1m_7d0',
              offers_free_trial: true,
              period: 'MONTH',
            },
            {
              id: 'flipaclip_2999_1y_7d0',
              offers_free_trial: true,
              period: 'YEAR',
            },
          ],
          ab_test_id: 'test_001',
          ab_test_variant: 'variant_A',
        }
      : {
          // Invalid payload missing required fields
          placement_id: 123, // Invalid type
          trigger_action: null, // Invalid value
          plans: 'invalid-plans', // Invalid type, should be array
        },
  };
};

// Helper function to wait between requests
const waitForRateLimit = async () => {
  console.log(
    chalk.gray(`Waiting ${MIN_DELAY_BETWEEN_BATCHES / 1000} seconds to respect rate limits...`),
  );
  await new Promise(resolve => setTimeout(resolve, MIN_DELAY_BETWEEN_BATCHES));
};

// Send batch request with retry logic
async function sendBatchRequest(events: any[], retryCount = 0): Promise<any> {
  try {
    const response = await axios.post(
      `${API_URL}/webhooks/batch`,
      { events },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: vscodeSettings['rest-client.defaultHeaders'].Authorization,
        },
        timeout: 30000,
      },
    );
    return response;
  } catch (error: any) {
    if (error.response?.status === 429 && retryCount < MAX_RETRIES) {
      const delay = Math.min(INITIAL_RETRY_DELAY * Math.pow(2, retryCount), MAX_RETRY_DELAY);
      console.log(
        chalk.yellow(
          `Rate limited. Waiting ${delay / 1000} seconds before retry ${
            retryCount + 1
          }/${MAX_RETRIES}...`,
        ),
      );
      await new Promise(resolve => setTimeout(resolve, delay));
      return sendBatchRequest(events, retryCount + 1);
    }
    throw error;
  }
}

// Test a batch of events
async function testBatch(description: string, events: any[]) {
  console.log(chalk.blue(`\nTesting: ${description}`));
  console.log(chalk.gray(`Events count: ${events.length}`));

  if (events.length === 0) {
    console.log(chalk.yellow('No events to process'));
    return;
  }

  try {
    await waitForRateLimit();
    console.log(chalk.gray('Sending request...'));

    const response = await sendBatchRequest(events);
    console.log(chalk.gray(`Response status: ${response.status}`));

    const { data } = response;
    console.log(chalk.green('✓ Success'));
    console.log('Processed:', chalk.cyan(data.processed));
    console.log('Failed:', chalk.red(data.failed));

    if (data.validationErrors?.length > 0) {
      console.log(chalk.yellow('\nValidation Errors:'));
      data.validationErrors.forEach((error: any, index: number) => {
        console.log(chalk.yellow(`${index + 1}. ${JSON.stringify(error, null, 2)}`));
      });
    }

    if (data.processingErrors?.length > 0) {
      console.log(chalk.red('\nProcessing Errors:'));
      data.processingErrors.forEach((error: any, index: number) => {
        console.log(chalk.red(`${index + 1}. ${JSON.stringify(error, null, 2)}`));
      });
    }
  } catch (error: any) {
    console.log(chalk.red('✗ Failed'));
    if (error.response?.data) {
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function runTests() {
  console.log(chalk.green('🚀 Starting Redis/Bull Queue Webhook Testing\n'));
  console.log(chalk.gray('Test Configuration:'));
  console.log(chalk.gray(`- Mixed Batch Size: ${TEST_CONFIG.MIXED_BATCH_SIZE}`));
  console.log(chalk.gray(`- Large Batch Size: ${TEST_CONFIG.LARGE_BATCH_SIZE}`));

  try {
    // Initialize database connection
    await dataSource.initialize();
    console.log('✅ Database connection established');

    // Get real users from database
    const userRepository = dataSource.getRepository(User);
    const users = await userRepository
      .createQueryBuilder('user')
      .where('device_ids IS NOT NULL')
      .andWhere('array_length(device_ids, 1) > 0')
      .orderBy('RANDOM()')
      .limit(3)
      .getMany();

    if (users.length === 0) {
      throw new Error('No users found with device IDs. Please seed the database first.');
    }

    console.log(`Found ${users.length} users for testing`);

    // Test single events
    const user1 = users[0];
    let lastTimestamp = user1.event_control[user1.device_ids[0]] || Date.now();

    await testBatch('Single AD event', [
      createAdEvent(user1.fcid, user1.device_ids[0], true, lastTimestamp),
    ]);

    // Update lastTimestamp after each batch
    lastTimestamp = getNextTimestamp(lastTimestamp);

    await testBatch('Single SUBSCRIPTION event', [
      createSubscriptionEvent(user1.fcid, user1.device_ids[0], true, lastTimestamp),
    ]);

    // Test batch with invalid payload
    lastTimestamp = getNextTimestamp(lastTimestamp);
    await testBatch('Invalid payload batch', [
      createAdEvent(user1.fcid, user1.device_ids[0], false, lastTimestamp),
      createSubscriptionEvent(user1.fcid, user1.device_ids[0], false, lastTimestamp + 1000),
    ]);

    // Test mixed batch with configurable size
    const user2 = users[1];
    lastTimestamp = user2.event_control[user2.device_ids[0]] || Date.now();
    const mixedBatch = Array(TEST_CONFIG.MIXED_BATCH_SIZE)
      .fill(null)
      .map((_, i) => {
        lastTimestamp = getNextTimestamp(lastTimestamp);
        return i % 2 === 0
          ? createAdEvent(user2.fcid, user2.device_ids[0], true, lastTimestamp)
          : createSubscriptionEvent(user2.fcid, user2.device_ids[0], true, lastTimestamp);
      });
    await testBatch(`Mixed batch (${TEST_CONFIG.MIXED_BATCH_SIZE} events)`, mixedBatch);

    // Test larger batch with configurable size
    const user3 = users[2];
    lastTimestamp = user3.event_control[user3.device_ids[0]] || Date.now();
    const largeBatch = Array(TEST_CONFIG.LARGE_BATCH_SIZE)
      .fill(null)
      .map((_, i) => {
        lastTimestamp = getNextTimestamp(lastTimestamp);
        return i % 2 === 0
          ? createAdEvent(user3.fcid, user3.device_ids[0], true, lastTimestamp)
          : createSubscriptionEvent(user3.fcid, user3.device_ids[0], true, lastTimestamp);
      });
    await testBatch(`Large mixed batch (${TEST_CONFIG.LARGE_BATCH_SIZE} events)`, largeBatch);

    // Test mixed valid/invalid payload batch
    lastTimestamp = getNextTimestamp(lastTimestamp);
    const mixedValidityBatch = [
      createAdEvent(user1.fcid, user1.device_ids[0], true, lastTimestamp),
      createAdEvent(user1.fcid, user1.device_ids[0], false, lastTimestamp + 1000),
      createSubscriptionEvent(user1.fcid, user1.device_ids[0], true, lastTimestamp + 2000),
      createSubscriptionEvent(user1.fcid, user1.device_ids[0], false, lastTimestamp + 3000),
    ];
    await testBatch('Mixed valid/invalid payloads', mixedValidityBatch);

    console.log(chalk.green('\n✨ Testing completed!'));
  } catch (error) {
    console.error('Error running tests:', error);
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

runTests().catch(console.error);
