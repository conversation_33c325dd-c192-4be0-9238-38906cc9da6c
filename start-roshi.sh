#!/bin/bash


# ========================================================
# Roshi Application Startup Script
# ========================================================
# This script starts all required services for the Roshi application:
# 1. Redis (local instance)
# 2. PostgreSQL (via PostgresApp)
# 3. Roshi application (npm run start:dev)
#
# Each service runs in its own iTerm pane for better visibility.
# ========================================================

# Set the working directory
# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
WORKING_DIR="$SCRIPT_DIR"
cd "$WORKING_DIR" || { echo "Error: Cannot change to working directory $WORKING_DIR"; exit 1; }

# Text formatting
BOLD='\033[1m'
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script version
VERSION="1.0.0"

# Display banner
echo -e "${BOLD}${PURPLE}"
echo "╔═══════════════════════════════════════════════════════╗"
echo "║                                                       ║"
echo "║             ROSHI APPLICATION LAUNCHER                ║"
echo "║                                                       ║"
echo "║  Starts Redis, PostgreSQL, and the Roshi application  ║"
echo "║                 Version: ${VERSION}                       ║"
echo "║                                                       ║"
echo "╚═══════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Load environment variables from .env file
if [ -f .env ]; then
  echo -e "${BLUE}Loading environment variables from .env file...${NC}"
  export $(grep -v '^#' .env | xargs)
else
  echo -e "${RED}Error: .env file not found. Please create a .env file with required configuration.${NC}"
  exit 1
fi

# Set PostgreSQL variables for easier reference
PG_HOST=$POSTGRES_HOST
PG_PORT=$POSTGRES_PORT
PG_USER=$POSTGRES_USER
PG_PASSWORD=$POSTGRES_PASSWORD
PG_DB=$POSTGRES_DB
REDIS_PASSWORD=$REDIS_PASSWORD

# ========================================================
# Check prerequisites
# ========================================================

echo -e "${BOLD}Checking prerequisites...${NC}"

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
  echo -e "${RED}Error: Redis is not installed. Please install Redis using Homebrew:${NC}"
  echo -e "${YELLOW}brew install redis${NC}"
  exit 1
fi

# Check if PostgresApp is installed
if ! [ -d "/Applications/Postgres.app" ] && ! [ -d "/Applications/PostgreSQL.app" ]; then
  echo -e "${RED}Error: PostgresApp not found. Please install it from https://postgresapp.com/${NC}"
  exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
  echo -e "${RED}Error: Node.js is not installed. Please install Node.js.${NC}"
  exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
  echo -e "${RED}Error: npm is not installed. Please install npm.${NC}"
  exit 1
fi

# Check if iTerm is running
if ! osascript -e 'tell application "System Events" to (name of processes) contains "iTerm2"' &> /dev/null; then
  echo -e "${YELLOW}Warning: iTerm2 is not running. Starting iTerm2...${NC}"
  open -a iTerm
  sleep 2
fi

# ========================================================
# Helper functions
# ========================================================

# Function to log messages with timestamp
log_message() {
  local level=$1
  local message=$2
  local color=$NC

  case $level in
    "INFO") color=$GREEN ;;
    "WARN") color=$YELLOW ;;
    "ERROR") color=$RED ;;
    "DEBUG") color=$BLUE ;;
  esac

  echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${color}${level}${NC}: ${message}"
}

# Function to check if Redis is running
check_redis() {
  log_message "DEBUG" "Checking Redis connection at localhost:6379..."

  if redis-cli -h localhost -p 6379 -a $REDIS_PASSWORD ping &> /dev/null; then
    log_message "INFO" "Redis is running on localhost:6379"
    return 0
  else
    log_message "ERROR" "Redis is not running or not accessible"
    return 1
  fi
}

# Function to check if PostgreSQL is running
check_postgres() {
  log_message "DEBUG" "Checking PostgreSQL connection at $PG_HOST:$PG_PORT..."

  if pg_isready -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" &> /dev/null; then
    log_message "INFO" "PostgreSQL is running on $PG_HOST:$PG_PORT"
    return 0
  else
    log_message "ERROR" "PostgreSQL is not running or not accessible"
    return 1
  fi
}

# Function to check if npm dependencies are installed
check_npm_deps() {
  log_message "DEBUG" "Checking npm dependencies..."

  if [ ! -d "node_modules" ]; then
    log_message "WARN" "Node modules not found. Running npm install..."
    npm install
    if [ $? -ne 0 ]; then
      log_message "ERROR" "Failed to install npm dependencies"
      return 1
    fi
    log_message "INFO" "npm dependencies installed successfully"
  else
    log_message "INFO" "npm dependencies already installed"
  fi
  return 0
}

# ========================================================
# Create iTerm panes and start services
# ========================================================

log_message "INFO" "Starting services in iTerm..."

# Check npm dependencies before starting
check_npm_deps

# Create a temporary script for each pane to ensure proper error handling
REDIS_SCRIPT=$(mktemp)
PG_SCRIPT=$(mktemp)
APP_SCRIPT=$(mktemp)

# Create Redis startup script
cat > "$REDIS_SCRIPT" << EOL
#!/bin/bash
cd "$WORKING_DIR"
echo -e "${BOLD}${CYAN}=== REDIS SERVER ===${NC}"
echo -e "${CYAN}Starting Redis server on port 6379...${NC}"

# Check if Redis is already running
if redis-cli -h localhost -p 6379 -a $REDIS_PASSWORD ping &> /dev/null; then
  echo -e "${YELLOW}Redis is already running on localhost:6379${NC}"
  echo -e "${YELLOW}Monitoring Redis...${NC}"
  redis-cli -h localhost -p 6379 -a $REDIS_PASSWORD monitor
else
  echo -e "${GREEN}Starting new Redis server...${NC}"
  redis-server --port 6379 --requirepass $REDIS_PASSWORD
fi
EOL
chmod +x "$REDIS_SCRIPT"

# Create PostgreSQL startup script
cat > "$PG_SCRIPT" << EOL
#!/bin/bash
cd "$WORKING_DIR"
echo -e "${BOLD}${CYAN}=== POSTGRESQL SERVER ===${NC}"
echo -e "${CYAN}Checking PostgreSQL status...${NC}"

# Check if PostgreSQL is running
if pg_isready -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER"; then
  echo -e "${GREEN}PostgreSQL is running on $PG_HOST:$PG_PORT${NC}"
else
  echo -e "${YELLOW}PostgreSQL is not running. Starting PostgresApp...${NC}"
  open -a Postgres

  # Wait for PostgreSQL to start
  echo -e "${YELLOW}Waiting for PostgreSQL to start...${NC}"
  ATTEMPTS=0
  MAX_ATTEMPTS=30
  while ! pg_isready -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" &> /dev/null; do
    ATTEMPTS=$((ATTEMPTS+1))
    if [ $ATTEMPTS -ge $MAX_ATTEMPTS ]; then
      echo -e "${RED}Failed to start PostgreSQL after $MAX_ATTEMPTS attempts.${NC}"
      echo -e "${RED}Please check PostgresApp and try again.${NC}"
      exit 1
    fi
    echo -n "."
    sleep 1
  done
  echo ""
  echo -e "${GREEN}PostgreSQL is now running!${NC}"
fi

# Display PostgreSQL connection details
echo -e "${CYAN}PostgreSQL connection details:${NC}"
echo -e "Host: $PG_HOST"
echo -e "Port: $PG_PORT"
echo -e "Database: $PG_DB"
echo -e "User: $PG_USER"

# List databases
echo -e "${CYAN}Available databases:${NC}"
psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -d "$PG_DB" -c "\\l"

# Keep the pane alive with a monitoring query
echo -e "${CYAN}Monitoring PostgreSQL activity...${NC}"
while true; do
  echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')] PostgreSQL status:${NC}"
  psql -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" -d "$PG_DB" -c "SELECT count(*) as connections FROM pg_stat_activity;"
  sleep 10
done
EOL
chmod +x "$PG_SCRIPT"

# Create Application startup script
cat > "$APP_SCRIPT" << EOL
#!/bin/bash
cd "$WORKING_DIR"
echo -e "${BOLD}${CYAN}=== ROSHI APPLICATION ===${NC}"

# Give Redis and PostgreSQL some time to start up
echo -e "${CYAN}Giving Redis and PostgreSQL time to start up...${NC}"
sleep 5

echo -e "${CYAN}Waiting for Redis and PostgreSQL to be ready...${NC}"

# Wait for Redis
echo -n "Checking Redis connection..."
ATTEMPTS=0
MAX_ATTEMPTS=30
while ! redis-cli -h localhost -p 6379 -a password ping &> /dev/null; do
  ATTEMPTS=$((ATTEMPTS+1))
  if [ $ATTEMPTS -ge $MAX_ATTEMPTS ]; then
    echo -e "${RED}Failed to connect to Redis after $MAX_ATTEMPTS attempts.${NC}"
    echo -e "${RED}Please check Redis and try again.${NC}"
    exit 1
  fi
  echo -n "."
  sleep 1
done
echo -e "${GREEN} Connected!${NC}"

# Wait for PostgreSQL
echo -n "Checking PostgreSQL connection..."
ATTEMPTS=0
MAX_ATTEMPTS=30
while ! pg_isready -h "$PG_HOST" -p "$PG_PORT" -U "$PG_USER" &> /dev/null; do
  ATTEMPTS=$((ATTEMPTS+1))
  if [ $ATTEMPTS -ge $MAX_ATTEMPTS ]; then
    echo -e "${RED}Failed to connect to PostgreSQL after $MAX_ATTEMPTS attempts.${NC}"
    echo -e "${RED}Please check PostgreSQL and try again.${NC}"
    exit 1
  fi
  echo -n "."
  sleep 1
done
echo -e "${GREEN} Connected!${NC}"

# All services are ready, start the application
echo -e "${GREEN}All services are ready! Starting Roshi application...${NC}"
echo -e "${YELLOW}Running: npm run start:dev${NC}"
cd "$WORKING_DIR" && npm run start:dev
EOL
chmod +x "$APP_SCRIPT"

# AppleScript to create iTerm panes and start services
osascript <<EOF
tell application "iTerm"
  set newWindow to (create window with default profile)

  tell current session of newWindow
    -- Set the title for Redis pane
    set name to "Redis"
    -- Split vertically for Redis
    set redisPane to split horizontally with default profile

    -- Split horizontally for PostgreSQL
    set pgPane to split vertically with default profile

    -- Set the title for PostgreSQL pane
    tell pgPane
      set name to "PostgreSQL"
    end tell

    -- Set the title for Application pane
    set name to "Roshi App"

    -- Start Redis in the Redis pane
    tell redisPane
      write text "bash \"$REDIS_SCRIPT\""
    end tell

    -- Check PostgreSQL in the PostgreSQL pane
    tell pgPane
      write text "bash \"$PG_SCRIPT\""
    end tell

    -- Start the application in the main pane
    write text "bash \"$APP_SCRIPT\""
  end tell
end tell
EOF

# Clean up temporary scripts (will be removed when the terminal session ends)
#rm -f "$REDIS_SCRIPT" "$PG_SCRIPT" "$APP_SCRIPT"

log_message "INFO" "Services startup initiated in iTerm!"
log_message "INFO" "Redis: Running on localhost:6379"
log_message "INFO" "PostgreSQL: Running on $PG_HOST:$PG_PORT"
log_message "INFO" "Application: Starting with 'npm run start:dev'"
log_message "WARN" "Check the iTerm window for detailed service status"
