name: Comment Test Results

on:
  workflow_call:
    inputs:
      results_file:
        required: false
        type: string
        default: 'jest_results.json'

jobs:
  comment:
    name: Make comment with test results
    permissions:
      actions: read
      contents: read
      pull-requests: write

    runs-on: ubuntu-latest

    steps:
      - name: Check out source
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}

      - name: List artifacts
        run: |
          echo "Available artifacts:"
          curl -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
               -H "Accept: application/vnd.github.v3+json" \
               "https://api.github.com/repos/${{ github.repository }}/actions/artifacts"

      - name: Download test results
        uses: actions/download-artifact@v4
        with:
          name: test-results
          path: .

      - name: List files
        run: ls -la

      - name: Post test results as PR comment
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const resultsFile = '${{ inputs.results_file }}';
            const commentTag = '<!-- JEST_RESULTS -->';

            if (!fs.existsSync(resultsFile)) {
              core.setFailed(`Test results file '${resultsFile}' not found.`);
              return;
            }
            const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
            let passedTests = 0;
            let failedTests = 0;
            results.testResults.forEach(suite => {
              suite.assertionResults.forEach(test => {
                if (test.status === 'passed') passedTests++;
                else failedTests++;
              });
            });

            let summary = '## Jest Test Results\n\n';
            // Create summary line for the details tag
            let summaryLine = '';
            if (failedTests === 0) {
              summaryLine = '### All tests passed! 🎉🎉🎉';
            } else {
              summaryLine = `Summary: ${passedTests} ✅ Passed, ${failedTests} ❌ Failed`;
            }
            // Start details tag with summary
            summary += `<details closed>\n<summary>${summaryLine}</summary>\n<br>\n\n`;
            // Add the table inside the details tag
            summary += '| Test Suite | Test Case | Status |\n';
            summary += '| --- | --- | --- |\n';
            results.testResults.forEach(suite => {
              const suiteName = suite.name.replace(/\\/g, '/').split('/').pop();
              suite.assertionResults.forEach(test => {
                const status = test.status === 'passed' ? '✅ Passed' : '❌ Failed';
                summary += `| ${suiteName} | ${test.title} | ${status} |\n`;
              });
            });
            // Close the details tag
            summary += '\n</details>';

            // Delete old comment if exists allways create new one
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            const existingComment = comments.find(
              comment => comment.body && comment.body.includes(commentTag)
            );
            if (existingComment) {
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id
              });
            } 
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: `${commentTag}\n${summary}`
            });
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_AUTH_PACKAGE_TOKEN: ${{ secrets.GH_AUTH_PACKAGE_TOKEN }}
