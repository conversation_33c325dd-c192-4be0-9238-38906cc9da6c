name: Required PR Approvals
on:
  pull_request:
    branches: [dev, main, staging]
    types: [opened, synchronize, reopened, ready_for_review, review_requested, review_request_removed]
  pull_request_review:

jobs:
  check_required_approvals:
    name: Check required approvals for all files
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      checks: write

    steps:
      - name: Check required approvals
        uses: weetbix/configurable-required-approvals@v1
        with:
          requirements: |
            - patterns:
                - 'src/migrations/*'
              requiredApprovals: 2
          github-token: ${{ secrets.GITHUB_TOKEN }}
