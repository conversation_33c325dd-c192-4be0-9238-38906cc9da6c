name: Build and Deploy to ECS

on:
  push:
    branches:
      - main
      - dev
      - staging

jobs:
  deploy:

    runs-on: ubuntu-latest
    environment: ${{ matrix.environment }}
    permissions:
      actions: read
      contents: read
      id-token: write

    strategy:
      matrix:
        include:
          - environment: dev
            cluster_name: flipaclip-dev-ecs
            role_to_assume: arn:aws:iam::008971638716:role/roshiApiDeployRole
            repo_uri: 008971638716.dkr.ecr.us-east-1.amazonaws.com/roshi
            db_intance_identifier: roshi-dev-db-20250310211708953500000002
            branch: dev
            enable_user_events: true
            node_env: development
          - environment: prod
            cluster_name: flipaclip-prod-ecs
            role_to_assume: arn:aws:iam::008971638386:role/roshiApiDeployRole
            repo_uri: 008971638386.dkr.ecr.us-east-1.amazonaws.com/roshi
            db_intance_identifier: roshi-prod-db-20250311201445697200000002
            branch: main
            enable_user_events: false
            node_env: production
          - environment: staging
            cluster_name: flipaclip-prod-ecs  # Same as prod
            role_to_assume: arn:aws:iam::008971638386:role/roshiApiDeployRole  # Same as prod
            repo_uri: 008971638386.dkr.ecr.us-east-1.amazonaws.com/roshi  # Same as prod
            db_intance_identifier: roshi-prod-db-20250311201445697200000002  # Same as prod
            branch: staging
            enable_user_events: false
            node_env: production  # Same as prod
            service_name: roshi-staging  # Different service name (shortened to comply with AWS naming limits)
    env:
      AWS_REGION: us-east-1
      ECS_SERVICE_NAME: roshi
      ENABLE_USER_EVENTS: ${{ matrix.enable_user_events }}
      IMAGE_TAG: ${{ github.sha }}
      NODE_ENV: ${{ matrix.node_env }}
      GH_AUTH_PACKAGE_TOKEN: ${{ secrets.GH_AUTH_PACKAGE_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ matrix.role_to_assume }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push Docker image
        if: ${{ github.ref_name == matrix.branch }}
        run: |
          # For staging environment, use 'staging' tag
          if [[ "${{ matrix.environment }}" == "staging" ]]; then
            IMAGE_URI="${{ matrix.repo_uri }}:staging"
          else
            IMAGE_URI="${{ matrix.repo_uri }}:${{ env.IMAGE_TAG }}"
          fi

          docker build --build-arg GH_AUTH_PACKAGE_TOKEN="${{ secrets.GH_AUTH_PACKAGE_TOKEN }}" -t $IMAGE_URI .
          docker push $IMAGE_URI

      - name: Update ECS Task Definition
        if: ${{ github.ref_name == matrix.branch }}
        run: |
          # Use the service_name from matrix if available, otherwise use the default
          SERVICE_NAME="${{ matrix.service_name || env.ECS_SERVICE_NAME }}"

          aws ecs describe-task-definition \
            --task-definition $SERVICE_NAME \
            --region $AWS_REGION > taskdef.json

          # Prepare the jq command with conditional environment variables
          # For staging environment, add IS_STAGING environment variable
          if [[ "${{ matrix.environment }}" == "staging" ]]; then
            jq '.taskDefinition |
              del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy) |
              (.containerDefinitions[0].image="${{ matrix.repo_uri }}:staging") |
              (.containerDefinitions[0].environment += [
                {"name": "TZ", "value": "UTC"},
                {"name": "IS_STAGING", "value": "true"}
              ])' taskdef.json > updated-taskdef.json
          else
            jq '.taskDefinition |
              del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy) |
              (.containerDefinitions[0].image="${{ matrix.repo_uri }}:${{ env.IMAGE_TAG }}") |
              (.containerDefinitions[0].environment += [{"name": "TZ", "value": "UTC"}])' taskdef.json > updated-taskdef.json
          fi

          aws ecs register-task-definition \
            --cli-input-json file://updated-taskdef.json \
            --region $AWS_REGION

      - name: Deploy to ECS
        if: ${{ github.ref_name == matrix.branch }}
        run: |
          # Use the service_name from matrix if available, otherwise use the default
          SERVICE_NAME="${{ matrix.service_name || env.ECS_SERVICE_NAME }}"

          aws ecs update-service \
            --cluster ${{ matrix.cluster_name }} \
            --service $SERVICE_NAME \
            --task-definition $SERVICE_NAME \
            --desired-count ${{ matrix.environment == 'prod' && '3' || '1' }} \
            --health-check-grace-period-seconds ${{ matrix.environment == 'prod' && '60' || '30' }} \
            --region $AWS_REGION

          aws ecs wait services-stable \
            --cluster ${{ matrix.cluster_name }} \
            --services $SERVICE_NAME \
            --region $AWS_REGION

      - name: Get changes in migration folder
        id: get_changes
        uses: tj-actions/changed-files@823fcebdb31bb35fdf2229d9f769b400309430d0
        with:
          files: 'src/migrations/**'

      - name: Take RDS snapshot
        if: ${{ github.ref_name == matrix.branch && steps.get_changes.outputs.any_changed == 'true' }}
        id: snapshot
        run: |
          SNAPSHOT_ID="roshi-db-snapshot-${{ matrix.environment }}-$(date +%Y-%m-%d-%H-%M)"
          echo "SNAPSHOT_ID=$SNAPSHOT_ID" >> $GITHUB_OUTPUT
          aws rds create-db-snapshot \
            --db-instance-identifier "${{ matrix.db_intance_identifier }}" \
            --db-snapshot-identifier "$SNAPSHOT_ID" \
            --region "$AWS_REGION"