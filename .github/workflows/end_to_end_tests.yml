name: End-to-End Tests

on:
  merge_group:
    branches: [dev, main]
  pull_request:
    branches: [dev, main, staging]
    types: [opened, synchronize, reopened]

jobs:
  test:
    name: Run tests on docker-compose environment
    runs-on: ubuntu-latest
    env:
      API_AUTH_TOKEN: ${{ secrets.API_AUTH_TOKEN }}
      ENABLE_SIGNOZ: false
      FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
      FIREBASE_PRIVATE_KEY: ${{ secrets.FIREBASE_PRIVATE_KEY }}
      FIREBASE_CLIENT_EMAIL: ${{ secrets.FIREBASE_CLIENT_EMAIL }}
      GH_AUTH_PACKAGE_TOKEN: ${{ secrets.GH_AUTH_PACKAGE_TOKEN }}
      JWT_SECRET: ${{ secrets.JWT_SECRET }}
      NODE_ENV: local
      POSTGRES_HOST: database-test
      POSTGRES_PORT: ${{ secrets.POSTGRES_PORT }}
      POSTGRES_DB: ${{ secrets.POSTGRES_DB }}
      POSTGRES_USER: ${{ secrets.POSTGRES_USER }}
      POSTGRES_PASSWORD: ${{ secrets.POSTGRES_PASSWORD }}
      REDIS_HOST: redis
      REDIS_PORT: ${{ secrets.REDIS_PORT }}
      REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD }}
      REDIS_TLS: false
      ROSHI_URL: ${{ secrets.ROSHI_URL }}
      SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}
      TEST_ENV: ${{ secrets.TEST_ENV }}
    permissions:
      actions: read
      contents: read
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref || github.head_ref || github.ref_name }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Create test results directory
        run: mkdir -p test-results

      - name: Build and run tests
        env:
          DOCKER_BUILDKIT: 1
        run: |
          # Clean up any existing containers and volumes
          docker compose down -v

          # Start database, redis, and app services with test profile
          docker compose --profile test up -d database-test redis app-test

          # Wait for services to be ready (app-test has healthcheck now)
          echo "Waiting for PostgreSQL to be ready..."
          timeout 60 bash -c 'until docker compose exec database-test pg_isready -U $POSTGRES_USER; do sleep 2; done'

          echo "Waiting for Redis to be ready..."
          timeout 20 bash -c 'until docker compose exec redis redis-cli -a "$REDIS_PASSWORD" ping | grep -q PONG; do sleep 2; done'

          echo "Waiting for application server to be ready (including migrations)..."
          timeout 120 bash -c 'until docker compose exec app-test curl -f http://localhost:3000/health; do echo "App not ready yet, waiting..."; sleep 5; done'

          # Ensure migrations are run (double-check)
          echo "Running database migrations..."
          docker compose exec app-test npm run migration:run || echo "Migrations may have already run"

          # Run tests using docker-compose (simpler and more reliable)
          docker compose --profile test run --rm test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: test-results/jest_results.json
          retention-days: 1

      - name: Cleanup containers
        if: always()
        run: docker compose --profile test down -v

  comment:
    needs: test
    if: always()
    uses: ./.github/workflows/comment_test_result.yml
    secrets: inherit
    permissions:
      actions: read
      contents: read
      pull-requests: write
