#!/bin/bash

# Local End-to-End Test Script
# Mimics the GitHub Actions workflow for local testing

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required environment variables are set
check_env_vars() {
    print_status "Checking required environment variables..."
    
    required_vars=(
        "API_AUTH_TOKEN"
        "FIREBASE_PROJECT_ID"
        "FIREBASE_PRIVATE_KEY"
        "FIREBASE_CLIENT_EMAIL"
        "GH_AUTH_PACKAGE_TOKEN"
        "JWT_SECRET"
        "POSTGRES_PORT"
        "POSTGRES_DB"
        "POSTGRES_USER"
        "POSTGRES_PASSWORD"
        "REDIS_PORT"
        "REDIS_PASSWORD"
        "ROSHI_URL"
        "SLACK_TOKEN"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        print_warning "Please set these variables in your environment or create a .env file"
        print_warning "Example: export API_AUTH_TOKEN='your-token-here'"
        exit 1
    fi
    
    print_success "All required environment variables are set"
}

# Function to load .env file if it exists
load_env_file() {
    if [[ -f .env ]]; then
        print_status "Loading environment variables from .env file..."
        # Use set -a to automatically export variables, then source the file
        set -a
        source .env
        set +a
        print_success "Environment variables loaded from .env"
    else
        print_warning "No .env file found. Make sure environment variables are set manually."
    fi
}

# Function to set default environment variables
set_defaults() {
    export ENABLE_SIGNOZ=${ENABLE_SIGNOZ:-false}
    export NODE_ENV=${NODE_ENV:-local}
    export POSTGRES_HOST=${POSTGRES_HOST:-database-test}
    export REDIS_HOST=${REDIS_HOST:-redis}
    export REDIS_TLS=${REDIS_TLS:-false}
    export DOCKER_BUILDKIT=${DOCKER_BUILDKIT:-1}
}

# Function to create test results directory
create_test_dir() {
    print_status "Creating test results directory..."
    mkdir -p test-results
    print_success "Test results directory created"
}

# Function to cleanup containers
cleanup() {
    print_status "Cleaning up containers and volumes..."
    docker compose --profile test down -v 2>/dev/null || true
    print_success "Cleanup completed"
}

# Function to start services
start_services() {
    print_status "Starting database, redis, and app services..."
    docker compose --profile test up -d database-test redis app-test
    print_success "Services started"
}

# Function to wait for PostgreSQL
wait_for_postgres() {
    print_status "Waiting for PostgreSQL to be ready..."
    timeout 60 bash -c "
        while ! docker compose exec database-test pg_isready -U $POSTGRES_USER; do 
            echo 'PostgreSQL not ready yet, waiting...'
            sleep 2
        done
    "
    print_success "PostgreSQL is ready"
}

# Function to wait for Redis
wait_for_redis() {
    print_status "Waiting for Redis to be ready..."
    timeout 20 bash -c "
        while ! docker compose exec redis redis-cli -a '$REDIS_PASSWORD' ping | grep -q PONG; do 
            echo 'Redis not ready yet, waiting...'
            sleep 2
        done
    "
    print_success "Redis is ready"
}

# Function to wait for application server
wait_for_app() {
    print_status "Waiting for application server to be ready (including migrations)..."
    timeout 120 bash -c "
        while ! docker compose exec app-test curl -f http://localhost:3000/health; do 
            echo 'App not ready yet, waiting...'
            sleep 5
        done
    "
    print_success "Application server is ready"
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations (double-check)..."
    if docker compose exec app-test npm run migration:run; then
        print_success "Migrations completed successfully"
    else
        print_warning "Migrations may have already run or failed"
    fi
}

# Function to run tests
run_tests() {
    print_status "Running end-to-end tests..."
    if docker compose --profile test run --rm test; then
        print_success "Tests completed successfully!"
        return 0
    else
        print_error "Tests failed!"
        return 1
    fi
}

# Function to show logs if tests fail
show_logs() {
    print_status "Showing service logs for debugging..."
    echo ""
    echo "=== App Test Logs ==="
    docker compose logs app-test --tail=50
    echo ""
    echo "=== Database Test Logs ==="
    docker compose logs database-test --tail=20
    echo ""
    echo "=== Redis Logs ==="
    docker compose logs redis --tail=20
}

# Function to check test results
check_results() {
    if [[ -f "test-results/jest_results.json" ]]; then
        print_success "Test results file created: test-results/jest_results.json"
        
        # Try to extract basic info from results
        if command -v jq &> /dev/null; then
            echo ""
            print_status "Test Summary:"
            jq -r '.numTotalTests as $total | .numPassedTests as $passed | .numFailedTests as $failed | "Total: \($total), Passed: \($passed), Failed: \($failed)"' test-results/jest_results.json 2>/dev/null || echo "Could not parse test results"
        fi
    else
        print_warning "Test results file not found"
    fi
}

# Main execution
main() {
    echo "🚀 Starting Local End-to-End Test Runner"
    echo "========================================"
    echo ""
    
    # Load environment
    load_env_file
    set_defaults
    check_env_vars
    
    # Setup
    create_test_dir
    
    # Cleanup any existing containers
    cleanup
    
    # Trap to ensure cleanup on exit
    trap cleanup EXIT
    
    # Start services and run tests
    start_services
    
    # Wait for all services to be ready
    wait_for_postgres
    wait_for_redis
    wait_for_app
    
    # Run migrations
    run_migrations
    
    # Run the tests
    if run_tests; then
        check_results
        echo ""
        print_success "🎉 All tests passed! Your setup is working correctly."
        exit 0
    else
        echo ""
        print_error "❌ Tests failed. Showing logs for debugging..."
        show_logs
        check_results
        echo ""
        print_error "Fix the issues above and try again."
        exit 1
    fi
}

# Help function
show_help() {
    echo "Local End-to-End Test Runner"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  --cleanup      Only run cleanup (remove containers and volumes)"
    echo "  --logs         Show service logs"
    echo ""
    echo "Environment Variables:"
    echo "  You can set required variables in a .env file or export them manually."
    echo "  Required variables: API_AUTH_TOKEN, FIREBASE_PROJECT_ID, etc."
    echo ""
    echo "Examples:"
    echo "  $0                    # Run full test suite"
    echo "  $0 --cleanup          # Clean up containers only"
    echo "  $0 --logs             # Show service logs"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --cleanup)
        cleanup
        exit 0
        ;;
    --logs)
        show_logs
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac