# Roshi NestJS and TypeScript Development Guidelines

## TypeScript General Guidelines

### Basic Principles

- Use English for all code and documentation
- Always declare the type of each variable and function (parameters and return value)
- Avoid using `any`
- Create necessary types
- Use JSDoc to document public classes and methods
- Don't leave blank lines within a function
- One export per file

### Nomenclature

- Use PascalCase for classes
- Use camelCase for variables, functions, and methods
- Use kebab-case for file and directory names
- Use UPPERCASE for environment variables
- Avoid magic numbers and define constants
- Start each function with a verb
- Use verbs for boolean variables (e.g., isLoading, hasError, canDelete)
- Use complete words instead of abbreviations
- Standard abbreviations allowed: API, URL, etc.
- Common abbreviations allowed:
  - i, j for loops
  - err for errors
  - ctx for contexts
  - req, res, next for middleware function parameters

### Imports

- All imports should be alphabetically sorted (sort imports starting with @ first)
- External libs should go on top
- Internal imports should start from the more number of relative paths to lesser ones (i.e: '../../filedir' goes before '../filedir')
- Following previous step, files on same folder will be imported at last
- Finally, all type imports sorted with the same rules as previous steps

### Functions

- Write short functions with a single purpose (< 20 instructions)
- Name functions with a verb and descriptive noun
- Boolean returns: use isX, hasX, canX
- Void returns: use executeX, saveX
- Avoid nesting blocks through:
  - Early checks and returns
  - Extraction to utility functions
- Use higher-order functions (map, filter, reduce)
- Use arrow functions for simple functions (< 3 instructions)
- Use named functions for complex functions
- Use default parameter values
- Follow RO-RO pattern:
  - Use objects for multiple parameters
  - Use objects for return values
  - Declare types for inputs and outputs
- Maintain single level of abstraction

### Comments

- Redudant comments are forbiden (for example if we call a function "createUser" there is no need for a "Call create user" comment)
- Do not explain code, comments should add external context or justify the way the code is written

### Data

- Encapsulate data in composite types
- Use classes with internal validation
- Prefer immutability
- Use readonly for immutable data
- Use as const for literal types

### Classes

- Follow SOLID principles
- Prefer composition over inheritance
- Declare interfaces for contracts
- Keep classes small:
  - < 200 instructions
  - < 10 public methods
  - < 10 properties

### Exceptions

- Use exceptions for unexpected errors
- Catch exceptions only to:
  - Fix expected problems
  - Add context
  - Otherwise, use global handler

### Testing

- Follow Arrange-Act-Assert pattern
- Use clear test variable names
- Follow naming convention: inputX, mockX, actualX, expectedX
- Write unit tests for public functions
- Use test doubles for dependencies
- Write acceptance tests per module
- Follow Given-When-Then convention

## NestJS Specific Guidelines

### Architecture

- Use modular architecture
- Module structure:
  - One module per main domain/route
  - One primary controller per route
  - Secondary controllers for sub-routes
  - Models folder with data types
  - DTOs with class-validator
  - Simple types for outputs
  - Services module for business logic
  - Entities with MikroORM
  - One service per entity

### Core Module

- Global filters for exception handling
- Global middlewares for request management
- Guards for permission management
- Interceptors for request management

### Shared Module

- Utilities
- Shared business logic

### Testing

- Use Jest framework
- Test all controllers and services
- Write end-to-end tests per API module
- Include admin/test method in controllers for smoke testing
