# Application
NODE_ENV=development
PORT=3000
# Auth Token
API_AUTH_TOKEN=

#Seceret Manager
AWS_SECRETS_ENDPOINT=http://localhost:4566
#JWT
AWS_SECRET_NAME=roshi/dev
JWT_SECRET=
JWT_REFRESH_SECRET=

#PG
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# CRON
CRON_ENABLED=false

#SLACK
# Token used for validating delete operations
SLACK_TOKEN=your_secure_token_here

FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_PROJECT_ID=
FIREBASE_STORAGE_BUCKET=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_APP_ID=

#SENTRY
SENTRY_DSN=

#LOGS
LOG_LEVEL=['error', 'warn', 'debug', 'log', 'verbose']
TEST_ENV=live

#REDIS
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=pass
REDIS_TLS=false # false for local redis true for AWS

#KIDSWEBSERVICES
KWS_PRODUCT_CLIENT_ID=
KWS_API_KEY=
KWS_AUTH_URL=
KWS_API_URL=
GEOLOCATION_FALLBACK_IP=*******
GEOLOCATION_SKIP_LOCAL_IPS=true

# Google Play Pass Configuration
GOOGLE_PLAY_PUBLIC_KEY=your-google-play-public-key
GOOGLE_PLAY_ACCESS_TOKEN=your-google-play-access-token
GOOGLE_PLAY_REFRESH_TOKEN=your-google-play-refresh-token
GOOGLE_PLAY_CLIENT_ID=your-google-play-client-id
GOOGLE_PLAY_CLIENT_SECRET=your-google-play-client-secret

#SIGNOZ
OTEL_LOG_LEVEL=none
SIGNOZ_URL=https://signoz.flipaclip.com
ENABLE_SIGNOZ=false