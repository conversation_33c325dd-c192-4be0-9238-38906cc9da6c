# Optimized multi-stage Dockerfile with caching
FROM node:20-alpine AS base

# Install system dependencies once
RUN apk --no-cache add curl tzdata

# Set working directory
WORKDIR /app

# Add build argument for GitHub token
ARG GH_AUTH_PACKAGE_TOKEN

# Copy package files for dependency caching
COPY package*.json ./
COPY .npmrc .npmrc

# Replace the token placeholder in .npmrc with the actual token
RUN sed -i "s/\${GH_AUTH_PACKAGE_TOKEN}/${GH_AUTH_PACKAGE_TOKEN}/g" .npmrc

# Install dependencies with npm cache mount
RUN --mount=type=cache,target=/root/.npm \
    npm ci --prefer-offline --no-audit

# Build stage
FROM base AS builder

# Copy source code
COPY . .

# Build the application with cache mount
RUN --mount=type=cache,target=/app/.nest \
    npm run build

# Production stage
FROM node:20-alpine AS production

# Install system dependencies
RUN apk --no-cache add curl tzdata

# Download the AWS RDS CA certificate bundle
RUN apk --no-cache add wget && \
    mkdir -p /var/app/current/cert/ && \
    wget -O /var/app/current/cert/rds-ca-cert.pem https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem && \
    apk del wget

WORKDIR /var/app/current

# Copy package files
COPY package*.json ./
COPY .npmrc .npmrc

# Add build argument for GitHub token
ARG GH_AUTH_PACKAGE_TOKEN

# Replace the token placeholder in .npmrc with the actual token
RUN sed -i "s/\${GH_AUTH_PACKAGE_TOKEN}/${GH_AUTH_PACKAGE_TOKEN}/g" .npmrc

# Install production dependencies with cache mount
RUN --mount=type=cache,target=/root/.npm \
    npm ci --prefer-offline --no-audit

# Copy built application and necessary files
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src
COPY --from=builder /app/tsconfig*.json ./

# Set environment variables
ENV PORT=3000
ENV TZ=UTC

# Create non-root user and set permissions
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs && \
    chown -R nodejs:nodejs /var/app/current

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start the application with migrations
CMD ["sh", "-c", "npm run migration:run && node dist/main.js"]