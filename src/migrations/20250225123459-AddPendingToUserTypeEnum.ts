import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPendingToUserTypeEnum1709827200001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if PENDING already exists in the enum
    const enumValues = await queryRunner.query(`
      SELECT enum_range(NULL::user_type_enum);
    `);

    if (!enumValues[0].enum_range.includes('PENDING')) {
      await queryRunner.query(`
        ALTER TYPE user_type_enum ADD VALUE 'PENDING';
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE user_type_enum_new AS ENUM ('ANONYMOUS', 'REGISTERED', 'MERGED');

      ALTER TABLE "users" ALTER COLUMN "type" TYPE user_type_enum_new 
      USING type::text::user_type_enum_new;

      DROP TYPE user_type_enum;

      ALTER TYPE user_type_enum_new RENAME TO user_type_enum;
    `);
  }
}
