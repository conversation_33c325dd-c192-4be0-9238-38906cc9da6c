import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTimestampColumns20250225123456 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, create temporary columns with the new timestamp type
    await queryRunner.query(`
      ALTER TABLE "users"
      ADD COLUMN "created_at_temp" TIMESTAMP,
      ADD COLUMN "updated_at_temp" TIMESTAMP,
      ADD COLUMN "installed_at_temp" TIMESTAMP
    `);

    // Convert existing data from bigint to timestamp
    await queryRunner.query(`
      UPDATE "users"
      SET
        "created_at_temp" = to_timestamp("created_at" / 1000),
        "updated_at_temp" = to_timestamp("updated_at" / 1000),
        "installed_at_temp" = to_timestamp("installed_at" / 1000)
    `);

    // Drop the old columns
    await queryRunner.query(`
      ALTER TABLE "users"
      DROP COLUMN "created_at",
      DROP COLUMN "updated_at",
      DROP COLUMN "installed_at"
    `);

    // Rename the temporary columns to the original names (one by one)
    await queryRunner.query(`ALTER TABLE "users" RENAME COLUMN "created_at_temp" TO "created_at"`);
    await queryRunner.query(`ALTER TABLE "users" RENAME COLUMN "updated_at_temp" TO "updated_at"`);
    await queryRunner.query(
      `ALTER TABLE "users" RENAME COLUMN "installed_at_temp" TO "installed_at"`,
    );

    // Add the default constraints for created_at and updated_at
    await queryRunner.query(`
      ALTER TABLE "users"
      ALTER COLUMN "created_at" SET DEFAULT now(),
      ALTER COLUMN "updated_at" SET DEFAULT now()
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // First, create temporary columns with the old bigint type
    await queryRunner.query(`
      ALTER TABLE "users"
      ADD COLUMN "created_at_temp" BIGINT,
      ADD COLUMN "updated_at_temp" BIGINT,
      ADD COLUMN "installed_at_temp" BIGINT
    `);

    // Convert existing data from timestamp to bigint (milliseconds since epoch)
    await queryRunner.query(`
      UPDATE "users"
      SET
        "created_at_temp" = EXTRACT(EPOCH FROM "created_at") * 1000,
        "updated_at_temp" = EXTRACT(EPOCH FROM "updated_at") * 1000,
        "installed_at_temp" = EXTRACT(EPOCH FROM "installed_at") * 1000
    `);

    // Drop the timestamp columns
    await queryRunner.query(`
      ALTER TABLE "users"
      DROP COLUMN "created_at",
      DROP COLUMN "updated_at",
      DROP COLUMN "installed_at"
    `);

    // Rename the temporary columns to the original names (one by one)
    await queryRunner.query(`ALTER TABLE "users" RENAME COLUMN "created_at_temp" TO "created_at"`);
    await queryRunner.query(`ALTER TABLE "users" RENAME COLUMN "updated_at_temp" TO "updated_at"`);
    await queryRunner.query(
      `ALTER TABLE "users" RENAME COLUMN "installed_at_temp" TO "installed_at"`,
    );
  }
}
