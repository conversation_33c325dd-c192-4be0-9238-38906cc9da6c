import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeviceIdToWebhooks1709827200000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "webhooks"
            ADD COLUMN "device_id" text;

            CREATE INDEX "IDX_webhooks_device_id" ON "webhooks" ("device_id");
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX "IDX_webhooks_device_id";
            ALTER TABLE "webhooks" DROP COLUMN "device_id";
        `);
  }
}
