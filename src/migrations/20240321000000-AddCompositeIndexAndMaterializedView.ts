import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompositeIndexAndMaterializedView20240321000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create composite index for device_ids and type using btree
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_device_ids_type_composite 
      ON users USING btree (type) 
      WHERE type != 'MERGED'::user_type_enum;
    `);

    // Create GIN index for device_ids array
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_users_device_ids_gin 
      ON users USING gin (device_ids) 
      WHERE type != 'MERGED'::user_type_enum;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_device_ids_gin;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_device_ids_type_composite;`);
  }
}
