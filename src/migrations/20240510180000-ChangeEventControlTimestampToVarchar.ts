import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeEventControlTimestampToVarchar20240510180000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the index before altering the column type
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_webhooks_event_control_timestamp";
    `);
    // Alter the column type from BIGINT to VARCHAR
    await queryRunner.query(`
      ALTER TABLE "webhooks"
      ALTER COLUMN "event_control_timestamp" TYPE VARCHAR USING "event_control_timestamp"::VARCHAR;
    `);
    // Recreate the index
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhooks_event_control_timestamp" ON "webhooks" ("event_control_timestamp");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the index before reverting the column type
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_webhooks_event_control_timestamp";
    `);
    // Revert the column type from VARCHAR back to BIGINT
    await queryRunner.query(`
      ALTER TABLE "webhooks"
      ALTER COLUMN "event_control_timestamp" TYPE BIGINT USING "event_control_timestamp"::BIGINT;
    `);
    // Recreate the index
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhooks_event_control_timestamp" ON "webhooks" ("event_control_timestamp");
    `);
  }
}
