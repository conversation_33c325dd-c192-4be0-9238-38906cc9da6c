import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEventControlFieldsToWebhooks20240427120000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "webhooks"
      ADD COLUMN "event_control_device_id" VARCHAR NULL,
      ADD COLUMN "event_control_timestamp" BIGINT NULL;
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhooks_event_control_device_id" ON "webhooks" ("event_control_device_id");
    `);
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_webhooks_event_control_timestamp" ON "webhooks" ("event_control_timestamp");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_webhooks_event_control_device_id";
    `);
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_webhooks_event_control_timestamp";
    `);
    await queryRunner.query(`
      ALTER TABLE "webhooks"
      DROP COLUMN IF EXISTS "event_control_device_id",
      DROP COLUMN IF EXISTS "event_control_timestamp";
    `);
  }
}
