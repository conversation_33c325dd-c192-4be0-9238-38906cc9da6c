-- Create user_type_enum
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type_enum') THEN
    CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'MERGED');
  END IF;
END $$;

-- Create users table
CREATE TABLE IF NOT EXISTS "users" (
    "fcid" character varying NOT NULL,
    "fcaid" character varying,
    "merged_to" character varying,
    "type" user_type_enum NOT NULL,
    "identifiers" JSONB NOT NULL,
    "device_ids" text[] NOT NULL DEFAULT '{}',
    "properties" JSONB NOT NULL DEFAULT '{}',
    "event_control" JSONB NOT NULL DEFAULT '{}'
    "merged_fcids" text[] NOT NULL DEFAULT '{}',
    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
    "installed_at" TIMESTAMP NOT NULL,
    CONSTRAINT "PK_users_fcid" PRIMARY KEY ("fcid")
);

-- Create indexes for users table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'users') THEN
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_users_fcaid') THEN
            CREATE INDEX "IDX_users_fcaid" ON "users" ("fcaid");
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_users_device_ids') THEN
            CREATE INDEX "IDX_users_device_ids" ON "users" USING GIN ("device_ids");
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_users_identifiers') THEN
            CREATE INDEX "IDX_users_identifiers" ON "users" USING GIN ("identifiers");
        END IF;
    END IF;
END $$;

-- Create indexes for event_control in users table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'users') THEN
        -- Index for accessing device_id within event_control
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_users_event_control_device_id') THEN
            CREATE INDEX "IDX_users_event_control_device_id" ON "users" ((event_control->>'device_id'));
        END IF;
        
        -- Index for timestamp queries within event_control
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_users_event_control_timestamp') THEN
            CREATE INDEX "IDX_users_event_control_timestamp" ON "users" (((event_control->>'timestamp')::numeric));
        END IF;
        
        -- GIN index for full event_control object queries
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_users_event_control') THEN
            CREATE INDEX "IDX_users_event_control" ON "users" USING GIN ("event_control");
        END IF;
    END IF;
END $$;

-- Create webhooks table
CREATE TABLE IF NOT EXISTS "webhooks" (
    "id" SERIAL PRIMARY KEY,
    "event_timestamp" TIMESTAMP NOT NULL,
    "fcid" VARCHAR NOT NULL,
    "event_name" VARCHAR NOT NULL,
    "provider" VARCHAR NOT NULL,
    "payload" JSONB NOT NULL,
    "modified_properties" JSONB,
    "store" VARCHAR,
    CONSTRAINT "FK_webhooks_users_fcid" FOREIGN KEY ("fcid") REFERENCES "users"("fcid") ON DELETE CASCADE
);

-- Create indexes for webhooks table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'webhooks') THEN
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_webhooks_event_timestamp') THEN
            CREATE INDEX "IDX_webhooks_event_timestamp" ON "webhooks" ("event_timestamp");
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_webhooks_event_name') THEN
            CREATE INDEX "IDX_webhooks_event_name" ON "webhooks" ("event_name");
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_webhooks_payload') THEN
            CREATE INDEX "IDX_webhooks_payload" ON "webhooks" USING GIN (payload);
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_modified_properties') THEN
            CREATE INDEX "IDX_modified_properties" ON "webhooks" USING GIN (modified_properties);
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_fcid') THEN
            CREATE INDEX "IDX_fcid" ON "webhooks" ("fcid");
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IDX_webhooks_store') THEN
            CREATE INDEX "IDX_webhooks_store" ON "webhooks" ("store");
        END IF;
    END IF;
END $$; 
