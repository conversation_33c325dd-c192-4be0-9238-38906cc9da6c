import { MigrationInterface, QueryRunner } from 'typeorm';

export class OptimizeMergedFcidsQuery20250226000000 implements MigrationInterface {
  name = 'OptimizeMergedFcidsQuery20250226000000';

  // Disable transaction for this migration to allow CONCURRENTLY operations
  public readonly transaction = false;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update table statistics first
    await queryRunner.query(`ANALYZE users;`);

    // Create a more efficient index for merged_fcids queries
    // This index excludes MERGED users and only includes non-null merged_fcids
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_merged_fcids_optimized_v2
      ON users USING GIN (merged_fcids)
      WHERE type != 'MERGED'::user_type_enum 
        AND merged_fcids IS NOT NULL 
        AND array_length(merged_fcids, 1) > 0;
    `);

    // Create a partial index for users that have been merged (have merged_fcids)
    // This helps the query planner choose the right index
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_has_merged_fcids
      ON users (fcid, type)
      WHERE type != 'MERGED'::user_type_enum 
        AND merged_fcids IS NOT NULL 
        AND array_length(merged_fcids, 1) > 0;
    `);

    // Create a materialized view for frequently accessed merged user data
    // This will be refreshed periodically and provides fast lookups
    await queryRunner.query(`
      CREATE MATERIALIZED VIEW IF NOT EXISTS mv_merged_users_lookup AS
      SELECT 
        fcid,
        unnest(merged_fcids) as merged_fcid,
        type,
        created_at,
        updated_at
      FROM users 
      WHERE type != 'MERGED'::user_type_enum 
        AND merged_fcids IS NOT NULL 
        AND array_length(merged_fcids, 1) > 0;
    `);

    // Create index on the materialized view for fast lookups
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mv_merged_users_lookup_fcid
      ON mv_merged_users_lookup (merged_fcid, fcid);
    `);

    // Create a unique index to prevent duplicates in the materialized view
    await queryRunner.query(`
      CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_mv_merged_users_lookup_unique
      ON mv_merged_users_lookup (merged_fcid, fcid);
    `);

    // Create a function to refresh the materialized view
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION refresh_merged_users_lookup()
      RETURNS void AS $$
      BEGIN
        REFRESH MATERIALIZED VIEW CONCURRENTLY mv_merged_users_lookup;
      END;
      $$ LANGUAGE plpgsql;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop materialized view and its indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_mv_merged_users_lookup_unique;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_mv_merged_users_lookup_fcid;`);
    await queryRunner.query(`DROP MATERIALIZED VIEW IF EXISTS mv_merged_users_lookup;`);

    // Drop the new indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_has_merged_fcids;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_merged_fcids_optimized_v2;`);
  }
}
