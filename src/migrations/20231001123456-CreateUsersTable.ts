import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUsersTable20231001123456 implements MigrationInterface {
  name = 'CreateUsersTable20231001123456';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DO $$ BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type_enum') THEN
          CREATE TYPE user_type_enum AS ENUM ('ANONYMOUS', 'REGISTERED', 'MERGED');
        END IF;
      END $$;
    `);

    // Create users table
    await queryRunner.query(`
            CREATE TABLE "users" (
                "fcid" character varying NOT NULL,
                "fcaid" character varying,
                "merged_to" character varying,
                "type" user_type_enum NOT NULL,
                "identifiers" JSONB NOT NULL,
                "device_ids" text[] NOT NULL DEFAULT '{}',
                "properties" JSONB NOT NULL DEFAULT '{}',
                "merged_fcids" text[] NOT NULL DEFAULT '{}',
                "created_at" BIGINT NOT NULL,
                "updated_at" BIGINT NOT NULL,
                "installed_at" BIGINT NOT NULL,
                CONSTRAINT "PK_users_fcid" PRIMARY KEY ("fcid")
            )
        `);

    // Create indexes
    await queryRunner.query(`
            CREATE INDEX "IDX_users_fcaid" ON "users" ("fcaid")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_users_device_ids" ON "users" USING GIN ("device_ids")
        `);

    await queryRunner.query(`
            CREATE INDEX "IDX_users_identifiers" ON "users" USING GIN ("identifiers")
        `);

    // Add trigger for automatic updated_at timestamp
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = (EXTRACT(EPOCH FROM now() AT TIME ZONE 'UTC') * 1000)::bigint;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        `);

    await queryRunner.query(`
            CREATE TRIGGER update_users_updated_at
                BEFORE UPDATE ON "users"
                FOR EACH ROW
                EXECUTE FUNCTION update_updated_at_column();
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger and function
    await queryRunner.query(`DROP TRIGGER IF EXISTS update_users_updated_at ON "users"`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS update_updated_at_column`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_identifiers"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_device_ids"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_users_fcaid"`);

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS "users"`);

    // Drop enum type
    await queryRunner.query(`DROP TYPE IF EXISTS user_type_enum`);
  }
}
