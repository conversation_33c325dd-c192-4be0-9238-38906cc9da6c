import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEventControlToUser20250225123459 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users"
      
      ADD COLUMN "event_control" JSONB NOT NULL DEFAULT '{}'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users"
      DROP COLUMN "event_control"
    `);
  }
}
