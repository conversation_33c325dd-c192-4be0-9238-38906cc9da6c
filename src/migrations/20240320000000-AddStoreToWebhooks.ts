import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStoreToWebhooks20240320000000 implements MigrationInterface {
  name = 'AddStoreToWebhooks20240320000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "webhooks" 
      ADD COLUMN "store" VARCHAR;
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_webhooks_store" ON "webhooks" ("store");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_webhooks_store"`);
    await queryRunner.query(`ALTER TABLE "webhooks" DROP COLUMN IF EXISTS "store"`);
  }
}
