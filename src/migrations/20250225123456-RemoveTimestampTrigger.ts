import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveTimestampTrigger20250225123456 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the trigger and function as they're now handled by @UpdateDateColumn
    await queryRunner.query(`DROP TRIGGER IF EXISTS update_users_updated_at ON "users"`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS update_updated_at_column`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the function and trigger if needed to roll back
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = (EXTRACT(EPOCH FROM now() AT TIME ZONE 'UTC') * 1000)::bigint;
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await queryRunner.query(`
      CREATE TRIGGER update_users_updated_at
          BEFORE UPDATE ON "users"
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
    `);
  }
}
