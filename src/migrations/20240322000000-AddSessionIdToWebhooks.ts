import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSessionIdToWebhooks20240322000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "webhooks"
      ADD COLUMN "session_id" BIGINT;

      CREATE INDEX "IDX_webhooks_session_id" ON "webhooks" ("session_id");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_webhooks_session_id";
      ALTER TABLE "webhooks" DROP COLUMN IF EXISTS "session_id";
    `);
  }
}
