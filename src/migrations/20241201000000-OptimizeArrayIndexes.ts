import { MigrationInterface, QueryRunner } from 'typeorm';

export class OptimizeArrayIndexes20241201000000 implements MigrationInterface {
  name = 'OptimizeArrayIndexes20241201000000';

  // Disable transaction for this migration to allow CONCURRENTLY operations
  public readonly transaction = false;

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update table statistics first - critical for query planner
    await queryRunner.query(`ANALYZE users;`);
    await queryRunner.query(`ANALYZE webhooks;`);

    // Create optimized index for device_ids queries with type filter
    // This addresses the highest CPU consumer query pattern
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_device_ids_type_optimized
      ON users USING GIN (device_ids)
      WHERE type != 'MERGED'::user_type_enum;
    `);

    // Create optimized index for merged_fcids queries
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_merged_fcids_optimized
      ON users USING GIN (merged_fcids);
    `);

    // Create composite index for better query planning
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_type_fcid
      ON users (type, fcid)
      WHERE type != 'MERGED'::user_type_enum;
    `);

    // Create index for fcaid lookups excluding merged users
    await queryRunner.query(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_fcaid_not_merged
      ON users (fcaid)
      WHERE type != 'MERGED'::user_type_enum AND fcaid IS NOT NULL;
    `);

    // Execute ALTER SYSTEM commands individually to avoid transaction issues
    try {
      await queryRunner.query(`ALTER SYSTEM SET work_mem = '16MB';`);
    } catch (error) {
      console.warn('Failed to set work_mem, may require superuser privileges:', error.message);
    }

    try {
      await queryRunner.query(`ALTER SYSTEM SET random_page_cost = 1.1;`);
    } catch (error) {
      console.warn(
        'Failed to set random_page_cost, may require superuser privileges:',
        error.message,
      );
    }

    try {
      await queryRunner.query(`SELECT pg_reload_conf();`);
    } catch (error) {
      console.warn(
        'Failed to reload configuration, may require superuser privileges:',
        error.message,
      );
    }

    // Enable pg_stat_statements for monitoring if not already enabled
    try {
      await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS pg_stat_statements;`);
    } catch (error) {
      console.warn(
        'Failed to create pg_stat_statements extension, may require superuser privileges:',
        error.message,
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the optimized indexes
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_device_ids_type_optimized;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_merged_fcids_optimized;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_type_fcid;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_users_fcaid_not_merged;`);

    // Reset PostgreSQL settings to defaults individually
    try {
      await queryRunner.query(`ALTER SYSTEM RESET work_mem;`);
    } catch (error) {
      console.warn('Failed to reset work_mem, may require superuser privileges:', error.message);
    }

    try {
      await queryRunner.query(`ALTER SYSTEM RESET random_page_cost;`);
    } catch (error) {
      console.warn(
        'Failed to reset random_page_cost, may require superuser privileges:',
        error.message,
      );
    }

    try {
      await queryRunner.query(`SELECT pg_reload_conf();`);
    } catch (error) {
      console.warn(
        'Failed to reload configuration, may require superuser privileges:',
        error.message,
      );
    }

    // Note: pg_stat_statements extension is intentionally NOT dropped here
    // as it's a monitoring tool that may be used by other applications,
    // monitoring systems, or DBAs. Dropping it could break external tools
    // and would lose valuable query performance history.
    //
    // If you need to remove it in a test environment, run manually:
    // DROP EXTENSION IF EXISTS pg_stat_statements;
  }
}
