import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateWebhooksTable20231001123502 implements MigrationInterface {
  name = 'CreateWebhooksTable20231001123502';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "webhooks" (
        "id" SERIAL PRIMARY KEY,
        "event_timestamp" TIMESTAMP NOT NULL,
        "fcid" VARCHAR NOT NULL,
        "event_name" VARCHAR NOT NULL,
        "provider" VARCHAR NOT NULL,
        "payload" JSONB NOT NULL,
        "modified_properties" JSONB,
        CONSTRAINT "FK_webhooks_users_fcid" FOREIGN KEY ("fcid") REFERENCES "users"("fcid") ON DELETE CASCADE
      );
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_webhooks_event_timestamp" ON "webhooks" ("event_timestamp");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_webhooks_event_name" ON "webhooks" ("event_name");
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_webhooks_payload ON webhooks USING GIN (payload);
    `);

    await queryRunner.query(`
      CREATE INDEX IDX_modified_properties ON webhooks USING GIN (modified_properties);
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_fcid" ON "webhooks" ("fcid");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_webhooks_event_timestamp"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_webhooks_event_name"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_webhooks_payload"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_modified_properties"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_fcid"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "webhooks"`);
  }
}
