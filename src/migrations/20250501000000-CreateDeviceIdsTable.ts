import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDeviceIdsTable20250501000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Ensure pg_partman is available (run this manually if on RDS and not superuser)
    await queryRunner.query(`
      CREATE EXTENSION IF NOT EXISTS pg_partman;
    `);
    // Create the enum type if not exists
    await queryRunner.query(`
      DO $$ BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'id_type_enum') THEN
          CREATE TYPE id_type_enum AS ENUM ('idfa', 'idfv', 'gaid', 'adid');
        END IF;
      END $$;
    `);
    // Create the partitioned table with composite primary key
    await queryRunner.query(`
      CREATE TABLE "device_ids" (
        "fcid" text NOT NULL,
        "device_id" text NOT NULL,
        "id_type" id_type_enum NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "last_seen" TIMESTAMP NOT NULL,
        CONSTRAINT "PK_device_ids" PRIMARY KEY ("device_id", "id_type", "fcid"),
        CONSTRAINT "FK_device_ids_users_fcid" FOREIGN KEY ("fcid") REFERENCES "users"("fcid") ON DELETE CASCADE
      ) PARTITION BY HASH (device_id);
    `);
    // Create 32 hash partitions
    for (let i = 0; i < 32; i++) {
      await queryRunner.query(`
        CREATE TABLE device_ids_p${i} PARTITION OF device_ids FOR VALUES WITH (MODULUS 32, REMAINDER ${i});
      `);
    }
    // Create indexes on parent (Postgres will propagate to partitions for hash partitioning)
    await queryRunner.query(`CREATE INDEX idx_device_ids_fcid ON device_ids (fcid);`);
    await queryRunner.query(`CREATE INDEX idx_device_ids_device_id ON device_ids (device_id);`);
    await queryRunner.query(
      `CREATE INDEX idx_device_ids_device_id_type ON device_ids (device_id, id_type);`,
    );
    await queryRunner.query(`CREATE INDEX idx_device_ids_fcid_type ON device_ids (fcid, id_type);`);
    await queryRunner.query(
      `CREATE INDEX idx_device_ids_fcid_device_id ON device_ids (fcid, device_id);`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    for (let i = 0; i < 32; i++) {
      await queryRunner.query(`DROP TABLE IF EXISTS device_ids_p${i};`);
    }
    await queryRunner.query(`DROP INDEX IF EXISTS idx_device_ids_fcid;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_device_ids_device_id;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_device_ids_device_id_type;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_device_ids_fcid_type;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_device_ids_fcid_device_id;`);
    await queryRunner.query(`DROP TABLE IF EXISTS "device_ids";`);
    await queryRunner.query(`DROP TYPE IF EXISTS id_type_enum;`);
    // Optionally, do not drop pg_partman extension here
  }
}
