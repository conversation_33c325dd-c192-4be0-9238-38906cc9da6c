export const throttleConfig = {
  auth: {
    default: {
      ttl: 10000,
      limit: 200,
    },
  },
  users: {
    default: {
      ttl: 10000,
      limit: 150,
    },
  },
  webhooks: {
    default: {
      ttl: 60000,
      limit: 40,
    },
  },
  webhooksBatch: {
    default: {
      ttl: 60000,
      limit: 100,
    },
  },
  health: {
    default: {
      ttl: 60000,
      limit: 10,
    },
  },
};

export function getThrottlerConfig() {
  return Object.values(throttleConfig).map(config => config.default);
}
