import { Config } from './interfaces/config.interface';

import * as dotenv from 'dotenv';
// Load environment variables from .env file before importing configs
dotenv.config();

import { developmentConfig } from './environments/development.config';
import { productionConfig } from './environments/production.config';
import { localConfig } from './environments/local.config';
import { testConfig } from './environments/test.config';

const environment = process.env.NODE_ENV || 'development';

const configurations: Record<string, Config> = {
  development: developmentConfig,
  production: productionConfig,
  test: testConfig,
  local: localConfig,
};

export default (): Config => {
  const config = configurations[environment];
  if (!config) {
    throw new Error(`Configuration for environment "${environment}" not found`);
  }
  return config;
};
