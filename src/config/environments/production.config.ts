import { Config } from '../interfaces/config.interface';
import { throttleConfig } from '../throttle.config';
import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '@nestjs/common';

const environment = process.env.NODE_ENV || 'development';

let sslConfig = {
  rejectUnauthorized: true,
  require: true,
  ca: '',
};

if (environment === 'production') {
  const logger = new Logger('ProductionConfig');
  try {
    const caCertPath = path.resolve('/var/app/current/cert/rds-ca-cert.pem');
    if (fs.existsSync(caCertPath)) {
      const caCert = fs.readFileSync(caCertPath).toString();
      sslConfig = {
        rejectUnauthorized: true,
        require: true,
        ca: caCert,
      };
      logger.log('Successfully loaded RDS CA certificate');
    } else {
      logger.warn('RDS CA certificate not found, using default SSL config');
    }
  } catch (error) {
    logger.warn('Failed to read RDS CA certificate, using default SSL config:', error.message);
  }
}

const isStaging = process.env.IS_STAGING === 'true';
const databaseName = isStaging ? 'staging' : process.env.POSTGRES_DB;

export const productionConfig: Config = {
  port: parseInt(process.env.PORT || '3000', 10),
  environment: 'production',
  jwt: {
    secret: process.env.JWT_SECRET || '',
    refreshSecret: '',
    accessTokenExpiry: '45m', // 45 minutes for now
    refreshTokenExpiry: '7d',
  },
  database: {
    host: process.env.POSTGRES_HOST || '',
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    username: process.env.POSTGRES_USER || '',
    password: process.env.POSTGRES_PASSWORD || '',
    database: databaseName || '',
    // database: process.env.POSTGRES_DB || '',
    ssl: sslConfig,
    typeorm: {
      entities: [__dirname + '/../**/entities/*.ts'],
      migrations: [__dirname + '/../migrations/*.ts'],
      synchronize: false,
      timezone: 'Z',
      logging: false,
      poolSize: 80, // Increase from 50 for 800 ops/sec
      extra: {
        max: 80, // Match poolSize
        min: 30, // Increase warm connections
        idleTimeoutMillis: 120000, // Increase from 60000 to reduce churn
        connectionTimeoutMillis: 5000, // Increase from 2000 - too aggressive
        acquireTimeoutMillis: 10000, // Increase from 2000 - too aggressive
        createTimeoutMillis: 5000, // Increase from 2000
        destroyTimeoutMillis: 5000, // Increase from 2000
        reapIntervalMillis: 1000, // Increase from 500 - less aggressive cleanup
        createRetryIntervalMillis: 200, // Increase from 100
        keepAlive: true,
        keepAliveInitialDelayMillis: 10000, // Increase from 5000
        ssl: sslConfig,
      },
      autoLoadEntities: true,
    },
  },
  cache: {
    ttl: 300,
    max: 1000,
  },
  throttler: throttleConfig,
  logging: {
    levels: ['error', 'warn'],
    prefix: 'Roshi',
    colors: false,
    timestamp: true,
  },
  api: {
    authTokens: [process.env.API_AUTH_TOKEN || '', process.env.API_AUTH_TOKEN2 || ''].filter(
      Boolean,
    ),
    qaUrl: process.env.API_QA_URL || 'https://api-qa.flipaclip.com',
    prodUrl: process.env.API_PROD_URL || 'https://api.flipaclip.com',
  },
  firebase: {
    apiKey: process.env.FIREBASE_API_KEY || '',
    authDomain: process.env.FIREBASE_AUTH_DOMAIN || '',
    projectId: process.env.FIREBASE_PROJECT_ID || '',
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET || '',
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.FIREBASE_APP_ID || '',
    privateKey: process.env.FIREBASE_PRIVATE_KEY || '',
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL || '',
  },
  webhooks: {
    debugEvents: {
      enabled: false,
      maxEventsPerFlush: 30,
    },
    purchasely: {
      fcSecSecret: process.env.X_FC_SEC_SECRET || '',
    },
  },
  slack: {
    token: process.env.SLACK_TOKEN || '',
  },
  geolocation: {
    apiKey: process.env.IPGEOLOCATION_API_KEY || '',
    apiUrl: 'https://api.ipgeolocation.io/v2/ipgeo',
    timeout: 5000,
    fallbackIp: process.env.GEOLOCATION_FALLBACK_IP || '*******',
    skipLocalIps: process.env.GEOLOCATION_SKIP_LOCAL_IPS === 'true' || false,
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || '',
    tls: process.env.REDIS_TLS === 'true',
    connectTimeout: 10000,
    commandTimeout: 5000,
    cache: {
      ttl: 3600,
      max: 1000,
    },
    queue: {
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    },
    enableAutoPipelining: true,
    maxRetriesPerRequest: 3,
    enableOfflineQueue: true,
    enableReadyCheck: true,
    lazyConnect: true,
    showFriendlyErrorStack: true,
  },
  kws: {
    productClientId: process.env.KWS_PRODUCT_CLIENT_ID || '',
    apiKey: process.env.KWS_API_KEY || '',
    authUrl:
      process.env.KWS_AUTH_URL ||
      'https://auth.kidswebservices.com/auth/realms/kws/protocol/openid-connect/token',
    apiUrl: process.env.KWS_API_URL || 'https://api.kidswebservices.com/v1/age',
  },
  disableStacktraceLogging: true,
  google: {
    public_key: process.env.GOOGLE_PLAY_PUBLIC_KEY || '',
    googleAccToken: process.env.GOOGLE_PLAY_ACCESS_TOKEN || '',
    googleRefToken: process.env.GOOGLE_PLAY_REFRESH_TOKEN || '',
    client_id: process.env.GOOGLE_PLAY_CLIENT_ID || '',
    googleClientSecret: process.env.GOOGLE_PLAY_CLIENT_SECRET || '',
  },
  signoz: {
    url: process.env.SIGNOZ_URL || 'http://localhost:4318/v1/logs',
    securityHeaders: {
      'X-Roshi-Service': process.env.SIGNOZ_SEC_HEADER || 'roshi',
    },
  },
  cron: {
    enabled: process.env.CRON_ENABLED === 'true' || true,
  },
  pendingUser: {
    ttlSeconds: parseInt(process.env.PENDING_USER_TTL_SECONDS || '3600', 10), // 1 hour
    lockTtlMs: parseInt(process.env.PENDING_USER_LOCK_TTL_MS || '5000', 10), // 5 seconds
    keyPrefix: process.env.PENDING_USER_KEY_PREFIX || 'pending:user:',
    fcidKeyPrefix: process.env.PENDING_USER_FCID_KEY_PREFIX || 'pending:fcid:',
    lockKeyPrefix: process.env.PENDING_USER_LOCK_KEY_PREFIX || 'pending:lock:',
    enableRedisFallback: process.env.PENDING_USER_ENABLE_REDIS_FALLBACK !== 'false',
    maxRetryAttempts: parseInt(process.env.PENDING_USER_MAX_RETRY_ATTEMPTS || '3', 10),
    retryDelayMs: parseInt(process.env.PENDING_USER_RETRY_DELAY_MS || '1000', 10),
    enableStrictConstraints: process.env.PENDING_USER_ENABLE_STRICT_CONSTRAINTS !== 'false',
    enableCleanup: process.env.PENDING_USER_ENABLE_CLEANUP !== 'false',
    cleanupIntervalSeconds: parseInt(
      process.env.PENDING_USER_CLEANUP_INTERVAL_SECONDS || '3600',
      10,
    ), // 1 hour
  },
};
