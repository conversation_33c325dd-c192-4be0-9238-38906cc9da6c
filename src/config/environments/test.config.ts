import { Config } from '../interfaces/config.interface';
import { throttleConfig } from '../throttle.config';

export const testConfig: Config = {
  port: 3000,
  environment: 'test',
  jwt: {
    secret: 'test_secret',
    refreshSecret: 'test_refresh_secret',
    accessTokenExpiry: '15m',
    refreshTokenExpiry: '7d',
  },
  database: {
    host: 'localhost',
    port: 5432,
    username: 'test_user',
    password: 'test_password',
    database: 'test_db',
    ssl: {
      rejectUnauthorized: false,
      require: false,
      ca: '',
    },
    typeorm: {
      entities: [__dirname + '/../**/entities/*.ts'],
      migrations: [__dirname + '/../migrations/*.ts'],
      synchronize: true,
      timezone: 'Z',
      logging: false,
      poolSize: 10,
      extra: {
        max: 30, // Increased from 20 to handle more concurrent connections
        min: 10, // Increased from 5 to maintain more warm connections
        idleTimeoutMillis: 30000, // Reduced for faster connection recycling
        connectionTimeoutMillis: 5000, // Reduced for faster failure detection
        acquireTimeoutMillis: 5000, // Timeout for acquiring connections from pool
        createTimeoutMillis: 5000, // Timeout for creating new connections
        destroyTimeoutMillis: 5000, // Timeout for destroying connections
        reapIntervalMillis: 1000, // How often to check for idle connections
        createRetryIntervalMillis: 200, // Retry interval for connection creation
        keepAlive: true, // Enable connection keep-alive
        keepAliveInitialDelayMillis: 10000, // Initial delay for keep-alive
        ssl: undefined,
      },
      autoLoadEntities: true,
    },
  },
  cache: {
    ttl: 60, // 1 minute for tests
    max: 100,
  },
  throttler: throttleConfig,
  logging: {
    levels: ['error'],
    prefix: 'Roshi',
    colors: false,
    timestamp: true,
  },
  api: {
    authTokens: [process.env.API_AUTH_TOKEN || '', process.env.API_AUTH_TOKEN2 || ''].filter(
      Boolean,
    ),
    qaUrl: process.env.API_QA_URL || 'https://api-qa.flipaclip.com',
    prodUrl: process.env.API_PROD_URL || 'https://api.flipaclip.com',
  },
  firebase: {
    apiKey: 'test_api_key',
    authDomain: 'test_auth_domain',
    projectId: 'test_project_id',
    storageBucket: 'test_storage_bucket',
    messagingSenderId: 'test_messaging_sender_id',
    appId: 'test_app_id',
  },
  webhooks: {
    debugEvents: {
      enabled: false,
      maxEventsPerFlush: 1,
    },
    purchasely: {
      fcSecSecret: process.env.X_FC_SEC_SECRET || '',
    },
  },
  slack: {
    token: process.env.SLACK_TOKEN || 'test_slack_token',
  },
  geolocation: {
    apiKey: process.env.IPGEOLOCATION_API_KEY || '',
    apiUrl: 'https://api.ipgeolocation.io/v2/ipgeo',
    timeout: 5000,
    fallbackIp: process.env.GEOLOCATION_FALLBACK_IP || '*******',
    skipLocalIps: process.env.GEOLOCATION_SKIP_LOCAL_IPS === 'true' || true,
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || '',
    tls: process.env.REDIS_TLS === 'true',
    connectTimeout: 10000,
    commandTimeout: 5000,
    cache: {
      ttl: 3600,
      max: 1000,
    },
    queue: {
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    },
    enableAutoPipelining: true,
    maxRetriesPerRequest: 3,
    enableOfflineQueue: true,
    enableReadyCheck: true,
    lazyConnect: true,
    showFriendlyErrorStack: true,
  },
  kws: {
    productClientId: process.env.KWS_PRODUCT_CLIENT_ID || '',
    apiKey: process.env.KWS_API_KEY || '',
    authUrl:
      process.env.KWS_AUTH_URL ||
      'https://auth.kidswebservices.com/auth/realms/kws/protocol/openid-connect/token',
    apiUrl: process.env.KWS_API_URL || 'https://api.kidswebservices.com/v1/age',
  },
  disableStacktraceLogging: true,
  google: {
    public_key: 'test_public_key',
    googleAccToken: 'test_access_token',
    googleRefToken: 'test_refresh_token',
    client_id: 'test_client_id',
    googleClientSecret: 'test_client_secret',
  },
  signoz: {
    url: process.env.SIGNOZ_URL || 'http://localhost:4318/v1/logs',
    securityHeaders: {
      'X-Roshi-Service': process.env.SIGNOZ_SEC_HEADER || 'roshi',
    },
  },
  cron: {
    enabled: process.env.CRON_ENABLED === 'true' || false,
  },
  pendingUser: {
    ttlSeconds: parseInt(process.env.PENDING_USER_TTL_SECONDS || '3600', 10), // 1 hour
    lockTtlMs: parseInt(process.env.PENDING_USER_LOCK_TTL_MS || '5000', 10), // 5 seconds
    keyPrefix: process.env.PENDING_USER_KEY_PREFIX || 'pending:user:',
    fcidKeyPrefix: process.env.PENDING_USER_FCID_KEY_PREFIX || 'pending:fcid:',
    lockKeyPrefix: process.env.PENDING_USER_LOCK_KEY_PREFIX || 'pending:lock:',
    enableRedisFallback: process.env.PENDING_USER_ENABLE_REDIS_FALLBACK !== 'false',
    maxRetryAttempts: parseInt(process.env.PENDING_USER_MAX_RETRY_ATTEMPTS || '3', 10),
    retryDelayMs: parseInt(process.env.PENDING_USER_RETRY_DELAY_MS || '1000', 10),
    enableStrictConstraints: process.env.PENDING_USER_ENABLE_STRICT_CONSTRAINTS !== 'false',
    enableCleanup: process.env.PENDING_USER_ENABLE_CLEANUP !== 'false',
    cleanupIntervalSeconds: parseInt(
      process.env.PENDING_USER_CLEANUP_INTERVAL_SECONDS || '3600',
      10,
    ), // 1 hour
  },
};
