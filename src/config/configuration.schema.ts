import * as Joi from 'joi';

export const configurationSchema = Joi.object({
  // Environment and Server
  NODE_ENV: Joi.string().valid('development', 'production', 'test', 'local').default('development'),
  PORT: Joi.number().default(3000),

  // JWT Configuration
  JWT_SECRET: Joi.string().required(),
  JWT_REFRESH_SECRET: Joi.string().required(),
  JWT_ACCESS_TOKEN_EXPIRY: Joi.string().default('15m'),
  JWT_REFRESH_TOKEN_EXPIRY: Joi.string().default('7d'),

  // Database Configuration
  POSTGRES_HOST: Joi.string().required(),
  POSTGRES_PORT: Joi.number().default(5432),
  POSTGRES_USER: Joi.string().required(),
  POSTGRES_PASSWORD: Joi.string().required(),
  POSTGRES_DB: Joi.string().required(),
  RDS_CA_CERT: Joi.string().when('NODE_ENV', {
    is: 'production',
    then: Joi.required(),
    otherwise: Joi.optional(),
  }),

  // API Configuration
  API_AUTH_TOKEN: Joi.string().required(),
  API_AUTH_TOKEN2: Joi.string().optional(),

  // Firebase Configuration
  FIREBASE_API_KEY: Joi.string().required(),
  FIREBASE_AUTH_DOMAIN: Joi.string().required(),
  FIREBASE_PROJECT_ID: Joi.string().required(),
  FIREBASE_STORAGE_BUCKET: Joi.string().required(),
  FIREBASE_MESSAGING_SENDER_ID: Joi.string().required(),
  FIREBASE_APP_ID: Joi.string().required(),
  FIREBASE_PRIVATE_KEY: Joi.string().required(),
  FIREBASE_CLIENT_EMAIL: Joi.string().required(),

  // Webhooks Configuration
  X_FC_SEC_SECRET: Joi.string().required(),

  // Slack Configuration
  SLACK_TOKEN: Joi.string().required(),

  // Sentry Configuration
  SENTRY_DSN: Joi.string().optional(),
  SENTRY_ENABLED: Joi.boolean().default(false),

  // Google Play Pass Configuration
  GOOGLE_PLAY_PUBLIC_KEY: Joi.string().required(),
  GOOGLE_PLAY_ACCESS_TOKEN: Joi.string().required(),
  GOOGLE_PLAY_REFRESH_TOKEN: Joi.string().required(),
  GOOGLE_PLAY_CLIENT_ID: Joi.string().required(),
  GOOGLE_PLAY_CLIENT_SECRET: Joi.string().required(),
});
