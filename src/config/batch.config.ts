export const batchConfig = {
  maxBatchSize: 100, // Maximum events per batch
  chunkSize: 25, // Process 25 events at a time within a batch
  processingTimeout: 30000, // 30 seconds timeout for processing
  cache: {
    ttl: 3600, // Cache results for 1 hour
    max: 1000, // Store up to 1000 processed event results
  },
  retry: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      initialDelay: 5000, // 5 seconds
    },
  },
};
