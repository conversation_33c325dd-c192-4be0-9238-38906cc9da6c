import configuration from './configuration';

const config = configuration();

export const redisConfig = {
  host: config.redis.host,
  port: config.redis.port,
  password: config.redis.password,
  ...(config.redis.tls && {
    tls: {
      rejectUnauthorized: false,
      servername: config.redis.host,
    },
  }),
  // Connection settings
  connectTimeout: config.redis.connectTimeout,
  commandTimeout: config.redis.commandTimeout,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  // Reconnect strategy
  reconnectOnError: (err: Error) => {
    const targetError = 'READONLY';
    if (err.message.includes(targetError)) {
      return true;
    }
    return false;
  },
  cache: {
    ttl: config.redis.cache.ttl,
    max: config.redis.cache.max,
  },
  queue: {
    defaultJobOptions: {
      attempts: config.redis.queue.defaultJobOptions.attempts,
      backoff: {
        type: config.redis.queue.defaultJobOptions.backoff.type,
        delay: config.redis.queue.defaultJobOptions.backoff.delay,
      },
      removeOnComplete: config.redis.queue.defaultJobOptions.removeOnComplete,
      removeOnFail: config.redis.queue.defaultJobOptions.removeOnFail,
    },
  },
  enableAutoPipelining: config.redis.enableAutoPipelining,
  maxRetriesPerRequest: config.redis.maxRetriesPerRequest,
  enableOfflineQueue: config.redis.enableOfflineQueue,
  enableReadyCheck: config.redis.enableReadyCheck,
  lazyConnect: config.redis.lazyConnect,
  showFriendlyErrorStack: config.redis.showFriendlyErrorStack,
};
