import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';
import { DataSource } from 'typeorm';

import configuration from './configuration';

import type { Config } from './interfaces/config.interface';

dotenv.config();

// Set default environment to 'local' if not specified
const environment = process.env.NODE_ENV || 'local';
const isProductionOrDevelopment = environment === 'production' || environment === 'development';

export const getTypeOrmConfig = (configService: ConfigService<Config>) => {
  const dbConfig = configService.get('database');
  if (!dbConfig) {
    throw new Error('Database configuration not found');
  }

  const config = {
    type: 'postgres' as const,
    host: dbConfig.host,
    port: dbConfig.port,
    username: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    entities: dbConfig.typeorm.entities,
    migrations: dbConfig.typeorm.migrations,
    synchronize: dbConfig.typeorm.synchronize,
    timezone: dbConfig.typeorm.timezone,
    logging: dbConfig.typeorm.logging,
    poolSize: dbConfig.typeorm.poolSize,
    // Allow migrations to override transaction mode for CONCURRENTLY operations
    migrationsTransactionMode: 'each' as const,
    extra: {
      ...dbConfig.typeorm.extra,
    },
  };

  // Use SSL config from database configuration if available
  if (isProductionOrDevelopment && dbConfig.ssl) {
    (config.extra as any).ssl = dbConfig.ssl;
  }

  return config;
};

export const createDataSource = (configService: ConfigService<Config>) => {
  const config = getTypeOrmConfig(configService);
  return new DataSource(config);
};

// Get configuration based on environment
const defaultConfig = configuration();
// logger.log(`Loaded configuration for environment: ${defaultConfig.environment}`);

// Initialize the config service with the configuration
const configService = new ConfigService<Config>({
  load: [() => defaultConfig],
  isGlobal: true,
  cache: true,
});

// Set the configuration directly
Object.entries(defaultConfig).forEach(([key, value]) => {
  configService.set(key as keyof Config, value);
});

const dataSource = createDataSource(configService);

export default dataSource;
