export interface Config {
  port: number;
  environment: 'development' | 'production' | 'test' | 'local';
  jwt: {
    secret: string;
    refreshSecret: string;
    accessTokenExpiry: string;
    refreshTokenExpiry: string;
  };
  database: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    ssl?: {
      rejectUnauthorized: boolean;
      require: boolean;
      ca: string;
      checkServerIdentity?: () => undefined;
    };
    typeorm: {
      entities: string[];
      migrations: string[];
      synchronize: boolean;
      timezone: string;
      logging: boolean;
      poolSize: number;
      extra: {
        max: number;
        min: number;
        idleTimeoutMillis: number;
        connectionTimeoutMillis: number;
        acquireTimeoutMillis: number;
        createTimeoutMillis: number;
        destroyTimeoutMillis: number;
        reapIntervalMillis: number;
        createRetryIntervalMillis: number;
        keepAlive: boolean;
        keepAliveInitialDelayMillis: number;
        ssl?: {
          rejectUnauthorized: boolean;
          require: boolean;
          ca: string;
        };
      };
      autoLoadEntities: boolean;
    };
  };
  cache: {
    ttl: number;
    max: number;
  };
  throttler: {
    auth: {
      default: {
        ttl: number;
        limit: number;
      };
    };
    users: {
      default: {
        ttl: number;
        limit: number;
      };
    };
    webhooks: {
      default: {
        ttl: number;
        limit: number;
      };
    };
    webhooksBatch: {
      default: {
        ttl: number;
        limit: number;
      };
    };
    health: {
      default: {
        ttl: number;
        limit: number;
      };
    };
  };
  logging: {
    levels: ('error' | 'warn' | 'log' | 'debug' | 'verbose' | 'info')[];
    prefix: string;
    colors: boolean;
    timestamp: boolean;
  };
  api: {
    authTokens: string[];
    qaUrl: string;
    prodUrl: string;
  };
  firebase: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    privateKey?: string;
    clientEmail?: string;
  };
  webhooks: {
    debugEvents: {
      enabled: boolean;
      maxEventsPerFlush: number;
    };
    purchasely: {
      fcSecSecret: string;
    };
  };
  slack: {
    token: string;
  };
  geolocation: {
    apiKey: string;
    apiUrl: string;
    timeout: number;
    fallbackIp: string;
    skipLocalIps: boolean;
  };
  redis: {
    host: string;
    port: number;
    password: string;
    tls: boolean;
    connectTimeout: number;
    commandTimeout: number;
    cache: {
      ttl: 3600;
      max: 1000;
    };
    queue: {
      defaultJobOptions: {
        attempts: 3;
        backoff: {
          type: 'exponential';
          delay: 5000;
        };
        removeOnComplete: true;
        removeOnFail: false;
      };
    };
    enableAutoPipelining: boolean;
    maxRetriesPerRequest: number;
    enableOfflineQueue: boolean;
    enableReadyCheck: boolean;
    lazyConnect: boolean;
    showFriendlyErrorStack: boolean;
  };
  kws: {
    productClientId: string;
    apiKey: string;
    authUrl: string;
    apiUrl: string;
  };
  /**
   * If true, disables stacktrace logging in GlobalExceptionFilter
   */
  disableStacktraceLogging?: boolean;
  google: {
    public_key: string;
    googleAccToken: string;
    googleRefToken: string;
    client_id: string;
    googleClientSecret: string;
  };
  signoz: {
    url: string;
    securityHeaders: {
      'X-Roshi-Service': string;
    };
  };
  /**
   * Controls whether cron jobs are enabled in the application.
   */
  cron: {
    enabled: boolean;
  };
  /**
   * Configuration for pending user management system
   */
  pendingUser: {
    ttlSeconds: number;
    lockTtlMs: number;
    keyPrefix: string;
    fcidKeyPrefix: string;
    lockKeyPrefix: string;
    enableRedisFallback: boolean;
    maxRetryAttempts: number;
    retryDelayMs: number;
    enableStrictConstraints: boolean;
    enableCleanup: boolean;
    cleanupIntervalSeconds: number;
  };
}
