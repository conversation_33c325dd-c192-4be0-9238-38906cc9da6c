'use strict';

import { diag, DiagConsoleLogger, DiagLogLevel } from '@opentelemetry/api';
diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.ERROR);

import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { OTLPLogExporter } from '@opentelemetry/exporter-logs-otlp-http';
import * as opentelemetry from '@opentelemetry/sdk-node';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { Resource } from '@opentelemetry/resources';
import { BatchLogRecordProcessor } from '@opentelemetry/sdk-logs';
import { WinstonInstrumentation } from '@opentelemetry/instrumentation-winston';
import * as dotenv from 'dotenv';
dotenv.config();

const resource = new Resource({
  [SemanticResourceAttributes.SERVICE_NAME]: `roshi-${process.env.NODE_ENV}`,
});

const signozUrl = process.env.SIGNOZ_URL || 'https://signoz.flipaclip.com';
const traceExporter = new OTLPTraceExporter({
  url: `${signozUrl}/v1/traces`,
  headers: {
    'X-Roshi-Service': `${process.env.SIGNOZ_SEC_HEADER}`,
  },
});
const logExporter = new OTLPLogExporter({
  url: `${signozUrl}/v1/logs`,
  headers: {
    'Content-Type': 'application/json',
    'X-Roshi-Service': `${process.env.SIGNOZ_SEC_HEADER}`,
  },
});

const logRecordProcessor = new BatchLogRecordProcessor(logExporter);

const sdk = new opentelemetry.NodeSDK({
  traceExporter,
  logRecordProcessor,
  instrumentations: [
    new WinstonInstrumentation({
      enabled: true,
    }),
    getNodeAutoInstrumentations({
      '@opentelemetry/instrumentation-grpc': {
        enabled: false,
      },
    }),
  ],
  resource,
});

// initialize the SDK and register with the OpenTelemetry API
// this enables the API to record telemetry
sdk.start();

// gracefully shut down the SDK on process exit
process.on('SIGTERM', () => {
  sdk
    .shutdown()
    .then(() => console.log('Tracing terminated'))
    .catch(error => console.log('Error terminating tracing', error))
    .finally(() => process.exit(0));
});

export default sdk;
