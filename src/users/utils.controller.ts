import { DeviceIdentifiersDto, UserResponseDto } from './user.dto';
/**
 * AsyncLock provides a mechanism to prevent duplicate operations by ensuring
 * that concurrent requests with the same key wait for the first operation to complete.
 *
 * This class is used to implement request deduplication, where multiple identical
 * requests return the same result instead of performing the operation multiple times.
 *
 * @example
 * ```typescript
 * const lock = new AsyncLock();
 * const result = await lock.acquire('user-creation-key', async () => {
 *   return await createUser(userData);
 * });
 * ```
 */
export class AsyncLock {
  private locks = new Map<string, Promise<unknown>>();

  /**
   * Acquires a lock for the given key and executes the operation.
   *
   * If a lock already exists for the key, this method waits for the existing
   * operation to complete and returns its result. If no lock exists, it creates
   * a new lock, executes the operation, and automatically cleans up the lock
   * when the operation completes (success or failure).
   *
   * @param key - The unique identifier for the operation. Requests with the same
   *              key will be deduplicated and return the same result.
   * @param operation - The async function to execute. This function will only
   *                   be called once per unique key, even with concurrent requests.
   * @returns A promise that resolves to the result of the operation. All concurrent
   *          requests with the same key will receive the same result.
   *
   * @example
   * ```typescript
   * // Multiple concurrent requests with the same key
   * const results = await Promise.all([
   *   lock.acquire('fcid-123', () => createUser(userData)),
   *   lock.acquire('fcid-123', () => createUser(userData)), // Waits for first
   *   lock.acquire('fcid-123', () => createUser(userData))  // Waits for first
   * ]);
   * // All results will be identical
   * ```
   */
  async acquire<T>(key: string, operation: () => Promise<T>): Promise<T> {
    if (this.locks.has(key)) {
      return this.locks.get(key) as Promise<T>;
    }

    const promise = operation().finally(() => {
      this.locks.delete(key);
    });

    this.locks.set(key, promise);
    return promise;
  }
}

function cleanEmptyIds(ids: string[]): string[] {
  return ids.filter(k => k !== '');
}

// Regex matches empty Ad IDsL: any combination of zeros and - signs (can be all zeros)
function cleanAdvertisingIds(ids: string[]): string[] {
  return ids.filter(k => !k.match(/^[0+-]+$/gm));
}

export function cleanIdentifiers(
  identifiers?: Partial<DeviceIdentifiersDto>,
): DeviceIdentifiersDto {
  return {
    idfa: identifiers?.idfa ? cleanAdvertisingIds(cleanEmptyIds(identifiers?.idfa)) : [],
    idfv: identifiers?.idfv ? cleanEmptyIds(identifiers?.idfv) : [],
    gaid: identifiers?.gaid ? cleanAdvertisingIds(cleanEmptyIds(identifiers?.gaid)) : [],
    adid: identifiers?.adid ? cleanEmptyIds(identifiers?.adid) : [],
  };
}

/**
 * Ensures identifiers and device_ids are always arrays in the user response.
 */
export function normalizeUserResponse(user: Partial<UserResponseDto>): Partial<UserResponseDto> {
  return {
    ...user,
    identifiers: {
      idfa: [],
      idfv: [],
      adid: [],
      gaid: [],
    },
    device_ids: [],
  };
}
