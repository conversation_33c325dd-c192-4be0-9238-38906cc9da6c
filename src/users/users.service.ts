import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';

import { Request } from 'express';

import { DeviceIdDto } from '../auth/jwt.dto';
import { User } from './entities/user.entity';
import { PostgresUserService } from '../common/services/postgres-user.service';
import { Config } from '../config/interfaces/config.interface';
import { GeolocationService } from '../geolocation/geolocation.service';
import { createIdentifyWebhookData } from '../webhooks/handlers/identify-utils';
import { EventsService } from '../webhooks/events.service';
import {
  CreateUserInputDto,
  DeleteUserDto,
  DeviceIdentifiersDto,
  FindUserInputDto,
  ModifiedPropertyDto,
  UsersEndpointInputDTO,
  UsersEndpointOutputDTO,
  UserResponseDto,
  UserType,
  VerifyPlayPassDto,
  VerifyPlayPassResponseDto,
} from './user.dto';
import {
  computeTotalRevenue,
  filterValidAttributes,
  initializeProperties,
  updateUserData,
  verifyGooglePlayPass,
} from './utils.service';
import { calculateUserCurrentAge } from '../common/utils/calculate-user-age';
import { ErrorLoggerService } from '../common/services/error-logger.service';

import { toModifiedProperties } from '../common/utils/modified-properties';
import { PendingUserService } from '../pending-user';
import { isRedisError } from '../common/utils/redis-error.util';

/**
 * Service responsible for managing user operations in the system.
 * Handles user creation, updates, and retrieval while maintaining
 * identifier consistency and property calculations.
 */
@Injectable()
export class UsersService {
  private readonly logger: Logger;

  constructor(
    private readonly pgUserService: PostgresUserService,
    private readonly eventsService: EventsService,
    private readonly configService: ConfigService<Config>,
    private readonly errorLogger: ErrorLoggerService,
    private readonly geolocationService: GeolocationService,
    @Inject(REQUEST) private readonly request: Request,
    private readonly pendingUserService: PendingUserService,
  ) {
    this.logger = new Logger(UsersService.name);
  }

  /**
   * Auth flow for creating JWT tokens.
   *
   * @deprecated This method is deprecated. Use PendingUserService directly in AuthController instead.
   * @param dto - Object containing deviceId (defined)
   * @returns User (if found) or partial user (if created as PENDING) response
   */
  async findOrCreatePendingUser(dto: DeviceIdDto): Promise<Partial<User>> {
    // Use the new pending user service
    const input = {
      deviceId: dto.deviceId,
      adid: dto.adid,
      idfv: dto.idfv,
    };

    const userForJwt = await this.pendingUserService.findOrCreateUserForJwt(input);

    // Double-save device IDs for pending user (restore previous behavior)
    try {
      await this.pgUserService.doubleSaveDeviceIds(userForJwt.fcid, {
        adid: dto.adid ? [dto.adid] : undefined,
        idfv: dto.idfv ? [dto.idfv] : undefined,
      });
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error in doubleSaveDeviceIds for pending user',
        context: 'UsersService.findOrCreatePendingUser',
        fcid: userForJwt.fcid,
        includeStack: true,
        includeRequest: false,
        deviceId: dto.deviceId,
      });
      // Do not throw, just log
    }

    // Map to the expected format
    return {
      fcid: userForJwt.fcid,
      type: UserType.PENDING,
      identifiers: userForJwt.identifiers,
      device_ids: userForJwt.device_ids,
      created_at: userForJwt.created_at,
      updated_at: userForJwt.updated_at,
    };
  }

  /**
   * Creates or updates a user based on provided data.
   * If a user with matching identifiers exists, updates the user;
   * otherwise, creates a new user.
   *
   * @param userDto - The user data for creation/update
   * @returns Partial user response, excluding properties unless specified
   */
  async createOrUpdateUser(userDto: UsersEndpointInputDTO): Promise<UsersEndpointOutputDTO> {
    let existingUser = await this.findExistingUser(userDto);
    if (existingUser) this.logger.debug(`Found existing user with id: ${existingUser?.fcid}`);

    // If user is pending, follow create new user path
    if (existingUser?.fcid && existingUser?.type === UserType.PENDING) {
      return await this.createUser(userDto as CreateUserInputDto, existingUser.fcid);
    }

    // Existing registered user paths
    if (existingUser && existingUser.fcaid) {
      if (userDto.fcaid) {
        // If new fcaid is provided and different from existing, create new user
        if (existingUser.fcaid !== userDto.fcaid) {
          existingUser = null;
          if (!(userDto as CreateUserInputDto).installed_at) {
            throw new BadRequestException('installed_at is required for new users');
          }
          return this.createUser({ ...userDto, fcid: undefined } as CreateUserInputDto);
        }
      } else {
        if (existingUser.fcid === userDto.fcid) {
          this.logger.debug('User without fcaid detected, creating new user');
          const createDto = userDto as CreateUserInputDto;
          if (!createDto.installed_at) {
            throw new BadRequestException('installed_at is required for new users');
          }
          return this.createUser(createDto);
        } else if (!userDto.fcid) {
          // If no fcid in request then check for an already anonymous user
          // with those device ids, if returns null, will create a new anonymous user
          existingUser = userDto.identifiers
            ? await this.findExistingAnonUser(userDto.identifiers)
            : null;
          if (existingUser)
            this.logger.debug(`Found existing ANON user with id: ${existingUser?.fcid}`);
        }
      }
    }

    if (existingUser) {
      if ((userDto as any).installed_at) delete (userDto as any).installed_at;
      const updateDto = userDto;

      /**
       * If updateDto.fcid was merged, follow the merge chain to the final target and update that user.
       * Always map user entities to UserResponseDto using cleanUserResponse for type safety.
       */
      const directMergeTarget =
        updateDto.fcid && existingUser.fcaid === updateDto.fcaid
          ? await this.pgUserService.findMergedUser(updateDto.fcid)
          : null;

      if (existingUser.fcaid === updateDto.fcaid && updateDto.fcid && directMergeTarget) {
        let finalUserToUpdate: User | null = directMergeTarget;
        const visitedFcids = new Set<string>();

        // Follow the entire merge chain to the ultimate target, with cycle detection
        while (finalUserToUpdate && finalUserToUpdate.merged_to) {
          if (visitedFcids.has(finalUserToUpdate.fcid)) {
            this.errorLogger.logError(new Error('Cycle detected in merged_to chain'), undefined, {
              errorName: 'Cycle detected in merged_to chain',
              context: 'UsersService.createOrUpdateUser',
              fcid: updateDto.fcid,
              includeStack: true,
              includeRequest: false,
              deviceId:
                userDto.identifiers?.idfa?.[0] ||
                userDto.identifiers?.idfv?.[0] ||
                userDto.identifiers?.gaid?.[0] ||
                userDto.identifiers?.adid?.[0],
              metadata: {
                fcid: updateDto.fcid,
                merged_to: finalUserToUpdate.merged_to,
              },
            });
            return this.formatUserResponse(existingUser, true);
          }
          visitedFcids.add(finalUserToUpdate.fcid);
          this.logger.debug(
            `Chained merge detected for ${updateDto.fcid}: merged to ${finalUserToUpdate.fcid}, which is further merged to ${finalUserToUpdate.merged_to}. Attempting to update final target.`,
          );
          const next = await this.pgUserService.findByAttribute(
            'fcid',
            finalUserToUpdate.merged_to,
          );
          if (!next) {
            this.logger.debug(
              `Chained merge target for ${updateDto.fcid} (via ${finalUserToUpdate.fcid} to ${finalUserToUpdate.merged_to}) not found. Falling back to returning existingUser.`,
            );
            return this.formatUserResponse(existingUser, true);
          }
          finalUserToUpdate = next;
        }

        if (finalUserToUpdate) {
          // Always map to UserResponseDto for type safety
          const cleanedUser = this.cleanUserResponse(finalUserToUpdate as UserResponseDto);
          // Prevent recursion by updating the DTO's fcid to the resolved user's fcid
          const safeUpdateDto = { ...updateDto, fcid: cleanedUser.fcid };
          this.logger.debug(
            `Updating user ${cleanedUser.fcid} as it is the effective merge target for ${updateDto.fcid}.`,
          );
          // Call the core update logic directly
          const updatedMergedToUser = await updateUserData(
            cleanedUser,
            safeUpdateDto,
            this.logger,
            this.pgUserService,
            this.eventsService,
            this.configService,
            this.errorLogger,
          );
          return updatedMergedToUser;
        }
      }

      if (existingUser.type === UserType.MERGED) {
        this.logger.debug(
          'Attempted to update a merged user. Returning the merged_to user if available.',
        );
        const mergedToFcid = (existingUser as { merged_to?: string }).merged_to;
        if (mergedToFcid) {
          const mergedToUser = await this.pgUserService.findByAttribute('fcid', mergedToFcid);
          if (mergedToUser) {
            return this.formatUserResponse(mergedToUser as UserResponseDto, true);
          }
        }
        // Fallback: return the merged user if merged_to is not set or not found
        return this.formatUserResponse(existingUser, true);
      }

      if (updateDto.type === UserType.ANONYMOUS && updateDto.identifiers) {
        const existingValueKeys = [];
        const newValueKey = [];
        const keys = !updateDto.identifiers.match
          ? Object.keys(updateDto.identifiers)
          : Object.keys(updateDto.identifiers).filter(k => k !== 'match');
        for (const key of keys) {
          const existingValues = (existingUser.identifiers?.[key as keyof DeviceIdentifiersDto] ||
            []) as Array<string>;
          const newValues = (updateDto.identifiers[key as keyof DeviceIdentifiersDto] ||
            []) as Array<string>;
          if (newValues.some(value => existingValues.includes(value))) {
            existingValueKeys.push(key);
          }
          if (newValues.filter(value => !existingValues.includes(value)).length) {
            newValueKey.push(key);
          }
        }
        if (existingValueKeys.length) {
          this.logger.debug(
            `Identifier conflict detected for keys '${existingValueKeys.join(
              ', ',
            )}', returning existing user.`,
          );
          return this.updateExistingUser(existingUser, updateDto);
        }
      }
      // Calculate age and Send Identify Event if user is logged in
      if (!!updateDto.fcaid) {
        const declaredAge = updateDto?.properties?.userDeclaredAge;
        const birthDate = existingUser.properties?.birthDate || updateDto?.properties?.birthDate;
        const calculatedAge = calculateUserCurrentAge(
          existingUser.installed_at,
          declaredAge,
          birthDate,
        );
        if (calculatedAge != null && existingUser.properties?.userCurrentAge !== calculatedAge) {
          const modifiedProperties = {
            ...updateDto.properties,
            userCurrentAge: calculatedAge,
            userDeclaredAge: declaredAge || calculatedAge,
          };
          const updatedUser = await this.updateExistingUser(existingUser, {
            ...updateDto,
            properties: modifiedProperties,
          });
          await this.queueIdentifyEvent(existingUser as User, updatedUser as UserResponseDto, {
            userDeclaredAge: declaredAge || calculatedAge,
            userCurrentAge: calculatedAge,
          });
          return {
            ...updatedUser,
            modified_properties: toModifiedProperties(
              modifiedProperties,
              updatedUser.updated_at.toISOString(),
            ),
          };
        }
      }
      return await this.updateExistingUser(existingUser, updateDto);
    } else {
      const createDto = userDto as CreateUserInputDto;
      if (!createDto.installed_at) {
        throw new BadRequestException('installed_at is required for new users');
      }
      if (createDto.type === UserType.ANONYMOUS) {
        const identifiers = createDto.identifiers || {};
        const hasDeviceId = !!(
          identifiers.idfa?.length ||
          identifiers.idfv?.length ||
          identifiers.gaid?.length ||
          identifiers.adid?.length
        );
        if (!hasDeviceId) {
          throw new BadRequestException('New anonymous users must provide at least one device ID');
        }
      }
      if (
        (createDto.identifiers?.idfa || []).length > 0 &&
        (createDto.identifiers?.idfv || []).length === 0
      ) {
        throw new BadRequestException('Cannot create user with idfa and without idfv');
      }
      const createdUser = await this.createUser(createDto);
      return this.formatUserResponse({ ...createdUser, newUser: true }, true);
    }
  }

  async queueIdentifyEvent(
    existingUser: User,
    updatedUser: UserResponseDto,
    payload: { userDeclaredAge: number; userCurrentAge: number },
  ): Promise<void> {
    const now = new Date();
    type Payload = { store?: string };
    await this.eventsService.addWebhookToPool(
      createIdentifyWebhookData(
        {
          event_name: 'identify',
          event_control: {
            device_id: updatedUser?.device_ids?.[0] || '',
            timestamp: now.getTime(),
          },
          provider: 'FlipaClip',
          fcid: existingUser.fcid || updatedUser.fcid || '',
          store:
            (existingUser as unknown as Payload).store ||
            (updatedUser as unknown as Payload).store ||
            undefined,
          session_id: undefined,
          payload,
          timestamp: now.toISOString(),
        },
        toModifiedProperties(payload, now.toISOString()),
      ),
    );
  }

  /**
   * Finds a user based on search parameters.
   * @param searchParams - Parameters to search for a user
   * @returns User data including properties
   * @throws NotFoundException if no user is found
   */
  async findUser(searchParams: Partial<FindUserInputDto>): Promise<Partial<UserResponseDto>> {
    const user = await this.findExistingUser(searchParams);
    if (!user) throw new NotFoundException('User not found');
    return user;
  }

  /**
   * Searches for an existing user using multiple identifiers.
   * Tries each available identifier sequentially until a match is found.
   * @param dto - FindUserInputDto containing potential identifiers
   * @returns Found user or null if no match
   * @private
   */
  private async findExistingUser(dto: Partial<FindUserInputDto>): Promise<UserResponseDto | null> {
    // Check by fcaid and fcid first
    if (dto.fcaid) {
      const user = await this.pgUserService.findByAttribute('fcaid', dto.fcaid);
      if (user) return this.cleanUserResponse(user);
    }
    if (dto.fcid) {
      const user = await this.pgUserService.findByAttribute('fcid', dto.fcid);
      if (user) return this.cleanUserResponse(user);
    }

    // Check all device IDs, prefer registered user if found
    const deviceTypes = ['idfa', 'idfv', 'gaid', 'adid'] as const;
    for (const type of deviceTypes) {
      const ids = dto.identifiers?.[type];
      if (Array.isArray(ids)) {
        for (const id of ids) {
          if (id) {
            // First check database users (REGISTERED, ANONYMOUS)
            const user = await this.pgUserService.findByAttribute('device_ids', id);
            if (user) {
              return this.cleanUserResponse(user);
            }

            // Then check Redis for PENDING users
            try {
              const pendingUser = await this.pendingUserService.findExistingPendingUser(id);

              if (pendingUser) {
                this.logger.debug(`Found existing PENDING user in Redis: ${pendingUser.fcid}`);
                return {
                  fcid: pendingUser.fcid,
                  type: UserType.PENDING,
                  identifiers: pendingUser.identifiers,
                  device_ids: pendingUser.device_ids,
                  created_at: pendingUser.created_at,
                  updated_at: pendingUser.updated_at,
                } as UserResponseDto;
              }
            } catch (error) {
              if (isRedisError(error)) {
                this.logger.warn(
                  `Redis connectivity error checking for PENDING user with ${type}=${id}:`,
                  error,
                );
                // Continue to next identifier instead of failing the entire lookup
                continue;
              } else {
                this.logger.warn(
                  `Non-Redis error checking for PENDING user with ${type}=${id}:`,
                  error,
                );
              }
            }
          }
        }
      }
    }
    return null;
  }

  /**
   * Searches for an existing ANONYMOUS user using multiple identifiers.
   * @param identifiers - DeviceIdentifiersDto containing potential identifiers
   * @returns Found user or null if no match
   * @private
   */
  private async findExistingAnonUser(
    identifiers: DeviceIdentifiersDto,
  ): Promise<UserResponseDto | null> {
    const deviceTypes = ['idfa', 'idfv', 'gaid', 'adid'] as const;
    for (const type of deviceTypes) {
      const ids = identifiers[type];
      if (Array.isArray(ids)) {
        for (const id of ids) {
          if (id) {
            // Use the optimized findAnonUser method that leverages Redis cache
            const user = await this.pgUserService.findAnonUser(id);
            if (user) {
              return this.cleanUserResponse(user);
            }
          }
        }
      }
    }
    return null;
  }

  /**
   * Helper to clean up user response (dates, empty arrays, etc.)
   */
  private cleanUserResponse(user: UserResponseDto): UserResponseDto {
    return {
      ...user,
      installed_at:
        user.installed_at instanceof Date ? user.installed_at : new Date(user.installed_at),
      mergedFcids: !user || user.mergedFcids?.length === 0 ? undefined : user.mergedFcids,
      identifiers: user.identifiers
        ? {
            adid: user.identifiers?.adid?.length === 0 ? undefined : user.identifiers.adid,
            gaid: user.identifiers?.gaid?.length === 0 ? undefined : user.identifiers.gaid,
            idfa: user.identifiers?.idfa?.length === 0 ? undefined : user.identifiers.idfa,
            idfv: user.identifiers?.idfv?.length === 0 ? undefined : user.identifiers.idfv,
          }
        : undefined,
    };
  }

  /**
   * Updates an existing user with new data, merging identifiers and updating properties.
   *
   * @param existingUser - The current user record in the database
   * @param updateDto - New data to update the user with
   * @returns A partial user response without properties
   */
  private async updateExistingUser(
    existingUser: UserResponseDto,
    updateDto: UsersEndpointInputDTO,
  ): Promise<User> {
    // Ignore payload reinstallCount
    if (updateDto.properties?.reinstallCount) delete updateDto.properties?.reinstallCount;
    if (updateDto.isFreshInstall !== undefined) {
      const { isFreshInstall, ...rest } = updateDto;
      if (isFreshInstall) {
        updateDto = {
          ...rest,
          properties: {
            ...rest.properties,
            reinstallCount: (existingUser.properties?.reinstallCount || 0) + 1,
          },
        };
      } else {
        updateDto = rest;
      }
    }
    return updateUserData(
      existingUser,
      updateDto,
      this.logger,
      this.pgUserService,
      this.eventsService,
      this.configService,
      this.errorLogger,
    );
  }

  private async createUser(
    createUserDto: CreateUserInputDto,
    pendingFcid?: string,
  ): Promise<UsersEndpointOutputDTO> {
    const installed_at =
      createUserDto.installed_at instanceof Date
        ? createUserDto.installed_at
        : new Date(createUserDto.installed_at);
    const type = createUserDto.fcaid ? UserType.REGISTERED : UserType.ANONYMOUS;
    const newUser: Record<string, any> = {
      installed_at,
      identifiers: createUserDto.identifiers,
      mergedFcids: createUserDto?.mergedFcids || [],
      properties: initializeProperties(installed_at, createUserDto.properties),
      type,
    };
    let modified_properties: ModifiedPropertyDto[] | undefined = undefined;
    if (type === UserType.REGISTERED) {
      newUser.fcaid = createUserDto.fcaid;
      const declaredAge = createUserDto?.properties?.userDeclaredAge;
      const birthDate = createUserDto?.properties?.birthDate;
      const calculatedAge = calculateUserCurrentAge(installed_at, declaredAge, birthDate);
      if (calculatedAge != null && createUserDto.properties?.userCurrentAge !== calculatedAge) {
        newUser.properties.userCurrentAge = calculatedAge;
        newUser.properties.userDeclaredAge = calculatedAge;
        if (birthDate) newUser.properties.birthDate = birthDate;
        modified_properties = toModifiedProperties(
          {
            birthDate,
            userCurrentAge: calculatedAge,
            userDeclaredAge: calculatedAge,
          },
          installed_at.toISOString(),
        );
      } else {
        if (birthDate) {
          this.logger.warn(
            `Birth date provided for user ${createUserDto.fcaid} but no age calculated. Check birthDate format.`,
          );
        }
      }
    }
    try {
      const createdUser = pendingFcid
        ? await this.pgUserService.updateUser(pendingFcid, newUser as UserResponseDto)
        : await this.pgUserService.createUser(newUser);

      // Clean up Redis pending user if this was a PENDING to ANONYMOUS transition
      if (pendingFcid) {
        // Use FCID-based cleanup since the device ID in the creation request
        // might not match the original device ID used for JWT creation
        this.pendingUserService.removePendingUserByFcid(pendingFcid).catch(error => {
          // Log but don't fail the user creation
          this.logger.warn(
            `Failed to cleanup Redis pending user after transition: FCID=${pendingFcid}`,
            error,
          );
        });
      }

      if (createUserDto.identifiers) {
        await this.eventsService.generateDebugEvent(
          'User Created',
          (createUserDto.identifiers.idfv?.[0] || createUserDto.identifiers.adid?.[0]) as string,
          createdUser.fcid,
          createUserDto.identifiers,
          createdUser.fcaid,
        );
      }
      if (createdUser.fcid) {
        this.logger.debug(
          `New user created: ${createdUser.fcid}${pendingFcid ? ' [from PENDING]' : ''}`,
        );
        this.queueGeolocationForNewUser(createdUser.fcid);
        return {
          ...this.formatUserResponse(
            createUserDto.isFreshInstall ? { ...createdUser, newUser: true } : createdUser,
            true,
          ),
          modified_properties,
        };
      }
      throw new Error('Failed to create user - no fcid returned');
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Queue a geolocation lookup for a newly created user
   * @param fcid User's fcid
   * @private
   */
  private queueGeolocationForNewUser(fcid: string): void {
    try {
      // Get the user's IP address from the request
      const ip = this.getUserIpAddress();

      if (ip) {
        this.logger.debug(`Queueing geolocation lookup for new user ${fcid} with IP ${ip}`);
        this.geolocationService.queueGeolocation(fcid, ip);
      } else {
        this.logger.debug(`Could not determine IP address for new user ${fcid}`);
      }
    } catch (error) {
      // Don't fail user creation if geolocation fails
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to queue geolocation for user',
        context: 'UsersService.queueGeolocationForNewUser',
        fcid: fcid,
        includeStack: true,
        includeRequest: false,
      });
    }
  }

  /**
   * Extract the user's IP address from the request
   * @returns IP address or null if not found
   * @private
   */
  private getUserIpAddress(): string | null {
    try {
      // Try to get IP from various headers that might contain the real IP
      const ip =
        this.request.headers['x-forwarded-for'] ||
        this.request.headers['x-real-ip'] ||
        this.request.socket.remoteAddress;

      if (!ip) {
        return null;
      }

      // Handle case where x-forwarded-for might contain multiple IPs
      if (typeof ip === 'string') {
        const ips = ip.split(',');
        return ips[0].trim();
      } else if (Array.isArray(ip)) {
        return ip[0].trim();
      }

      return String(ip);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error getting user IP address',
        context: 'UsersService.getUserIpAddress',
        includeStack: true,
        includeRequest: false,
      });
      return null;
    }
  }

  /**
   * Formats the user response object, optionally including or excluding properties.
   * Used to standardize the response format across different endpoints.
   *
   * @param user - Complete user data
   * @param includeProperties - Flag to include properties in response
   * @returns Formatted user response
   * @private
   */
  private formatUserResponse(
    user: Partial<UserResponseDto & { newUser?: boolean }>,
    includeProperties = false,
  ): User | Omit<User, 'properties'> {
    if (!includeProperties) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { properties, ...userWithoutProperties } = user;
      return userWithoutProperties as Omit<User, 'properties'>;
    }
    // Add computed totalRevenue to response
    const responseUser = { ...user };
    if (user.properties) {
      // Compute totalRevenue using the helper method
      const computedTotalRevenue = computeTotalRevenue(responseUser.properties);
      // Assign computed totalRevenue using a type assertion to bypass type restrictions
      (responseUser as any).properties.totalRevenue = computedTotalRevenue;
    }
    // Handle identifiers if they exist
    if (user && user.identifiers) {
      const identifiers = user.identifiers as Record<string, string[]>;
      for (const key of Object.keys(identifiers)) {
        if (!identifiers[key] || !identifiers[key].length) delete identifiers[key];
      }
      responseUser.identifiers = identifiers;
    }
    return filterValidAttributes(responseUser) as User;
  }

  /**
   * Deletes a user based on provided identifier.
   * Searches for the user first to ensure existence and gather all identifiers
   * for proper cache invalidation.
   *
   * @param searchParams - Parameters to identify the user to delete
   * @throws NotFoundException if user not found
   */
  async deleteUser(deleteUserDto: DeleteUserDto): Promise<void> {
    if (!deleteUserDto.fcid) {
      throw new BadRequestException('A valid roshi identifier (fcid) must be provided');
    }

    // Find the user using existing method
    const user = await this.findExistingUser(deleteUserDto);

    if (!user) {
      throw new NotFoundException(`User not found with provided fcid: ${deleteUserDto.fcid}`);
    }

    await this.pgUserService.deleteUser(deleteUserDto.fcid);
    // Also delete any merged users to this one
    await this.pgUserService.deleteMergedUsers(deleteUserDto.fcid);
  }

  async findByFcid(fcid: string): Promise<User | null> {
    return this.pgUserService.findByFcid(fcid);
  }

  /**
   * Finds a user by FCID, leveraging Redis cache for performance.
   * @param fcid - The user's FCID
   * @returns The user if found, otherwise null
   */
  async findUserByFcidCached(fcid: string): Promise<User | null> {
    return this.pgUserService.findByAttribute('fcid', fcid);
  }

  async updateUserProperties(fcid: string, properties: Record<string, any>): Promise<User> {
    const user = await this.pgUserService.findByAttribute('fcid', fcid);
    if (!user) {
      throw new NotFoundException(`User with fcid ${fcid} not found`);
    }

    const updatedUser = {
      ...user,
      properties: {
        ...user.properties,
        ...properties,
      },
    };

    return this.pgUserService.updateUser(fcid, updatedUser);
  }

  async verifyPlayPass(verifyDto: VerifyPlayPassDto): Promise<VerifyPlayPassResponseDto> {
    const { data, signature, fcid, fcaid, identifiers } = verifyDto;

    // Find the user first to ensure they exist
    const user = await this.findUser({ fcid, fcaid, identifiers });
    if (!user?.fcid) {
      throw new BadRequestException('User not found');
    }

    const result = await verifyGooglePlayPass(data, signature, this.configService, this.logger);

    // Update user's google play pass id if we got an order ID
    if (result.orderId) {
      await this.updateUserProperties(user.fcid, {
        ...user.properties,
        googlePlayPassId: result.orderId,
      });

      return {
        orderId: result.orderId,
        modified_properties: [
          {
            affected_at: new Date().toISOString(),
            affected_property: 'googlePlayPassId',
            affected_value: result.orderId,
          },
        ],
      };
    }

    return {
      orderId: result.orderId,
    };
  }
}
