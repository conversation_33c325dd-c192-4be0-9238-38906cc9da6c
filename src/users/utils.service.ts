import { BadRequestException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as iap from 'in-app-purchase';
import type { ValidationResponse } from 'in-app-purchase';

import { Config } from '../config/interfaces/config.interface';
import { PostgresUserService } from '../common/services/postgres-user.service';
import { EventsService } from '../webhooks/events.service';
import {
  DeviceIdentifiersDto,
  PropertiesDto,
  UsersEndpointInputDTO,
  UserResponseDto,
  UserType,
} from './user.dto';
import { ErrorLoggerService } from '../common/services/error-logger.service';
import { getDaysDifference } from './utils/get-days-difference';

import type { User } from './entities/user.entity';

// Regex matches empty Ad ID: any combination of zeros and - signs (can be all zeros)
function cleanAdvertisingIds(ids: string[]): string[] {
  return ids.filter(k => !k.match(/^[0+-]+$/gm));
}

/**
 * Filters out attributes with null or undefined values from an object.
 *
 * @param obj - Object to filter
 * @returns New object with only valid attributes
 * @private
 */
export function filterValidAttributes<T extends object>(obj: T): T {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    // Keep the value if it's not null/undefined, or if it's an object (for nested properties)
    if (value !== null && value !== undefined) {
      acc[key as keyof T] = value;
    }
    return acc;
  }, {} as T);
}

/**
 * Computes the total revenue from ad revenue and subscription revenue
 * @param properties - Properties containing revenue values
 * @returns The computed total revenue
 * @private
 */
const REVENUE_FIELDS: Array<keyof PropertiesDto> = ['totalAdRevenue', 'totalSubscriptionRevenue'];
export function computeTotalRevenue(properties?: PropertiesDto): number {
  if (!properties) return 0;
  // Use reducer to sum all revenue fields, making it easier to add new revenue types
  return REVENUE_FIELDS.reduce((total, field) => {
    const value = properties[field];
    return total + (typeof value === 'number' ? value : 0);
  }, 0);
}

/**
 * Initializes property values for a new user.
 * Sets default values (0) for default properties if not provided.
 * @param installed_at - The installation date of the user.
 * @param properties - Optional existing properties to merge with defaults
 * @returns Initialized properties object
 */
export function initializeProperties(
  installed_at: Date,
  properties?: PropertiesDto,
): PropertiesDto {
  const totalAdRevenue = properties?.totalAdRevenue || 0;
  const totalSubscriptionRevenue = properties?.totalSubscriptionRevenue || 0;
  const totalAdsShown = properties?.totalAdsShown || 0;
  const moengageMigrated = properties?.moengageMigrated || false;
  const reinstallCount = properties?.reinstallCount || 0;
  return {
    totalRevenue: computeTotalRevenue(properties),
    totalAdRevenue,
    totalSubscriptionRevenue,
    moengageMigrated,
    totalAdsShown,
    subscriptionType: '',
    subscriptionState: '',
    totalSubscriptionOffers: 0,
    totalSubscriptionOffersAborted: 0,
    daysAfterInstall: getDaysDifference(installed_at, new Date()),
    reinstallCount,
  };
}

/**
 * Calculates updated property values by combining existing and new values.
 * Handles revenue properties (totalAdRevenue, totalSubscriptionRevenue).
 *
 * @param base - Date to be overwrited
 * @param update - Updated data
 * @returns Combined properties object
 * @private
 */
const calculatePropierties = (base?: PropertiesDto, update?: PropertiesDto): PropertiesDto => {
  // Handle cases where either base or update is undefined
  if (!base && !update) return {};
  if (!base) return { ...update };
  if (!update) return { ...base };

  // Define which properties should be treated as incremental (additive)
  const incrementalProperties = [
    'totalAdRevenue',
    'totalSubscriptionRevenue',
    'totalAdsShown', // Added totalAdsShown as an incremental property
  ];

  // Start with all existing properties
  const result = { ...base };

  // Process all properties from the update object
  if (update) {
    (Object.keys(update) as Array<keyof PropertiesDto>).forEach(key => {
      const value = update[key];
      if (value !== undefined) {
        if (incrementalProperties.includes(key as string)) {
          // For incremental properties, add the new value to the existing value
          const existingValue = (base[key] as number) || 0;
          const newValue = (value as number) || 0;
          (result as any)[key] = existingValue + newValue;
        } else {
          // For non-incremental properties, simply replace the value
          (result as any)[key] = value;
        }
      }
    });
  }

  return result;
};

/**
 * Merges user identifiers from existing and new data, preferring existing values.
 * Handles all supported identifier types (fcaid, fcid, idfa, idfv, gaid, adid).
 *
 * @param base - The current identifiers
 * @param update - New identifiers
 * @returns Identifiers response
 * @private
 */
const mergeDeviceIdentifiers = (
  base?: DeviceIdentifiersDto,
  update?: DeviceIdentifiersDto,
): DeviceIdentifiersDto => {
  if (base === undefined || update === undefined) {
    return { ...base, ...update };
  }
  return {
    idfa:
      update?.idfa && update.idfa.length > 0
        ? Array.from(
            new Set([
              ...(base.idfa ? cleanAdvertisingIds(base.idfa) : []),
              ...cleanAdvertisingIds(update.idfa),
            ]),
          )
        : base.idfa
        ? cleanAdvertisingIds(base.idfa)
        : [],
    idfv:
      update?.idfv && update.idfv.length > 0
        ? Array.from(new Set([...(base.idfv || []), ...update.idfv]))
        : base.idfv || [],
    gaid:
      update?.gaid && update.gaid.length > 0
        ? Array.from(
            new Set([
              ...(base.gaid ? cleanAdvertisingIds(base.gaid) : []),
              ...cleanAdvertisingIds(update.gaid),
            ]),
          )
        : base.gaid
        ? cleanAdvertisingIds(base.gaid)
        : [],
    adid:
      update?.adid && update.adid.length > 0
        ? Array.from(new Set([...(base.adid || []), ...update.adid]))
        : base.adid || [],
  };
};

function createDeviceIdEntries(identifiers?: Partial<DeviceIdentifiersDto>): string[] {
  const entries: string[] = [];
  if (!identifiers) return entries;

  if (identifiers.idfa && Array.isArray(identifiers.idfa)) {
    entries.push(...identifiers.idfa);
  }
  if (identifiers.idfv && Array.isArray(identifiers.idfv)) {
    entries.push(...identifiers.idfv);
  }
  if (identifiers.gaid && Array.isArray(identifiers.gaid)) {
    entries.push(...identifiers.gaid);
  }
  if (identifiers.adid && Array.isArray(identifiers.adid)) {
    entries.push(...identifiers.adid);
  }
  return entries;
}

/**
 * Merges one user into another, first user fcids are stored,
 * second user is the new MERGED resulting user
 *
 * @param from - User to merge
 * @param to - Target user
 * @returns Merged user
 */
export const mergeUsers = (from: User, to: User): User => {
  // Handle merged FCIDs
  let mergedFcids = [...(from.mergedFcids || [])];
  // Push old fcid to mergedFcids array
  if (from.fcid) mergedFcids.push(from.fcid);
  if (to.mergedFcids) {
    mergedFcids = [...new Set([...mergedFcids, ...to.mergedFcids])];
  }
  const identifiers = mergeDeviceIdentifiers(from.identifiers, to.identifiers);
  return {
    ...from,
    ...to,
    properties: {
      ...calculatePropierties(from.properties, to.properties),
      daysAfterInstall: getDaysDifference(to.installed_at, new Date()),
    },
    // Merge device identifiers, preserving existing when no new ones provided
    identifiers,
    device_ids: createDeviceIdEntries(identifiers),
    mergedFcids,
  };
};

const AVOID_UPDATING: (keyof UsersEndpointInputDTO)[] = ['fcid', 'type'];
const UPDATABLE_PROP: (keyof PropertiesDto)[] = ['moengageMigrated'];
const AVOID_UPDATING_PROP: (keyof PropertiesDto)[] = ['birthDate', 'countryKws'];

export const shouldUpdateUser = (user: User, update: UsersEndpointInputDTO): boolean => {
  for (const key of AVOID_UPDATING) if (update[key]) delete update[key];
  if (update.fcaid && !user.fcaid) return true;
  if (!update.identifiers) return false;
  const updateDeviceIds = createDeviceIdEntries(update.identifiers);
  for (const id of updateDeviceIds) {
    // If any new device ID is found, update is needed
    if (!user.device_ids.includes(id)) return true;
  }
  if (update.properties) {
    for (const key of UPDATABLE_PROP) {
      if (user.properties[key] !== update.properties[key]) return true;
    }
  }
  return false;
};

/**
 * Updates existing user with new user vars
 *
 * @param base - User to update
 * @param update - DTO with updated user variables
 * @returns Updated user
 */
export const updateUserWith = (base: User, update: UsersEndpointInputDTO): User => {
  // Handle merged FCIDs
  let mergedFcids = [...(base.mergedFcids || [])];
  for (const key of AVOID_UPDATING) if (update[key]) delete update[key];
  if (update.mergedFcids) {
    mergedFcids = [...new Set([...mergedFcids, ...update.mergedFcids])];
  }
  const identifiers = mergeDeviceIdentifiers(base.identifiers, update.identifiers);

  // Prevent updating properties in AVOID_UPDATING_PROP if already set in base
  if (update.properties) {
    for (const prop of AVOID_UPDATING_PROP) {
      if (
        base.properties &&
        base.properties[prop] !== undefined &&
        base.properties[prop] !== null
      ) {
        delete update.properties[prop];
      }
    }
  }

  // Create a new object with base properties first
  const result = {
    ...base,
    // Then apply updates, but preserve critical fields
    ...update,
    // Always preserve original installed_at
    installed_at: base.installed_at,
    // Properly merge properties using the calculatePropierties function
    properties: {
      ...calculatePropierties(base.properties, update.properties),
      daysAfterInstall: getDaysDifference(base.installed_at, new Date()),
    },
    // Merge device identifiers, preserving existing when no new ones provided
    identifiers,
    device_ids: createDeviceIdEntries(identifiers),
    mergedFcids,
  };

  // Ensure type is REGISTERED if fcaid is present
  if (result.fcaid) {
    result.type = UserType.REGISTERED;
  }

  return result as User;
};

/**
 * Calls the external /admin/merge-user API to merge an FCID document into a FCAID document.
 * This is a fire-and-forget operation: errors are logged but do not interrupt the main flow.
 * @param fcid - The roshi generated FCID
 * @param fcaid - The firebase user ID
 */
const mergeLogger = new Logger('MergeUserApi');
export function callMergeUserApi(
  fcid: string,
  fcaid: string,
  configService: ConfigService<Config>,
): void {
  const config = configService.get('api');
  const webhooksConfig = configService.get('webhooks');
  const baseUrl = process.env.NODE_ENV === 'development' ? config.qaUrl : config.prodUrl;
  const apiKey = webhooksConfig.purchasely.fcSecSecret;
  if (!apiKey) {
    mergeLogger.error('[callMergeUserApi] X_FC_SEC_SECRET is not set');
    return;
  }
  const url = `${baseUrl}/admin/merge-user`;
  const payload = { fcid, fcaid };
  // Fire-and-forget
  (async () => {
    try {
      const res = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-fc-sec': apiKey,
        },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const text = await res.text();
        mergeLogger.debug(`[callMergeUserApi] Failed: ${res.status} ${res.statusText} - ${text}`);
      }
    } catch (err) {
      mergeLogger.error('[callMergeUserApi] Error:', err);
    }
  })();
}

/**
 * Core user update logic, excluding merge resolution. Used internally to prevent recursion.
 */
export async function updateUserData(
  existingUser: UserResponseDto,
  updateDto: UsersEndpointInputDTO,
  logger: Logger,
  pgUserService: PostgresUserService,
  eventsService: EventsService,
  configService: ConfigService<Config>,
  errorLogger: ErrorLoggerService,
): Promise<User> {
  updateDto.type = updateDto.fcaid ? UserType.REGISTERED : UserType.ANONYMOUS; // Encapsulate mutation
  const isValidTransition =
    existingUser.type === updateDto.type ||
    (existingUser.type === UserType.ANONYMOUS && updateDto.type === UserType.REGISTERED);

  let userToMerge: User | null = null;
  const shouldCheckForMerge =
    updateDto.type === UserType.REGISTERED &&
    !!updateDto.fcid &&
    existingUser.fcid !== updateDto.fcid;
  if (shouldCheckForMerge) {
    userToMerge = await pgUserService.findByAttribute('fcid', updateDto.fcid as string);
  }
  const isMergeOperation = shouldCheckForMerge && userToMerge !== null;

  if (!isValidTransition) {
    errorLogger.logError(new Error('Invalid user type transition'), undefined, {
      errorName: 'Invalid user type transition',
      context: 'updateUserData',
      fcid: existingUser.fcid || undefined,
      includeStack: true,
      includeRequest: false,
      metadata: { existingUser: existingUser, updateDto: updateDto },
    });
    return await pgUserService.createUser(updateDto);
  }

  if (isMergeOperation) {
    if (userToMerge?.type === UserType.MERGED) {
      const message = `Attempted to merge already merged user: ${updateDto.fcid}`;
      logger.warn(message);
      throw new BadRequestException(message);
    }
    logger.debug(`Merging user '${updateDto.fcid}' into '${existingUser.fcid}'.`);
    // 1. Update previous ANONYMOUS user as MERGED and clear its identifiers
    await pgUserService.updateUser(
      updateDto.fcid as string,
      {
        ...updateDto,
        fcaid: undefined,
        type: UserType.MERGED,
        merged_to: existingUser.fcid,
        identifiers: { adid: [], gaid: [], idfa: [], idfv: [] },
        device_ids: [],
      } as unknown as UserResponseDto,
    );
    // 2. Update the REGISTERED user with the merged identifiers
    const registeredUserToUpdate = mergeUsers(userToMerge as User, existingUser as User);
    registeredUserToUpdate.type = UserType.REGISTERED;
    registeredUserToUpdate.fcaid = updateDto.fcaid || undefined;
    const updatedUser = await pgUserService.updateUser(
      registeredUserToUpdate.fcid,
      registeredUserToUpdate,
    );
    if (updateDto.identifiers) {
      await eventsService.generateEvent('user_merged', updateDto.fcid as string, {
        merged_user_id: updateDto.fcid as string,
        master_user_id: registeredUserToUpdate.fcid,
        is_master_user: false,
      });
      await eventsService.generateEvent('user_merged', registeredUserToUpdate.fcid as string, {
        merged_user_id: updateDto.fcid as string,
        master_user_id: registeredUserToUpdate.fcid,
        is_master_user: true,
      });
    }
    return filterValidAttributes(updatedUser);
  } else {
    const user = existingUser as User;
    if (shouldUpdateUser(user, updateDto)) {
      logger.debug(`Updating existing user '${existingUser.fcid}'.`);
      if (updateDto.fcaid && !user.fcaid) {
        callMergeUserApi(user.fcid, updateDto.fcaid, configService);
      }
      const updatedUser = await pgUserService.updateUser(
        existingUser.fcid as string,
        updateUserWith(user, updateDto),
      );
      if (updateDto.identifiers) {
        await eventsService.generateDebugEvent(
          'User Updated',
          (updateDto.identifiers.idfv?.[0] || updateDto.identifiers.adid?.[0]) as string,
          updatedUser.fcid,
          updateDto.identifiers,
          updateDto.fcaid || undefined,
        );
      }
      return filterValidAttributes(updatedUser);
    } else {
      logger.debug(`No changes detected for user '${user.fcid}', skipping update.`);
      return filterValidAttributes(user);
    }
  }
}

interface GoogleValidationResponse extends ValidationResponse {
  service: typeof iap.GOOGLE;
  purchaseTime: number;
  expiryTimeMillis?: number;
  productId?: string;
  orderId?: string;
}

export const verifyGooglePlayPass = async (
  data: string,
  signature: string,
  configService: ConfigService<Config>,
  logger: Logger | Console,
): Promise<{
  orderId: string;
}> => {
  // Configure IAP
  const googleConfig = configService.get('google');
  iap.config({
    test: configService.get('environment') === 'test',
    googlePublicKeyStrSandBox: googleConfig.public_key,
    googlePublicKeyStrLive: googleConfig.public_key,
    googleAccToken: googleConfig.googleAccToken,
    googleRefToken: googleConfig.googleRefToken,
    googleClientID: googleConfig.client_id,
    googleClientSecret: googleConfig.googleClientSecret,
    verbose: false,
  });

  try {
    await iap.setup();
    const validationResponse = (await iap.validate({
      data,
      signature,
    })) as GoogleValidationResponse;

    if (!validationResponse.purchaseTime) {
      throw new BadRequestException('Invalid purchase response');
    }

    const isPlayPass = validationResponse.productId?.includes('playpass') ?? false;

    if (isPlayPass && validationResponse.orderId) {
      return {
        orderId: validationResponse.orderId,
      };
    }

    return {
      orderId: '',
    };
  } catch (error) {
    logger.error(`Error validating play pass: ${error.message}`, error.stack);
    return {
      orderId: '',
    };
  }
};
