import { <PERSON><PERSON><PERSON>, Lo<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';

import { FirebaseService } from '../auth/firebase.service';
import { PostgresUserService } from '../common/services/postgres-user.service';
import { MaterializedViewRefreshService } from '../common/services/materialized-view-refresh.service';
import { User } from './entities/user.entity';
import { DeviceId } from './entities/device-id.entity';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { TokenValidationGuard } from '../guards/token-validation.guard';
import { EventsService } from '../webhooks/events.service';
import { Webhook } from '../webhooks/entities/webhook.entity';
import { RedisModule } from '../common/redis.module';
import { WebhooksModule } from '../webhooks/webhooks.module';
import { GeolocationModule } from '../geolocation/geolocation.module';
import { SlackOrJwtAuthGuard } from '../guards/slack-or-jwt.guard';
import { JwtAuthGuard } from '../guards/auth.guard';
import { PendingUserModule } from '../pending-user';

@Module({
  imports: [
    TypeOrmModule.forFeature([Webhook, User, DeviceId]),
    CacheModule.register({ ttl: 3600 }),
    RedisModule,
    ConfigModule,
    forwardRef(() => WebhooksModule),
    GeolocationModule,
    PendingUserModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    JwtAuthGuard,
    SlackOrJwtAuthGuard,
    PostgresUserService,
    MaterializedViewRefreshService,
    FirebaseService,
    TokenValidationGuard,
    Logger,
    EventsService,
  ],
  exports: [UsersService, PostgresUserService, MaterializedViewRefreshService, TypeOrmModule],
})
export class UsersModule {}
