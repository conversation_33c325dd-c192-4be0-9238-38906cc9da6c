import {
  IsString,
  IsOptional,
  IsNumber,
  ValidateNested,
  IsEnum,
  IsArray,
  IsDate,
  IsBoolean,
  IsObject,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';

import { User, UserType } from './entities/user.entity';

export { UserType };

export const UUIDRegex = /^[0-9a-f]{8}(?:-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i;

export const AdidRegex = /^[0-9a-fA-F]{14,16}$/;

export interface DeviceIdentifiers {
  idfa?: string[];
  idfv?: string[];
  gaid?: string[];
  adid?: string[];
}

type DeviceId = 'idfa' | 'idfv' | 'gaid' | 'adid';

export type UserIdentifierKey = 'fcaid' | 'fcid' | DeviceId;
export class PropertiesDto {
  @IsNumber()
  @IsOptional()
  totalAdRevenue?: number;

  @IsNumber()
  @IsOptional()
  totalSubscriptionRevenue?: number;

  @IsNumber()
  @IsOptional()
  totalAdsShown?: number;

  @IsString()
  @IsOptional()
  subscriptionType?: string;

  @IsString()
  @IsOptional()
  subscriptionState?: string;

  @IsNumber()
  @IsOptional()
  totalSubscriptionOffers?: number;

  @IsNumber()
  @IsOptional()
  totalSubscriptionOffersAborted?: number;

  @IsNumber()
  @IsOptional()
  totalRevenue?: number;

  @IsNumber()
  @IsOptional()
  daysAfterInstall?: number;

  @IsBoolean()
  @IsOptional()
  moengageMigrated?: boolean;

  @IsNumber()
  @IsOptional()
  reinstallCount?: number;

  @IsString()
  @IsOptional()
  ageSelectorId?: string;

  @IsNumber()
  @IsOptional()
  userDeclaredAge?: number;

  @IsNumber()
  @IsOptional()
  userCurrentAge?: number;

  @IsString()
  @IsOptional()
  countryKws?: string;

  @IsString()
  @IsOptional()
  birthDate?: string;
}

// Matches both dummy values and UUIDs
export const AdvertisingRegex = /^([0+-]+|[0-9a-fA-F]{8}(-[0-9a-fA-F]{4}){3}-[0-9a-fA-F]{12})$/;
export class DeviceIdentifiersDto implements DeviceIdentifiers {
  @IsArray()
  @Matches(AdvertisingRegex, { each: true, message: 'Invalid idfa format.' })
  @IsOptional()
  idfa?: string[];

  @IsArray()
  @Matches(UUIDRegex, { each: true, message: 'Invalid idfv format.' })
  @IsOptional()
  idfv?: string[];

  @IsArray()
  @Matches(AdvertisingRegex, { each: true, message: 'Invalid gaid format.' })
  @IsOptional()
  gaid?: string[];

  @IsArray()
  @IsOptional()
  @Matches(AdidRegex, { each: true, message: 'Invalid adid format.' })
  adid?: string[];

  @IsObject()
  @IsOptional()
  match?: Record<DeviceId, string>;
}

export class FindUserInputDto {
  @IsString()
  @IsOptional()
  fcaid?: string | null;

  @IsString()
  @IsOptional()
  fcid?: string | null;

  @ValidateNested()
  @Type(() => DeviceIdentifiersDto)
  @IsOptional()
  identifiers?: DeviceIdentifiersDto;
}

export class UsersEndpointInputDTO extends FindUserInputDto {
  @ValidateNested()
  @Type(() => PropertiesDto)
  @IsOptional()
  properties?: PropertiesDto;

  @IsEnum(UserType)
  @IsOptional()
  type?: UserType;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mergedFcids?: string[];

  @IsBoolean()
  @IsOptional()
  isFreshInstall?: boolean;
}

/**
 * Data transfer object for creating a user
 * with Installation timestamp.
 * Required for new user creation.
 * Must remain immutable after initial setup.
 */
export class CreateUserInputDto extends UsersEndpointInputDTO {
  @IsDate()
  @Type(() => Date)
  installed_at: Date;
}

export class UserResponseDto {
  @IsString()
  @IsOptional()
  fcaid?: string | null;

  /**
   * Installation timestamp.
   * This field is immutable after initial creation.
   */
  @IsDate()
  @Type(() => Date)
  installed_at: Date;

  @IsString()
  @IsOptional()
  fcid?: string | null;

  @IsOptional()
  identifiers?: DeviceIdentifiersDto;

  @IsOptional()
  properties?: PropertiesDto;

  @IsEnum(UserType)
  type: UserType;

  @IsDate()
  @Type(() => Date)
  created_at: Date;

  @IsDate()
  @Type(() => Date)
  updated_at: Date;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mergedFcids?: string[];

  /**
   * If this user has been merged, this field points to the target user's fcid.
   */
  @IsString()
  @IsOptional()
  merged_to?: string;

  @IsBoolean()
  @IsOptional()
  newUser?: boolean;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  device_ids?: string[];
}

export type UsersEndpointOutputDTO = Omit<User, 'properties'> & {
  modified_properties?: ModifiedPropertyDto[];
  newUser?: boolean;
  properties?: PropertiesDto;
};

export type UserWithoutProperties = Omit<UserResponseDto, 'properties'>;

export class DeleteUserDto {
  @IsString({
    message: 'A valid roshi identifier (fcid) must be provided',
  })
  fcid: string;

  @IsString({
    message: 'A token value is required for delete operations',
  })
  token: string;
}

export class VerifyPlayPassDto {
  @IsString()
  @IsOptional()
  fcid?: string;

  @IsString()
  @IsOptional()
  fcaid?: string;

  @ValidateNested()
  @Type(() => DeviceIdentifiersDto)
  @IsOptional()
  identifiers?: DeviceIdentifiersDto;

  @IsString()
  data: string;

  @IsString()
  signature: string;
}

export class ModifiedPropertyDto {
  @IsString()
  affected_at: string;

  @IsString()
  affected_property: string;

  @IsString()
  affected_value: string;
}

export class VerifyPlayPassResponseDto {
  @IsString()
  orderId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ModifiedPropertyDto)
  @IsOptional()
  modified_properties?: ModifiedPropertyDto[];
}
