import {
  Entity,
  Column,
  PrimaryColumn,
  Index,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Webhook } from '../../webhooks/entities/webhook.entity';

export enum UserType {
  ANONYMOUS = 'ANONYMOUS',
  MERGED = 'MERGED',
  PENDING = 'PENDING',
  REGISTERED = 'REGISTERED',
}

@Entity('users')
export class User {
  @PrimaryColumn('text')
  fcid: string;

  @Column('uuid', { nullable: true })
  fcaid?: string;

  @Column('uuid', { nullable: true })
  merged_to?: string;

  @Column({
    type: 'enum',
    enum: UserType,
    default: UserType.ANONYMOUS,
  })
  type: UserType;

  @Column('jsonb', { default: {} })
  event_control: Record<string, any>;

  @Column('jsonb', { default: {} })
  identifiers: Record<string, any>;

  @Index()
  @Column('text', { array: true, default: [] })
  device_ids: string[];

  @Column('jsonb', { default: {} })
  properties: Record<string, any>;

  @Column('uuid', { array: true, default: [], name: 'merged_fcids' })
  mergedFcids: string[];

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @Column({ type: 'timestamp' })
  installed_at: Date;

  @OneToMany(() => Webhook, webhook => webhook.fcid)
  webhooks: Webhook[];
}
