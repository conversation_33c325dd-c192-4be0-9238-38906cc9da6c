import {
  Entity,
  PrimaryColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

export enum DeviceType {
  IDFA = 'idfa',
  IDFV = 'idfv',
  GAID = 'gaid',
  ADID = 'adid',
}

@Entity('device_ids')
export class DeviceId {
  @PrimaryColumn('text')
  device_id: string;

  @PrimaryColumn({ type: 'enum', enum: DeviceType })
  id_type: DeviceType;

  @PrimaryColumn('text')
  fcid: string;

  @ManyToOne(() => User, user => user.fcid, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'fcid', referencedColumnName: 'fcid' })
  user: User;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  last_seen: Date;
}
