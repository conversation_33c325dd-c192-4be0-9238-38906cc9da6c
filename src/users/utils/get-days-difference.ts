const msPerDay = 1000 * 60 * 60 * 24;
const roundStep = (value: number, step: number): number => {
  const inv = 1.0 / step;
  return Math.round(value * inv) / inv;
};
export function getDaysDifference(a: Date, b: Date) {
  const diffInMs = b.getTime() - a.getTime();
  const result = diffInMs / msPerDay;
  return result > 3
    ? Math.floor(result) // More than 3 days: count an integer per day
    : result > 1
    ? roundStep(result, 0.5) // Between 1 - 3 days: count half a day (12 hours) as decimal
    : result > 0.5
    ? roundStep(result, 0.1) // Between a day and half: count with 1 decimal precision
    : Number(result.toFixed(3)); // Less than 12 hours: count with a 3 decimals precision
}
