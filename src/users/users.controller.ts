import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Query,
  NotFoundException,
  UseGuards,
  HttpCode,
  HttpStatus,
  BadRequestException,
  HttpException,
  Param,
  Logger,
  UseInterceptors,
  Inject,
  UsePipes,
  ValidationPipe,
  ForbiddenException,
  Request,
} from '@nestjs/common';
import { <PERSON>acheInterceptor, <PERSON>acheKey, CacheTTL } from '@nestjs/cache-manager';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Throttle } from '@nestjs/throttler';
import crypto from 'crypto';
import { Cache } from 'cache-manager';

import { TransformNestedQuery } from '../common/transform-nested-query';
import { throttleConfig } from '../config/throttle.config';
import { JwtAuthGuard } from '../guards/auth.guard';
import { SlackOrJwtAuthGuard } from '../guards/slack-or-jwt.guard';
import { TokenValidationGuard } from '../guards/token-validation.guard';
import {
  CreateUserInputDto,
  DeleteUserDto,
  DeviceIdentifiersDto,
  FindUserInputDto,
  UsersEndpointInputDTO,
  UserResponseDto,
  VerifyPlayPassDto,
  VerifyPlayPassResponseDto,
} from './user.dto';
import { UsersService } from './users.service';
import { cleanIdentifiers, normalizeUserResponse, AsyncLock } from './utils.controller';
import { JwtDto } from '../auth/jwt.dto';
import { ErrorLoggerService } from '../common/services/error-logger.service';
import { Request as ExpressRequest } from 'express';

const userOperationLock = new AsyncLock();

const validateIdentifiers = (query: Partial<UsersEndpointInputDTO>): boolean => {
  // Check for primary identifiers (fcid, fcaid)
  const hasPrimaryIdentifiers = !!(query.fcid || query.fcaid);

  // Check for device identifiers (must be exactly one per type if present)
  const identifiers = query.identifiers;
  if (identifiers) {
    const deviceKeys = ['idfa', 'idfv', 'gaid', 'adid'] as const;
    let found = false;
    for (const key of deviceKeys) {
      const arr = identifiers[key];
      if (arr !== undefined) {
        if (!Array.isArray(arr) || arr.length !== 1) {
          return false;
        }
        found = true;
      }
    }
    if (found) {
      return true;
    }
  }
  return hasPrimaryIdentifiers;
};

// Utility to deeply sort an object for stable stringification
function deepSortObject(obj: unknown): unknown {
  if (Array.isArray(obj)) {
    return obj.map(deepSortObject);
  } else if (obj && typeof obj === 'object' && obj.constructor === Object) {
    return Object.keys(obj)
      .sort()
      .reduce((result, key) => {
        result[key] = deepSortObject((obj as Record<string, unknown>)[key]);
        return result;
      }, {} as Record<string, unknown>);
  }
  return obj;
}

function stableStringify(obj: unknown): string {
  return JSON.stringify(deepSortObject(obj));
}

const getCachedKey = (body: unknown) =>
  crypto.createHash('sha256').update(stableStringify(body)).digest('hex');
interface AuthenticatedRequest extends ExpressRequest {
  user: JwtDto & { slack?: boolean };
  [key: string]: unknown;
}

@Controller('users')
@UseInterceptors(CacheInterceptor)
export class UsersController {
  private readonly logger: Logger;

  constructor(
    private readonly usersService: UsersService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    this.logger = new Logger(UsersController.name);
  }

  private validateJwtClaimsAgainstRequest(
    jwtUser: { fcid?: string; idfv?: string; adid?: string },
    reqFcid?: string | null,
    identifiers?: DeviceIdentifiersDto,
    context: 'get' | 'createOrUpdate' | 'delete' = 'createOrUpdate',
  ) {
    const { fcid: jwtFcid, idfv: jwtIdfv, adid: jwtAdid } = jwtUser;
    const reqIdfv = identifiers?.idfv?.[0] ?? null;
    const reqAdid = identifiers?.adid?.[0] ?? null;
    const matchIdfv = identifiers?.match?.idfv;
    const matchAdid = identifiers?.match?.adid;
    if (jwtFcid && reqFcid && jwtFcid !== reqFcid) {
      throw new ForbiddenException('JWT fcid does not match request fcid');
    }
    if (jwtIdfv) {
      if ((!reqIdfv || jwtIdfv !== reqIdfv) && (!matchIdfv || jwtIdfv !== matchIdfv)) {
        throw new ForbiddenException('JWT idfv does not match request idfv or match.idfv');
      }
    }
    if (jwtAdid) {
      if ((!reqAdid || jwtAdid !== reqAdid) && (!matchAdid || jwtAdid !== matchAdid)) {
        throw new ForbiddenException('JWT adid does not match request adid or match.adid');
      }
    }
    if (!jwtIdfv && !jwtAdid) {
      if (!(context === 'delete' && jwtFcid && reqFcid && jwtFcid === reqFcid)) {
        throw new ForbiddenException('JWT must contain either idfv or adid');
      }
    }
  }

  @Get()
  @Throttle(throttleConfig.users)
  @HttpCode(HttpStatus.OK)
  @UseGuards(SlackOrJwtAuthGuard)
  @CacheKey('user-lookup')
  @CacheTTL(300) // 5 minutes cache
  async getUser(
    @Query(TransformNestedQuery) query: Partial<FindUserInputDto>,
    @Request() req: AuthenticatedRequest,
  ): Promise<Partial<UserResponseDto>> {
    this.logger.debug(`Received GET method in /users with query: ${JSON.stringify(query)}`);
    if (!validateIdentifiers(query)) {
      throw new BadRequestException('At least one valid identifier must be provided');
    }
    const { fcid: queryFcid, identifiers } = query;
    // Only validate JWT claims if not Slack-authenticated
    if (!req.user?.slack) {
      const jwtUser = req.user as JwtDto;
      this.validateJwtClaimsAgainstRequest(jwtUser, queryFcid, identifiers, 'get');
    }
    try {
      const user = await this.usersService.findUser(query);
      this.logger.debug(`Found user: ${JSON.stringify(user)}`);
      return normalizeUserResponse(user);
    } catch (error) {
      this.errorLogger.logError(error, req, {
        errorName: 'Error finding user',
        context: 'UsersController.getUser',
        includeStack: true,
        includeRequest: true,
        metadata: { query: query },
      });
      throw error;
    }
  }

  /**
   * Creates or updates a user based on the provided user data.
   *
   * @param userDto - The data transfer object containing user information for creation or update.
   * @param req - The authenticated request containing the JWT user claims.
   * @returns A partial user response DTO representing the created or updated user.
   * @throws {BadRequestException} If the identifiers are invalid or user creation/update fails.
   * @throws {ForbiddenException} If the JWT claims do not match the request data.
   * @throws {HttpException} For other HTTP-related errors during processing.
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async createOrUpdateUser(
    @Body() userDto: CreateUserInputDto | UsersEndpointInputDTO,
    @Request() req: AuthenticatedRequest,
  ): Promise<Partial<UserResponseDto>> {
    const dtoStr = JSON.stringify(userDto);
    const body = JSON.stringify(userDto, Object.keys(userDto).sort());
    this.logger.debug(`Received POST method in /users with body: ${dtoStr}`);
    if (!validateIdentifiers(userDto)) {
      throw new BadRequestException(
        'At least one valid identifier must be provided (fcid, fcaid, or exactly one value per device identifier: idfa, idfv, gaid, adid)',
      );
    }
    const jwtUser = req.user as { fcid?: string; idfv?: string; adid?: string };
    const { fcid: bodyFcid, identifiers } = userDto;
    this.validateJwtClaimsAgainstRequest(jwtUser, bodyFcid, identifiers, 'createOrUpdate');
    try {
      const key = getCachedKey(userDto);
      const user = await userOperationLock.acquire(key, async () => {
        const result = await this.usersService.createOrUpdateUser({
          ...userDto,
          properties:
            userDto.properties && Object.keys(userDto.properties).length > 0
              ? userDto.properties
              : undefined,
          identifiers: cleanIdentifiers(userDto.identifiers),
        });

        if (!result) {
          this.errorLogger.logError(new Error('No user returned from service'), req, {
            errorName: 'No user returned from service',
            context: 'UsersController.createOrUpdateUser',
            includeStack: true,
            includeRequest: true,
            metadata: { body: body },
          });
          throw new Error('No user returned from service');
        }

        await this.invalidateUserCache(result);
        this.logger.debug(`Returned user: ${JSON.stringify(result)}`);
        return result;
      });
      return normalizeUserResponse(user);
    } catch (error) {
      this.errorLogger.logError(error, req, {
        errorName: 'Error in /users endpoint',
        context: 'UsersController.createOrUpdateUser',
        includeStack: true,
        includeRequest: true,
        metadata: { body: body },
      });
      if (error instanceof HttpException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create/update user: ${error.message}`);
    }
  }

  @Post('verify-playpass')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async verifyPlayPass(@Body() verifyDto: VerifyPlayPassDto): Promise<VerifyPlayPassResponseDto> {
    this.logger.debug(`Received verify-playpass request with body: ${JSON.stringify(verifyDto)}`);
    if (!validateIdentifiers(verifyDto)) {
      throw new BadRequestException(
        'At least one valid identifier must be provided (fcid, fcaid, or device identifiers)',
      );
    }

    try {
      const result = await this.usersService.verifyPlayPass(verifyDto);
      return result;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error verifying play pass',
        context: 'UsersController.verifyPlayPass',
        includeStack: true,
        includeRequest: false,
        metadata: { verifyDto: verifyDto },
      });
      if (error instanceof HttpException) {
        throw error;
      }
      throw new BadRequestException(`Failed to verify play pass: ${error.message}`);
    }
  }

  private async invalidateUserCache(user: Partial<UserResponseDto>): Promise<void> {
    const cacheKeys = [
      'user-lookup', // Invalidate the GET endpoint cache
      `user:fcid:${user.fcid}`,
      ...(user.fcaid ? [`user:fcaid:${user.fcaid}`] : []),
      ...(user.identifiers?.idfa || []).map(id => `user:device_ids:${id}`),
      ...(user.identifiers?.idfv || []).map(id => `user:device_ids:${id}`),
      ...(user.identifiers?.gaid || []).map(id => `user:device_ids:${id}`),
      ...(user.identifiers?.adid || []).map(id => `user:device_ids:${id}`),
    ];

    await Promise.all(cacheKeys.map(key => this.cacheManager.del(key)));
  }

  /**
   * Deletes a user based on the provided delete user DTO.
   *
   * @param deleteUserDto - The data transfer object containing the FCID and token for user deletion.
   * @param req - The authenticated request containing the JWT user claims.
   * @returns An object with a message indicating the result of the deletion.
   * @throws {NotFoundException} If the user to delete is not found.
   * @throws {BadRequestException} If the request is invalid or deletion fails.
   * @throws {ForbiddenException} If the JWT claims do not match the request data.
   */
  @Delete()
  @HttpCode(HttpStatus.OK)
  @UseGuards(TokenValidationGuard) // DELETE endpoint is not protected by JWT
  async deleteUser(
    @Body() deleteUserDto: DeleteUserDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<{ message: string }> {
    this.logger.warn(
      `Received DELETE method in /users with body: ${JSON.stringify(deleteUserDto)}`,
    );
    const jwtUser = req.user as { fcid?: string; idfv?: string; adid?: string };
    const { fcid: bodyFcid } = deleteUserDto;
    try {
      await this.usersService.deleteUser(deleteUserDto);
      return { message: 'User successfully deleted' };
    } catch (error) {
      this.errorLogger.logError(error, req, {
        errorName: 'Error deleting user',
        context: 'UsersController.deleteUser',
        includeStack: true,
        includeRequest: true,
        metadata: { deleteUserDto: deleteUserDto },
        fcid: bodyFcid,
      });
      if (error.name === 'ConditionalCheckFailedException') {
        throw new NotFoundException('User not found');
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete user: ${error.message}`);
    }
  }

  @Get('get-fcaid/:fcid')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getFcaidByFcid(@Param('fcid') fcid: string) {
    const user = await this.usersService.findByFcid(fcid);
    if (!user) {
      throw new NotFoundException(`User with FCID ${fcid} not found`);
    }
    return { fcaid: user.fcaid };
  }
}
