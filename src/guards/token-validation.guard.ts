import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Config } from '../config/interfaces/config.interface';
import { WinstonLoggerService } from '../common/services/winston-logger.service';

/**
 * Guard that validates the presence of a token value in the request body
 * This is specifically for the Delete endpoint to add an extra layer of security
 */
@Injectable()
export class TokenValidationGuard implements CanActivate {
  constructor(
    private readonly customLogger: WinstonLoggerService,
    private readonly configService: ConfigService<Config>,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const body = request.body;

    // Check if the request body exists
    if (!body) {
      this.customLogger.warn('Delete request missing required body', TokenValidationGuard.name);
      throw new UnauthorizedException('Delete requests require a body');
    }

    // Check if the token field is present in the body
    if (body.token === undefined || body.token === null) {
      this.customLogger.warn(
        'Delete request missing required token field',
        TokenValidationGuard.name,
      );
      throw new UnauthorizedException('Delete requests require a token field for validation');
    }

    // Validate that the token is a string
    if (typeof body.token !== 'string') {
      this.customLogger.warn(
        'Delete request contains invalid token type',
        TokenValidationGuard.name,
      );
      throw new UnauthorizedException('Token must be a string');
    }

    // Validate that the token is not empty
    if (body.token.trim() === '') {
      this.customLogger.warn(
        'Delete request contains empty token field',
        TokenValidationGuard.name,
      );
      throw new UnauthorizedException('Token field cannot be empty');
    }

    // Get the expected token from configuration
    const expectedToken = this.configService.get('slack')?.token;
    if (!expectedToken) {
      this.customLogger.warn('Slack token is not configured', TokenValidationGuard.name);
      throw new UnauthorizedException('Slack token is not configured');
    }

    // Validate that the token matches the expected token
    if (body.token !== expectedToken) {
      this.customLogger.warn('Delete request contains invalid token', TokenValidationGuard.name);
      throw new UnauthorizedException('Invalid token provided');
    }

    this.customLogger.log(
      'Token validation successful for delete request',
      TokenValidationGuard.name,
    );
    return true;
  }
}
