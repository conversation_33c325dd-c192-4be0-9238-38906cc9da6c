import { Inject, Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from './auth.guard';
import { ConfigService } from '@nestjs/config';
import { Config } from '../config/interfaces/config.interface';
import crypto from 'crypto';

/**
 * Guard that allows access if a valid Slack token is provided in the Authorization header,
 * or falls back to JWT authentication. Enhances security by using constant-time comparison
 * and centralizes configuration management.
 */
@Injectable()
export class SlackOrJwtAuthGuard implements CanActivate {
  private readonly slackTokenBuffer: Buffer;

  /**
   * Constructs the guard, fetching the Slack token from configuration and preparing it for secure comparison.
   * @param reflector - The NestJS reflector for metadata access.
   * @param jwtAuthGuard - The standard JWT authentication guard.
   * @param configService - The configuration service for accessing app config.
   */
  constructor(
    private readonly reflector: Reflector,
    @Inject(JwtAuthGuard) private readonly jwtAuthGuard: JwtAuthGuard,
    private readonly configService: ConfigService<Config>,
  ) {
    const slackConfig = this.configService.get('slack');
    const slackToken = slackConfig?.token || '';
    this.slackTokenBuffer = Buffer.from(slackToken, 'utf8');
  }

  /**
   * Determines if the request can proceed by checking for a valid Slack token or falling back to JWT authentication.
   * @param context - The execution context of the request.
   * @returns True if access is granted, otherwise false.
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers['authorization'] || request.headers['Authorization'];
    const expectedHeader = Buffer.from(`Bearer ${this.slackTokenBuffer.toString('utf8')}`, 'utf8');
    if (authHeader) {
      const incomingHeader = Buffer.from(authHeader, 'utf8');
      if (
        incomingHeader.length === expectedHeader.length &&
        crypto.timingSafeEqual(incomingHeader, expectedHeader)
      ) {
        request.user = { slack: true };
        return true;
      }
    }
    // Fallback to JWT auth
    return this.jwtAuthGuard.canActivate(context) as Promise<boolean>;
  }
}
