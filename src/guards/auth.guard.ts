import { Injectable, ExecutionContext, UnauthorizedException, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';

import { FirebaseService } from '../auth/firebase.service';
import { ErrorLoggerService } from '../common/services/error-logger.service';
import { parseUserAgent } from '../common/utils/user-agent.utils';
import { Config } from '../config/interfaces/config.interface';

import type { JwtDto } from '../auth/jwt.dto';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly PURCHASLY_PROVIDER = 'purchasely';

  constructor(
    private readonly logger = new Logger(JwtAuthGuard.name),
    private readonly firebase: FirebaseService,
    private readonly configService: ConfigService<Config>,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const fcSecHeader = request.headers['x-fc-sec'] || request.headers['x_fc_sec'];
    const body = request.body;
    const isProduction = this.configService.get('environment') === 'production';
    const authHeader = request.headers.authorization;

    const isPurchasely = body?.provider === this.PURCHASLY_PROVIDER;
    const userAgent = request.headers['user-agent'];
    const parsedUserAgent = parseUserAgent(userAgent);
    const isVegeta = parsedUserAgent.family === 'Vegeta'; // Use parsed user agent instead of direct string comparison
    const isHealthCheck = request.path === '/health';

    // Log the full request details for debugging
    this.logger.debug(
      `Authentication attempt - Path: ${request.method} ${request.path}, Auth: ${
        authHeader ? 'Present' : 'Missing'
      }, UserAgent: ${parsedUserAgent.family}${
        parsedUserAgent.appVersion ? `/${parsedUserAgent.appVersion}` : ''
      }, Body: ${JSON.stringify(body)}`,
    );

    // Validate Authorization header format if present
    if (authHeader && !authHeader.startsWith('Bearer ')) {
      this.logger.debug(`Invalid Authorization header format: ${authHeader}`);
      throw new UnauthorizedException('Invalid Authorization header format');
    }

    // Only use X-FC-SEC validation for Purchasely webhooks
    if ((body && isPurchasely) || isVegeta || isHealthCheck) {
      const fcSecSecret = this.configService.get('webhooks').purchasely.fcSecSecret;
      const origin = isPurchasely ? 'Purchasely' : isVegeta ? 'Vegeta' : 'unknown';
      if (fcSecHeader && fcSecHeader === fcSecSecret) {
        this.logger.debug(`Authenticated ${origin} webhook via FC-Sec header`);
        return true;
      }
      this.logger.debug(`Invalid or missing FC-Sec header for ${origin} webhook`);
      throw new UnauthorizedException('Invalid or missing FC-Sec header');
    }

    if (body && body.fcaid && isProduction) {
      const fbHeader = request.headers['fb-auth'];
      if (fbHeader) {
        const token = fbHeader.split(' ')[1];
        try {
          await this.firebase.verifyIdToken(token);

          // Extract identifier for controller compatibility
          const identifiers = body.identifiers || {};
          const identifierKeys = ['adid', 'idfv', 'idfa', 'gaid'];
          const userIdentifier: { [key: string]: string | undefined } = {};

          for (const key of identifierKeys) {
            if (Array.isArray(identifiers[key]) && identifiers[key].length > 0) {
              userIdentifier[key] = identifiers[key][0];
            }
          }
          if (body.fcid) userIdentifier.fcid = body.fcid;
          if (body.fcaid) userIdentifier.fcaid = body.fcaid;

          request.user = userIdentifier;
          return true;
        } catch (error) {
          // this.errorLogger.logError(error, request, {
          //   errorName: 'Firebase error',
          //   context: 'JwtAuthGuard',
          //   includeStack: false,
          //   includeRequest: true,
          //   deviceId,
          //   fcid: body.fcid,
          // });
          throw new UnauthorizedException(`Firebase error: ${error.message} `);
        }
      } else {
        // Use parsed user agent information for better error messages
        const userAgentInfo = `${parsedUserAgent.family}${
          parsedUserAgent.appVersion ? `/${parsedUserAgent.appVersion}` : ''
        }`;
        throw new UnauthorizedException(
          `Invalid or missing Firebase Auth header with ${userAgentInfo}`,
        );
      }
    }

    // For all other providers, use JWT authentication
    return super.canActivate(context) as Promise<boolean>;
  }

  handleRequest<TUser = JwtDto>(
    err: any,
    user: TUser,
    info: any,
    context: ExecutionContext,
  ): TUser {
    const request = context.switchToHttp().getRequest();
    const body = request.body;

    // Extract deviceId once for use throughout the method
    const deviceId =
      body?.identifiers?.idfa?.[0] ||
      body?.identifiers?.idfv?.[0] ||
      body?.identifiers?.gaid?.[0] ||
      body?.identifiers?.adid?.[0];

    // Skip JWT validation for Purchasely webhooks
    if (body && body.provider === this.PURCHASLY_PROVIDER) {
      return user;
    }

    // Normal JWT validation for other providers
    if (err || !user) {
      const errorMessage = err?.message || info?.message || 'No user found';
      const requestPath = request.path;
      const requestMethod = request.method;
      const authHeader = request.headers.authorization;

      // Extract and decode JWT token for debugging
      let tokenDetails = '';
      if (authHeader) {
        try {
          const token = authHeader.split(' ')[1];
          const decodedToken = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
          const serverTime = new Date().toISOString();
          tokenDetails = `
          > Token Details:
            - Issued At (iat): ${new Date(decodedToken.iat * 1000).toISOString()}
            - Expires At (exp): ${new Date(decodedToken.exp * 1000).toISOString()}
            - Server Time: ${serverTime}
            - Time Until Expiry: ${Math.floor(
              (decodedToken.exp * 1000 - Date.now()) / 1000,
            )} seconds`;
        } catch (e) {
          tokenDetails = '> Failed to decode token for debugging';
        }
      }

      const errorType = err?.name || 'UnknownError';
      const logPayload = {
        path: `${requestMethod} ${requestPath}`,
        message: errorMessage,
        token: authHeader ? authHeader.split(' ')[1] : 'Authorization header missing',
      };

      // Do NOT log expired token errors
      if (
        !(errorType === 'TokenExpiredError' || errorMessage.toLowerCase().includes('jwt expired'))
      ) {
        this.errorLogger.logError(err, request, {
          errorName: errorMessage,
          context: 'JwtAuthGuard',
          includeStack: false,
          includeRequest: true,
          deviceId,
          metadata: { request },
        });
      }

      if (!authHeader) {
        this.errorLogger.logError(new Error('Missing Authorization header'), request, {
          errorName: 'JWT validation failed',
          context: 'JwtAuthGuard',
          includeStack: true,
          includeRequest: true,
          deviceId,
          metadata: { ...logPayload, token: 'Authorization header missing' },
        });
        throw new UnauthorizedException(
          'Missing Authorization header. Please include a valid Bearer token.',
        );
      } else if (errorType === 'TokenExpiredError' || errorMessage.includes('jwt expired')) {
        // Do NOT log expired token errors
        throw new UnauthorizedException('Token has expired. Please re create the token.');
      } else if (errorType === 'JsonWebTokenError') {
        this.errorLogger.logError(new Error('Invalid token signature'), request, {
          errorName: 'JWT validation failed',
          context: 'JwtAuthGuard',
          includeStack: true,
          includeRequest: true,
          deviceId,
          metadata: { ...logPayload, token: 'Invalid token signature' },
        });
        if (errorMessage.includes('invalid signature')) {
          throw new UnauthorizedException(
            'Invalid token signature. The token may have been tampered with.',
          );
        } else if (errorMessage.includes('jwt malformed')) {
          throw new UnauthorizedException(
            'Malformed token. Please ensure the token is properly formatted.',
          );
        } else if (errorMessage.includes('invalid token')) {
          throw new UnauthorizedException(
            'Invalid token structure. Please ensure the token is valid.',
          );
        }
      } else if (errorType === 'NotBeforeError') {
        this.errorLogger.logError(new Error('Token is not yet valid'), request, {
          errorName: 'JWT validation failed',
          context: 'JwtAuthGuard',
          includeStack: true,
          includeRequest: true,
          deviceId,
          metadata: { ...logPayload, token: 'Token is not yet valid' },
        });
        throw new UnauthorizedException('Token is not yet valid. Please check your system time.');
      } else {
        this.errorLogger.logError(new Error('Token validation failed'), request, {
          errorName: 'JWT validation failed',
          context: 'JwtAuthGuard',
          includeStack: true,
          includeRequest: true,
          deviceId,
          metadata: { ...logPayload, token: 'Token validation failed' },
        });
        throw new UnauthorizedException(`Token validation failed: ${errorMessage}`);
      }
    }

    const claims = user as unknown as JwtDto;
    this.logger.debug(
      `Successfully authenticated user: ${claims.adid || claims.idfv} for ${request.method} ${
        request.path
      }`,
    );

    return user;
  }
}
