import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { FirebaseService } from './firebase.service';
import { JwtStrategy } from './jwt.strategy';
import { RedisModule } from '../common/redis.module';
import { UsersModule } from '../users/users.module';
import { EventsService } from '../webhooks/events.service';
import { Webhook } from '../webhooks/entities/webhook.entity';
import { PendingUserModule } from '../pending-user';

@Module({
  imports: [
    UsersModule,
    PendingUserModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.register({}),
    TypeOrmModule.forFeature([Webhook]),
    RedisModule,
  ],
  providers: [AuthService, JwtStrategy, FirebaseService, EventsService],
  controllers: [AuthController],
  exports: [AuthService, FirebaseService],
})
export class AuthModule {}
