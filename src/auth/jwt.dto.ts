import { IsOptional, IsString, Matches } from 'class-validator';

import { AdidRegex, UUIDRegex } from '../users/user.dto';

// Both payload for JWT endpoints and token claims
export class JwtDto {
  @IsString()
  @IsOptional()
  fcid?: string;

  @IsString()
  @IsOptional()
  @Matches(UUIDRegex, { message: 'Invalid idfv format.' })
  idfv?: string; // iOS

  @IsString()
  @IsOptional()
  @Matches(AdidRegex, { message: 'Invalid adid format.' })
  adid?: string; // Android
}

export class DeviceIdDto extends JwtDto {
  @IsString()
  deviceId: string; // This is the device ID, either adid or idfv
}
