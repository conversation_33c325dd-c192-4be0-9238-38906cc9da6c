import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { initializeApp, type App, getApps } from 'firebase-admin/app';
import { getAuth, type Auth } from 'firebase-admin/auth';
import { getRemoteConfig, type RemoteConfig } from 'firebase-admin/remote-config';
import { Config } from '../config/interfaces/config.interface';
import { RedisService } from '../common/services/redis.service';
import * as admin from 'firebase-admin';
import { ErrorLoggerService } from '../common/services/error-logger.service';

let app: App | undefined = undefined;

@Injectable()
export class FirebaseService {
  private readonly logger = new Logger(FirebaseService.name);
  private auth: Auth | null = null;
  private remoteConfig: RemoteConfig | null = null;
  private readonly CACHE_PREFIX = 'remote_config:';
  private readonly CACHE_TTL = 3600; // 1 hour in seconds

  constructor(
    private readonly configService: ConfigService<Config>,
    private readonly redisService: RedisService,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    if (!getApps().length) {
      try {
        const firebaseConfig = this.configService.get('firebase');
        if (!firebaseConfig?.privateKey || !firebaseConfig?.clientEmail) {
          throw new Error('Firebase private key and client email are required');
        }

        app = initializeApp({
          credential: admin.credential.cert({
            projectId: firebaseConfig.projectId,
            privateKey: firebaseConfig.privateKey.replace(/\\n/g, '\n'),
            clientEmail: firebaseConfig.clientEmail,
          }),
        });
        this.logger.log('Firebase Admin initialized successfully');
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Failed to initialize Firebase Admin',
          context: 'FirebaseService',
          includeStack: true,
          includeRequest: false,
          metadata: { error },
        });
        throw error;
      }
    }
    this.auth = getAuth(app);
    this.remoteConfig = getRemoteConfig(app);
  }

  public verifyIdToken(token: string): Promise<any> {
    if (!this.auth) {
      throw new Error('Firebase Auth not initialized');
    }
    return this.auth.verifyIdToken(token);
  }

  private getCacheKey(key: string): string {
    return `${this.CACHE_PREFIX}${key}`;
  }

  /**
   * Get a remote config parameter value
   * @param key The parameter key to fetch
   * @param defaultValue The default value if the parameter doesn't exist
   */
  async getRemoteConfigValue<T>(key: string, defaultValue: T): Promise<T> {
    if (!this.remoteConfig) {
      throw new Error('Firebase Remote Config not initialized');
    }

    try {
      // Try to get from cache first
      const cacheKey = this.getCacheKey(key);
      const cachedValue = await this.redisService.get(cacheKey);

      if (cachedValue) {
        try {
          return JSON.parse(cachedValue) as T;
        } catch (error) {
          this.logger.debug(`Failed to parse cached value for key ${key}, fetching from Firebase`);
        }
      }

      // If not in cache or parsing failed, get from Firebase
      const template = await this.remoteConfig.getTemplate();
      const parameter = template.parameters[key];
      if (!parameter) {
        return defaultValue;
      }

      const defaultValueObj = parameter.defaultValue as { value: string };
      if (!defaultValueObj?.value) {
        return defaultValue;
      }

      let value: T;
      try {
        value = JSON.parse(defaultValueObj.value) as T;
      } catch {
        value = defaultValueObj.value as unknown as T;
      }

      // Cache the value
      try {
        await this.redisService.set(cacheKey, JSON.stringify(value), this.CACHE_TTL);
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Failed to cache remote config value',
          context: 'FirebaseService',
          includeStack: true,
          includeRequest: false,
          metadata: { key, error },
        });
      }

      return value;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error fetching remote config value',
        context: 'FirebaseService',
        includeStack: true,
        includeRequest: false,
        metadata: { key, error },
      });
      return defaultValue;
    }
  }

  /**
   * Update a remote config parameter
   * @param key The parameter key to update
   * @param value The new value to set
   * @param description Optional description for the parameter
   */
  async updateRemoteConfigValue(key: string, value: any, description?: string): Promise<void> {
    if (!this.remoteConfig) {
      throw new Error('Firebase Remote Config not initialized');
    }

    try {
      const template = await this.remoteConfig.getTemplate();
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);

      // Create or update the parameter
      template.parameters[key] = {
        defaultValue: {
          value: stringValue,
        },
        description: description || `Updated via API at ${new Date().toISOString()}`,
        valueType: 'STRING',
      };

      // Publish the template
      await this.remoteConfig.publishTemplate(template);

      // Update cache
      const cacheKey = this.getCacheKey(key);
      try {
        await this.redisService.set(cacheKey, stringValue, this.CACHE_TTL);
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Failed to update cache',
          context: 'FirebaseService',
          includeStack: true,
          includeRequest: false,
          metadata: { key, error },
        });
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error updating remote config value',
        context: 'FirebaseService',
        includeStack: true,
        includeRequest: false,
        metadata: { key, error },
      });
      throw error;
    }
  }

  /**
   * Get all remote config parameters
   */
  async getAllRemoteConfigValues(): Promise<Record<string, any>> {
    if (!this.remoteConfig) {
      throw new Error('Firebase Remote Config not initialized');
    }

    try {
      const template = await this.remoteConfig.getTemplate();
      const parameters: Record<string, any> = {};

      for (const [key, parameter] of Object.entries(template.parameters)) {
        const defaultValueObj = parameter.defaultValue as { value: string };
        if (defaultValueObj?.value) {
          try {
            parameters[key] = JSON.parse(defaultValueObj.value);
          } catch {
            parameters[key] = defaultValueObj.value;
          }

          // Cache each parameter
          const cacheKey = this.getCacheKey(key);
          try {
            await this.redisService.set(cacheKey, defaultValueObj.value, this.CACHE_TTL);
          } catch (error) {
            this.errorLogger.logError(error, undefined, {
              errorName: 'Failed to cache remote config value',
              context: 'FirebaseService',
              includeStack: true,
              includeRequest: false,
              metadata: { key, error },
            });
          }
        }
      }

      return parameters;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error fetching all remote config values',
        context: 'FirebaseService',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
      throw error;
    }
  }

  /**
   * Invalidate cache for a specific key or all keys
   * @param key Optional key to invalidate. If not provided, invalidates all remote config cache
   */
  async invalidateCache(key?: string): Promise<void> {
    try {
      if (key) {
        const cacheKey = this.getCacheKey(key);
        await this.redisService.del(cacheKey);
      } else {
        // Get all keys with the remote config prefix
        const client = this.redisService.getClient();
        const keys = await client.keys(`${this.CACHE_PREFIX}*`);
        if (keys.length > 0) {
          await client.del(...keys);
        }
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error invalidating cache',
        context: 'FirebaseService',
        includeStack: true,
        includeRequest: false,
        metadata: { key, error },
      });
      throw error;
    }
  }
}
