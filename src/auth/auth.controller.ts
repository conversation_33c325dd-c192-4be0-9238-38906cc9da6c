import {
  BadRequestException,
  Body,
  Controller,
  Headers,
  HttpException,
  Logger,
  Post,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request } from 'express';
import { Throttle } from '@nestjs/throttler';
import { ConfigService } from '@nestjs/config';

import { AuthService } from './auth.service';
import { throttleConfig } from '../config/throttle.config';
import { JwtDto } from './jwt.dto';
import { UsersService } from '../users/users.service';
import { EventsService } from '../webhooks/events.service';
import { Config } from '../config/interfaces/config.interface';
import { ErrorLoggerService } from '../common/services/error-logger.service';
import { PendingUserService } from '../pending-user';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly eventsService: EventsService,
    private readonly configService: ConfigService<Config>,
    private readonly errorLogger: ErrorLoggerService,
    private readonly pendingUserService: PendingUserService,
  ) {}

  @Post('createJwt')
  @Throttle(throttleConfig.auth)
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async createJwt(
    @Body() payload: JwtDto,
    @Headers('x-api-key') apiKey: string,
    @Headers('User-Agent') userAgent: string,
    @Req() request: Request,
  ) {
    const validApiKeys = this.configService.get('api').authTokens;

    this.logger.debug('Received create token request');

    if (!apiKey || !validApiKeys.includes(apiKey)) {
      this.logger.debug('Invalid API Key attempt', 'AuthController');
      throw new BadRequestException('Invalid API Key');
    }

    try {
      const deviceId = payload.idfv || payload.adid || '';
      if (!deviceId) throw new BadRequestException('Device ID (adid or idfv) is required');

      // Use the new pending user service for user lookup and creation
      const input = {
        deviceId,
        fcid: payload.fcid,
        idfv: payload.idfv,
        adid: payload.adid,
      };

      const user = await this.pendingUserService.findOrCreateUserForJwt(input);
      const fcid = user.fcid;

      if (!fcid) throw new HttpException('Server error: FCID generation failed', 500);

      await this.eventsService.generateDebugEvent(
        'JWT Created',
        deviceId,
        fcid,
        undefined,
        undefined,
        payload.adid ? 'Android' : 'iOS',
      );

      const accessToken = await this.authService.generateAccessToken(payload);
      const accessTokenExpiry = this.authService.getAccessTokenExpiry();

      this.logger.debug(
        `JWT tokens generated successfully ${JSON.stringify({
          fcid,
          accessTokenExpiry: accessTokenExpiry.toISOString(),
          userAgent,
        })}`,
      );

      return {
        accessToken,
        accessTokenExpiry: accessTokenExpiry.toISOString(),
      };
    } catch (error) {
      this.errorLogger.logError(error, request, {
        errorName: 'Error creating JWT',
        context: 'AuthController.createJwt',
        fcid: payload.fcid,
        includeStack: false,
        includeRequest: true,
        deviceId: payload.idfv || payload.adid,
      });

      throw new HttpException('Failed to create JWT token', 500);
    }
  }
}
