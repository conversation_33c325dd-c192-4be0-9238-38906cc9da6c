import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { JwtDto } from './jwt.dto';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKeyProvider: async (_, rawJwtToken, done) => {
        try {
          const secret = process.env.JWT_SECRET;
          if (!secret) {
            throw new Error('JWT_SECRET is missing');
          }
          done(null, secret);
        } catch (error) {
          done(error);
        }
      },
    });
  }

  async validate(payload: JwtDto) {
    const deviceId = payload.idfv || payload.adid;
    if (!deviceId) {
      throw new Error('Invalid JWT claims: missing any deviceId.');
    }
    return payload;
  }
}
