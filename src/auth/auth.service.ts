import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { instanceToPlain } from 'class-transformer';

import { JwtDto } from './jwt.dto';
import { Config } from '../config/interfaces/config.interface';
import { ErrorLoggerService } from '../common/services/error-logger.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService<Config>,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  async generateAccessToken(dto: JwtDto): Promise<string> {
    const jwtConfig = this.configService.get('jwt', { infer: true });
    if (!jwtConfig?.secret) {
      throw new Error('JWT_SECRET is not configured');
    }

    const secret = jwtConfig.secret;
    const accessTokenExpiry = jwtConfig.accessTokenExpiry;

    try {
      if (!secret || typeof secret !== 'string' || secret.trim() === '') {
        const error = new Error('JWT_SECRET is invalid');
        this.errorLogger.logError(error, undefined, {
          errorName: 'JWT_SECRET is invalid',
          context: 'AuthService.generateAccessToken',
          fcid: dto.fcid,
          includeStack: true,
          includeRequest: false,
        });
        throw error;
      }

      const plainClaims = instanceToPlain(dto);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { exp, ...cleanClaims } = plainClaims;

      // Log token generation details
      const now = Math.floor(Date.now() / 1000);
      this.logger.debug(
        `Generating JWT token:
        - Current timestamp: ${new Date(now * 1000).toISOString()}
        - Expiry setting: ${accessTokenExpiry}
        - Claims: ${JSON.stringify(cleanClaims)}`,
      );

      const token = this.jwtService.sign(
        { ...cleanClaims },
        {
          secret,
          expiresIn: accessTokenExpiry,
          // Ensure iat is set to current UTC time
          noTimestamp: false,
        },
      );

      // Verify the token was generated correctly
      const decoded = this.jwtService.decode(token) as { iat: number; exp: number };
      this.logger.debug(
        `Generated JWT token:
        - Issued At: ${new Date(decoded.iat * 1000).toISOString()}
        - Expires At: ${new Date(decoded.exp * 1000).toISOString()}
        - Duration: ${decoded.exp - decoded.iat} seconds`,
      );

      return token;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error signing JWT',
        context: 'AuthService.generateAccessToken',
        fcid: dto.fcid,
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId: dto.idfv || dto.adid },
      });
      throw new Error('Failed to generate access token');
    }
  }

  getAccessTokenExpiry(): Date {
    const expiryDate = new Date();
    const accessTokenExpiry = this.configService.get('jwt', { infer: true })!.accessTokenExpiry;
    const [amount, unit] = accessTokenExpiry.match(/\d+|\D+/g) as [string, string];
    const amountInSeconds = unit === 'm' ? parseInt(amount, 10) * 60 : parseInt(amount, 10);
    expiryDate.setUTCHours(expiryDate.getUTCHours());
    expiryDate.setUTCMinutes(expiryDate.getUTCMinutes());
    expiryDate.setUTCSeconds(expiryDate.getUTCSeconds() + amountInSeconds);

    return expiryDate;
  }
}
