import { UnauthorizedException, BadRequestException } from '@nestjs/common';

export class TokenExpiredException extends UnauthorizedException {
  constructor() {
    super({
      message: 'Refresh token has expired. Please log in again.',
      errorCode: 'REFRESH_TOKEN_EXPIRED',
      statusCode: 401,
    });
  }
}

export class InvalidTokenException extends UnauthorizedException {
  constructor() {
    super({
      message: 'Invalid refresh token.',
      errorCode: 'INVALID_REFRESH_TOKEN',
      statusCode: 401,
    });
  }
}

export class MissingTokenException extends BadRequestException {
  constructor() {
    super('Authorization token is missing');
  }
}
