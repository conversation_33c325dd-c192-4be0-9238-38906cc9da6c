/**
 * Interface for DLQ job information
 */
export interface DlqJobInfo {
  id: string;
  timestamp: number;
  attemptsMade: number;
  failedReason: string;
  data: any;
  error?: {
    message: string;
    stack?: string;
    name?: string;
  };
}

/**
 * Interface for DLQ statistics
 */
export interface DlqStats {
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  waiting: number;
  total: number;
}

/**
 * Interface for DLQ retry result
 */
export interface DlqRetryResult {
  success: boolean;
  newJobId?: string;
}

/**
 * Interface for DLQ delete result
 */
export interface DlqDeleteResult {
  success: boolean;
}

/**
 * Interface for DLQ bulk delete result
 */
export interface DlqBulkDeleteResult {
  deleted: number;
  failed: number;
}
