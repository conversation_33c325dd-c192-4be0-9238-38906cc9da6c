import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../common/services/redis.service';
import { Job } from 'bull';
import axios from 'axios';
import { DlqJobInfo, DlqStats, DlqRetryResult, DlqDeleteResult } from '../interfaces/dlq.interface';
import { ErrorLoggerService } from '../../common/services/error-logger.service';

@Injectable()
export class DlqService {
  private readonly logger = new Logger(DlqService.name);
  private readonly slackWebhookUrl: string;
  private readonly dlqMaxAgeDays: number;
  private readonly dlqAlertThreshold: number;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    this.slackWebhookUrl = this.configService.get<string>('SLACK_WEBHOOK_URL') || '';
    this.dlqMaxAgeDays = this.configService.get<number>('DLQ_MAX_AGE_DAYS') || 7;
    this.dlqAlertThreshold = this.configService.get<number>('DLQ_ALERT_THRESHOLD') || 50;
  }

  /**
   * Get statistics about the DLQ
   */
  async getStats(): Promise<DlqStats> {
    try {
      const stats = await this.redisService.getDlqStats();
      return {
        ...stats,
        total: stats.active + stats.completed + stats.failed + stats.delayed + stats.waiting,
      };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to get DLQ stats',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
      throw error;
    }
  }

  /**
   * Get jobs from the DLQ with pagination
   */
  async getJobs(page = 0, limit = 20): Promise<DlqJobInfo[]> {
    try {
      const start = page * limit;
      const end = start + limit - 1;
      const jobs = await this.redisService.getDlqJobs(start, end);

      return jobs.map(job => this.formatDlqJob(job));
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to get DLQ jobs',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
      throw error;
    }
  }

  /**
   * Get a specific job from the DLQ by ID
   */
  async getJob(jobId: string): Promise<DlqJobInfo> {
    try {
      const job = await this.redisService.getDlqJob(jobId);
      if (!job) {
        throw new NotFoundException(`Job ${jobId} not found in DLQ`);
      }

      return this.formatDlqJob(job);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to get DLQ job',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId },
      });
      throw error;
    }
  }

  /**
   * Retry a job from the DLQ
   */
  async retryJob(jobId: string): Promise<DlqRetryResult> {
    try {
      const newJob = await this.redisService.retryDlqJob(jobId);
      return {
        success: true,
        newJobId: newJob.id.toString(),
      };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to retry DLQ job',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId },
      });
      throw error;
    }
  }

  /**
   * Delete a job from the DLQ
   */
  async deleteJob(jobId: string): Promise<DlqDeleteResult> {
    try {
      await this.redisService.removeDlqJob(jobId);
      return { success: true };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to delete DLQ job',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId },
      });
      throw error;
    }
  }

  /**
   * Clean up old jobs from the DLQ
   * Runs daily at midnight
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOldJobs(): Promise<void> {
    try {
      const removedCount = await this.redisService.cleanupOldDlqJobs(this.dlqMaxAgeDays);
      this.logger.log(
        `DLQ cleanup: removed ${removedCount} jobs older than ${this.dlqMaxAgeDays} days`,
      );
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to clean up old DLQ jobs',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
    }
  }

  /**
   * Monitor DLQ size and send alerts if threshold is exceeded
   * Runs every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async monitorDlqSize(): Promise<void> {
    try {
      const stats = await this.getStats();
      const totalJobs = stats.total;

      if (totalJobs > this.dlqAlertThreshold) {
        // Get 5 most recent jobs for the alert
        const recentJobs = await this.getJobs(0, 5);
        await this.sendSlackAlert(totalJobs, recentJobs);
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to monitor DLQ size',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
    }
  }

  /**
   * Format a job for API response
   */
  private formatDlqJob(job: Job): DlqJobInfo {
    return {
      id: job.id.toString(),
      timestamp: job.timestamp,
      attemptsMade: job.attemptsMade,
      failedReason: job.failedReason || 'Unknown',
      data: job.data.originalData || job.data,
      error: job.data.error || {
        message: job.failedReason || 'Unknown error',
      },
    };
  }

  /**
   * Send a Slack alert when DLQ threshold is exceeded
   */
  private async sendSlackAlert(totalJobs: number, recentJobs: DlqJobInfo[]): Promise<void> {
    if (!this.slackWebhookUrl) {
      this.logger.warn('Slack webhook URL not configured, skipping alert');
      return;
    }

    try {
      // Format recent jobs for the alert
      const jobDetails = recentJobs
        .map(job => {
          return `• ID: ${job.id}\n  Error: ${job.error?.message || 'Unknown'}\n  Time: ${new Date(
            job.timestamp,
          ).toISOString()}\n  Original Job: ${job.data.correlationId || 'unknown'}`;
        })
        .join('\n\n');

      const message = {
        text: `🚨 ALERT: DLQ has ${totalJobs} jobs, exceeding threshold of ${this.dlqAlertThreshold}`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `🚨 *ALERT: DLQ has ${totalJobs} jobs, exceeding threshold of ${this.dlqAlertThreshold}*`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Recent DLQ Jobs:*\n${jobDetails}`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `🕐 Alert triggered at ${new Date().toISOString()}`,
            },
          },
        ],
      };

      await axios.post(this.slackWebhookUrl, message);
      this.logger.log(`Sent Slack alert for DLQ size: ${totalJobs} jobs`);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to send Slack alert',
        context: 'DlqService',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
    }
  }
}
