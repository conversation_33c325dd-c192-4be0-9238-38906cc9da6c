import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { PostgresUserService } from '../../common/services/postgres-user.service';

import type { BaseEvent } from '../schemas/types';

@Injectable()
export class TimestampValidationService {
  private readonly logger = new Logger(TimestampValidationService.name);

  constructor(private readonly pgUserService: PostgresUserService) {}

  /**
   * Validates the incoming webhook event against associated user's last event timestamp
   * @param event - The raw webhook event to validate
   * @returns {Promise<void | { alreadyProcessed: boolean }>} Returns a special object if already processed
   * @throws BadRequestException if validation fails
   */
  async validateEventByTimestamp(event: BaseEvent): Promise<void | { alreadyProcessed: boolean }> {
    const { fcid, event_control } = event;
    const user = await this.pgUserService.findUniqueUser(fcid, event_control.device_id);
    if (!user) {
      throw new BadRequestException({
        message: 'Invalid webhook event: user not found.',
      });
    }
    const lastTimestamp = user.event_control[event_control.device_id];
    const incomingTimestamp = Number(event_control.event_timestamp ?? event_control.timestamp);
    if (
      typeof lastTimestamp === 'number' &&
      !isNaN(incomingTimestamp) &&
      lastTimestamp > incomingTimestamp
    ) {
      return { alreadyProcessed: true };
    }
    const updatedUser = {
      ...user,
      event_control: {
        ...user.event_control,
        [event_control.device_id]: incomingTimestamp,
      },
    };
    await this.pgUserService.updateUser(fcid, updatedUser);
  }
}
