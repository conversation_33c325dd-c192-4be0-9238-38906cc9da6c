import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

import { Webhook } from '../entities/webhook.entity';
import { WebhookHandler } from '../handlers/webhook.handler';
import { UsersService } from '../../users/users.service';
import { MetricsService } from './metrics.service';
import { ErrorLoggerService } from '../../common/services/error-logger.service';

interface WebhookJobData extends Webhook {
  jobId?: string;
  batchId?: string;
  correlationId?: string;
}

interface LogContext {
  correlationId: string;
  fcid: string;
  eventName: string;
  provider: string;
  batchId?: string;
}

@Injectable()
export class BatchWebhookService {
  private readonly logger = new Logger(BatchWebhookService.name);

  constructor(
    @InjectRepository(Webhook)
    private readonly webhookRepository: Repository<Webhook>,
    private readonly webhookHandler: WebhookHandler,
    private readonly usersService: UsersService,
    @InjectQueue('webhooks') private readonly webhookQueue: Queue,
    private readonly metricsService: MetricsService,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  private createLogContext(webhook: WebhookJobData): LogContext {
    return {
      correlationId: webhook.correlationId || 'unknown',
      fcid: webhook.fcid,
      eventName: webhook.eventName,
      provider: webhook.provider,
      batchId: webhook.batchId,
    };
  }

  private logInfo(message: string, context: LogContext, data?: any) {
    this.logger.log({
      message,
      ...context,
      ...(data && { data }),
    });
  }

  private logError(message: string, context: LogContext, error: Error) {
    this.errorLogger.logError(error, undefined, {
      errorName: message,
      context: 'BatchWebhookService',
      includeStack: true,
      includeRequest: false,
      metadata: { message, context, error },
    });
  }

  private logWarning(message: string, context: LogContext, data?: any) {
    this.logger.warn({
      message,
      ...context,
      ...(data && { data }),
    });
  }

  /**
   * Process a webhook synchronously - Only for FlipaClip events
   */
  async processWebhook(webhook: Webhook, ip: string): Promise<Partial<Webhook> | null> {
    const context = this.createLogContext(webhook);

    if (webhook.provider === 'purchasely') {
      this.logWarning(
        'Skipping Purchasely webhook - batch processing not supported for Purchasely',
        context,
      );
      return null;
    }

    try {
      const saveToDatabase = webhook.payload?.saveToDatabase ?? true;

      // Prepare event for handler
      const event = {
        event_name: webhook.eventName,
        event_control: {
          device_id: webhook.deviceId,
          timestamp: new Date(webhook.eventTimestamp).getTime(),
        },
        provider: webhook.provider,
        fcid: webhook.fcid,
        store: webhook.store || 'unknown',
        payload: webhook.payload,
        timestamp: webhook.eventTimestamp,
        session_id: webhook.sessionId,
      };

      const data = await this.webhookHandler.handle(event, ip);
      if (data) {
        if (saveToDatabase) {
          await this.webhookRepository.save({
            ...data,
            deviceId: webhook.deviceId,
            eventControlDeviceId: webhook.eventControlDeviceId,
            eventControlTimestamp: webhook.eventControlTimestamp,
          });
          this.logInfo('Saved webhook data to repository', context);
        }
        const user = await this.usersService.findUserByFcidCached(webhook.fcid);
        if (user) {
          const updatedProperties = {
            ...user.properties,
            last_amplitude_device_id: webhook.deviceId,
          };
          await this.usersService.updateUserProperties(webhook.fcid, updatedProperties);
          this.logInfo('Updated user properties', context, {
            deviceId: webhook.deviceId,
            updatedProperties,
          });
        } else {
          this.logWarning('User not found', context, { fcid: webhook.fcid });
        }
      }

      this.logInfo('Successfully processed webhook', context, { webhookId: webhook.id });
      return data;
    } catch (error) {
      this.logError('Failed to process webhook', context, error);
      throw error;
    }
  }

  /**
   * Process a webhook job from the queue - Only for FlipaClip events
   */
  async processWebhookJob(webhook: WebhookJobData): Promise<void> {
    const context = this.createLogContext(webhook);
    const endTimer = this.metricsService.startWebhookProcessing(
      webhook.provider,
      webhook.eventName,
    );

    // Extract event_control fields if present
    const eventControl = (webhook as any).event_control || {};
    if (eventControl.device_id) {
      webhook.eventControlDeviceId = eventControl.device_id;
    }
    if (eventControl.timestamp || eventControl.event_timestamp) {
      webhook.eventControlTimestamp = String(
        eventControl.event_timestamp ?? eventControl.timestamp,
      );
    }

    if (webhook.provider === 'purchasely') {
      this.logWarning(
        'Skipping Purchasely webhook job - batch processing not supported for Purchasely',
        context,
      );
      return;
    }

    try {
      const event = {
        event_name: webhook.eventName,
        event_control: {
          device_id: webhook.deviceId,
          timestamp: new Date(webhook.eventTimestamp).getTime(),
        },
        provider: webhook.provider,
        fcid: webhook.fcid,
        store: webhook.store || 'unknown',
        payload: webhook.payload,
        timestamp: webhook.eventTimestamp,
        session_id: webhook.sessionId,
      };

      let data: Partial<Webhook> | null = null;
      const name = webhook.eventName;
      const shouldProcess =
        name.startsWith('ad_') ||
        name.startsWith('subscription_offer_') ||
        name === 'project_backed_up';

      if (shouldProcess) {
        data = await this.webhookHandler.handle(event);
        if (name.startsWith('ad_') && event.payload?.revenue) {
          this.metricsService.recordRevenue(webhook.provider, 'ad', Number(event.payload.revenue));
        }
      }

      if (data) {
        await this.webhookRepository.save({
          ...data,
          deviceId: webhook.deviceId,
          eventControlDeviceId: webhook.eventControlDeviceId,
          eventControlTimestamp: webhook.eventControlTimestamp,
        });
        this.logInfo('Saved webhook data to repository', context);

        const user = await this.usersService.findUserByFcidCached(webhook.fcid);
        if (user) {
          const updatedProperties = {
            ...user.properties,
            last_amplitude_device_id: webhook.deviceId,
          };
          await this.usersService.updateUserProperties(webhook.fcid, updatedProperties);
          this.logInfo('Updated user properties', context, {
            deviceId: webhook.deviceId,
            updatedProperties,
          });
        } else {
          this.logWarning('User not found', context, { fcid: webhook.fcid });
        }
      }

      this.logInfo('Successfully processed webhook', context, { webhookId: webhook.id });
      this.metricsService.endWebhookProcessing(
        endTimer,
        webhook.provider,
        webhook.eventName,
        'success',
      );
    } catch (error) {
      this.logError('Failed to process webhook job', context, error);
      this.metricsService.endWebhookProcessing(
        endTimer,
        webhook.provider,
        webhook.eventName,
        'error',
      );
      this.metricsService.recordError(webhook.provider, webhook.eventName, error.name || 'unknown');
      throw error;
    }
  }

  /**
   * Get priority for webhook processing - Only used for FlipaClip events in batch processing
   */
  private getWebhookPriority(webhook: Webhook): number {
    // Define priorities based on event names
    const eventPriorityMap: Record<string, number> = {
      // Subscription/paywall events - highest priority
      subscription_offer_shown: 1,
      subscription_offer_aborted: 1,
      // Ad events - lower priority
      ad_shown: 2,
    };

    return eventPriorityMap[webhook.eventName] || 3; // Default priority for unknown events
  }
}
