import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { QueryFailedError, Repository } from 'typeorm';

import { Webhook } from '../entities/webhook.entity';
import { WebhookHandler } from '../handlers/webhook.handler';
import { UsersService } from '../../users/users.service';

import type { MoEngageEvent } from '../schemas/moengage.schema';
import type { BaseEvent } from '../schemas/types';
import { ErrorLoggerService } from '../../common/services/error-logger.service';

type EnrichedEvent = BaseEvent & {
  timestamp: string;
  store: string;
};

@Injectable()
export class SingleWebhookService {
  private readonly logger = new Logger(SingleWebhookService.name);

  constructor(
    @InjectRepository(Webhook)
    private readonly webhookRepository: Repository<Webhook>,
    private readonly webhookHandler: WebhookHandler,
    private readonly usersService: UsersService,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  /**
   * Process a single event directly - Handles both FlipaClip and Purchasely events
   */
  public async processEvent(
    enrichedEvent: EnrichedEvent,
    ip: string,
  ): Promise<Partial<Webhook> | null> {
    let data: Partial<Webhook> | null = null;

    this.logger.debug(
      `Processing event ${enrichedEvent.event_name} from ${enrichedEvent.provider}`,
    );

    try {
      // Validate revenue values before processing
      if (enrichedEvent.payload) {
        const payload = enrichedEvent.payload as any;
        if (payload.revenue) {
          payload.revenue = Number(payload.revenue);
        }
        if (payload.totalRevenue) {
          payload.totalRevenue = Number(payload.totalRevenue);
        }
        if (payload.totalSubscriptionRevenue) {
          payload.totalSubscriptionRevenue = Number(payload.totalSubscriptionRevenue);
        }
      }
      data = await this.webhookHandler.handle(enrichedEvent, ip);
      // Extract event_control fields if present
      const eventControl = (enrichedEvent as any).event_control || {};
      const saveToDatabase = data && 'saveToDatabase' in data ? (data as any).saveToDatabase : true;
      if (data && saveToDatabase) {
        const deviceId = eventControl.device_id || enrichedEvent.event_control?.device_id;
        await this.webhookRepository.save({
          ...data,
          deviceId,
          eventControlDeviceId: eventControl.device_id,
          eventControlTimestamp: eventControl.event_timestamp ?? eventControl.timestamp,
        });

        // Update only the device ID in user properties
        const user = await this.usersService.findUserByFcidCached(enrichedEvent.fcid);
        if (user) {
          const updatedProperties = {
            ...user.properties,
            last_amplitude_device_id: deviceId,
          };
          await this.usersService.updateUserProperties(enrichedEvent.fcid, updatedProperties);
        }
      }
      return data;
    } catch (error) {
      if (error instanceof QueryFailedError) {
        const badForeignKey =
          error.message ===
          'insert or update on table "webhooks" violates foreign key constraint "FK_webhooks_users_fcid"';
        if (badForeignKey) {
          throw new BadRequestException(
            `Insert event error: invalid fcid, provider: ${enrichedEvent.provider}.`,
          );
        }
      }

      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to process event',
        context: 'SingleWebhookService',
        includeStack: true,
        includeRequest: false,
        metadata: { event: enrichedEvent, error },
      });
      throw error;
    }
  }

  async processMoengageEvents(
    events: MoEngageEvent[],
    timestamp: string,
  ): Promise<Partial<Webhook>[]> {
    const results: Partial<Webhook>[] = [];
    for (const event of events) {
      if (!event.uid) {
        this.errorLogger.logError(
          new Error('FCID (uid) is missing from the MoEngage event data'),
          undefined,
          {
            errorName: 'FCID (uid) is missing from the MoEngage event data',
            context: 'SingleWebhookService',
            includeStack: true,
            includeRequest: false,
            metadata: { event },
          },
        );
        continue;
      }
      const user = await this.usersService.findUserByFcidCached(event.uid);
      if (!user) {
        this.logger.warn(`User with fcid ${event.uid} not found.`);
        continue;
      }
      // TODO: Add user-based segmentationLeven and trackingLevel filtering logic here
      const webhookData: Partial<Webhook> = {
        eventTimestamp: timestamp,
        fcid: event.uid,
        eventName: event.event_name,
        provider: 'MOENGAGE',
        payload: event,
        store: undefined,
        modified_properties: {},
        deviceId: event.device_attributes?.moengage_device_id,
        sessionId: undefined,
        eventControlDeviceId: event.device_attributes?.moengage_device_id,
        eventControlTimestamp: String(event.event_time),
      };
      results.push(webhookData);
    }
    this.logger.log(`✅ Processed ${results.length} MoEngage events`);
    return results;
  }
}
