import { Injectable } from '@nestjs/common';
import { Counter, Histogram, Registry, Summary } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly registry: Registry;
  private readonly webhookProcessingDuration: Histogram;
  private readonly webhookProcessed: Counter;
  private readonly webhookErrors: Counter;
  private readonly webhookRetries: Counter;
  private readonly revenueTracked: Counter;
  private readonly webhookProcessingSummary: Summary;

  constructor() {
    this.registry = new Registry();

    // Histogram for webhook processing duration with more intuitive buckets
    this.webhookProcessingDuration = new Histogram({
      name: 'webhook_processing_duration_seconds',
      help: 'Duration of webhook processing in seconds',
      labelNames: ['provider', 'event_type', 'status'],
      buckets: [0.1, 0.25, 0.5, 1, 2.5], // More granular buckets under 3 seconds
      registers: [this.registry],
    });

    // Summary for quick stats on processing duration
    this.webhookProcessingSummary = new Summary({
      name: 'webhook_processing_summary_seconds',
      help: 'Summary of webhook processing durations',
      labelNames: ['provider', 'event_type'],
      percentiles: [0.5, 0.9, 0.95, 0.99], // Adds p50, p90, p95, p99 metrics
      registers: [this.registry],
    });

    // Counter for processed webhooks with more descriptive name
    this.webhookProcessed = new Counter({
      name: 'webhook_events_total',
      help: 'Total number of webhook events by status',
      labelNames: ['provider', 'event_type', 'status'],
      registers: [this.registry],
    });

    // Counter for webhook errors with specific error types
    this.webhookErrors = new Counter({
      name: 'webhook_errors_total',
      help: 'Total number of webhook errors by type',
      labelNames: ['provider', 'event_type', 'error_type'],
      registers: [this.registry],
    });

    // Counter for webhook retries with attempt number
    this.webhookRetries = new Counter({
      name: 'webhook_retry_attempts_total',
      help: 'Total number of webhook retry attempts',
      labelNames: ['provider', 'event_type', 'attempt'],
      registers: [this.registry],
    });

    // Counter for revenue with more precise naming
    this.revenueTracked = new Counter({
      name: 'revenue_usd_total',
      help: 'Total revenue in USD by type',
      labelNames: ['provider', 'revenue_type'],
      registers: [this.registry],
    });
  }

  // Start timing webhook processing
  startWebhookProcessing(provider: string, eventType: string): () => number {
    return this.webhookProcessingDuration.startTimer({
      provider,
      event_type: eventType,
    });
  }

  // End timing and record webhook processing
  endWebhookProcessing(
    endTimer: () => number,
    provider: string,
    eventType: string,
    status: 'success' | 'error',
  ): void {
    const duration = endTimer();
    this.webhookProcessingDuration.labels(provider, eventType, status).observe(duration);
    this.webhookProcessingSummary.labels(provider, eventType).observe(duration);
    this.webhookProcessed.labels(provider, eventType, status).inc();
  }

  // Record webhook error
  recordError(provider: string, eventType: string, errorType: string): void {
    this.webhookErrors.labels(provider, eventType, errorType).inc();
  }

  // Record webhook retry with attempt number
  recordRetry(provider: string, eventType: string, attempt: number): void {
    this.webhookRetries.labels(provider, eventType, attempt.toString()).inc();
  }

  // Record revenue with proper decimal handling
  recordRevenue(provider: string, revenueType: 'ad' | 'subscription', amount: number): void {
    // Round to 4 decimal places to avoid floating point issues
    const roundedAmount = Math.round(amount * 10000) / 10000;
    this.revenueTracked.labels(provider, revenueType).inc(roundedAmount);
  }

  // Record webhook processed
  recordWebhookProcessed(provider: string, eventType: string, status: string): void {
    this.webhookProcessed.labels(provider, eventType, status).inc();
  }

  // Get metrics
  async getMetrics(): Promise<string> {
    return await this.registry.metrics();
  }
}
