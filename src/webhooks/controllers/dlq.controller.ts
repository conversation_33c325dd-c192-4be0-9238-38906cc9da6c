import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  UseGuards,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../guards/auth.guard';
import { DlqService } from '../services/dlq.service';
import {
  DlqJobInfo,
  DlqStats,
  DlqRetryResult,
  DlqDeleteResult,
  DlqBulkDeleteResult,
} from '../interfaces/dlq.interface';

@Controller('webhooks/dlq')
@UseGuards(JwtAuthGuard)
export class DlqController {
  constructor(private readonly dlqService: DlqService) {}

  /**
   * Get statistics about the DLQ
   */
  @Get('stats')
  async getStats(): Promise<{ status: string; data: DlqStats }> {
    try {
      const stats = await this.dlqService.getStats();
      return {
        status: 'success',
        data: stats,
      };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to get DLQ stats: ${error.message}`);
    }
  }

  /**
   * Get jobs from the DLQ with pagination
   */
  @Get()
  async getJobs(
    @Query('page') page = '0',
    @Query('limit') limit = '20',
  ): Promise<{
    status: string;
    message?: string;
    data?: DlqJobInfo[];
    pagination?: {
      page: number;
      limit: number;
      count: number;
    };
  }> {
    try {
      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);

      if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 0 || limitNum < 1 || limitNum > 100) {
        return {
          status: 'error',
          message:
            'Invalid pagination parameters. Page must be >= 0 and limit must be between 1 and 100.',
        };
      }

      const jobs = await this.dlqService.getJobs(pageNum, limitNum);
      return {
        status: 'success',
        data: jobs,
        pagination: {
          page: pageNum,
          limit: limitNum,
          count: jobs.length,
        },
      };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to get DLQ jobs: ${error.message}`);
    }
  }

  /**
   * Get a specific job from the DLQ by ID
   */
  @Get(':id')
  async getJob(@Param('id') jobId: string): Promise<{ status: string; data: DlqJobInfo }> {
    try {
      const job = await this.dlqService.getJob(jobId);
      return {
        status: 'success',
        data: job,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get DLQ job ${jobId}: ${error.message}`);
    }
  }

  /**
   * Retry a job from the DLQ
   */
  @Post(':id/retry')
  async retryJob(@Param('id') jobId: string): Promise<{
    status: string;
    message: string;
    data: DlqRetryResult;
  }> {
    try {
      const result = await this.dlqService.retryJob(jobId);
      return {
        status: 'success',
        message: `Job ${jobId} retried successfully`,
        data: result,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to retry DLQ job ${jobId}: ${error.message}`);
    }
  }

  /**
   * Delete a job from the DLQ
   */
  @Delete(':id')
  async deleteJob(@Param('id') jobId: string): Promise<{
    status: string;
    message: string;
    data?: DlqDeleteResult;
  }> {
    try {
      const result = await this.dlqService.deleteJob(jobId);
      return {
        status: 'success',
        message: `Job ${jobId} deleted successfully`,
        data: result,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to delete DLQ job ${jobId}: ${error.message}`);
    }
  }

  /**
   * Delete all jobs from the DLQ (bulk delete)
   */
  @Delete()
  async deleteAllJobs(): Promise<{
    status: string;
    message: string;
    data: DlqBulkDeleteResult;
  }> {
    try {
      const stats = await this.dlqService.getStats();
      const jobs = await this.dlqService.getJobs(0, stats.total);

      let successCount = 0;
      let failureCount = 0;

      for (const job of jobs) {
        try {
          await this.dlqService.deleteJob(job.id);
          successCount++;
        } catch (error) {
          failureCount++;
        }
      }

      return {
        status: 'success',
        message: `Deleted ${successCount} jobs from DLQ`,
        data: {
          deleted: successCount,
          failed: failureCount,
        },
      };
    } catch (error) {
      throw new InternalServerErrorException(`Failed to delete all DLQ jobs: ${error.message}`);
    }
  }
}
