import { Controller, Post, Body, HttpCode, Get, Param, Query } from '@nestjs/common';
import { FirebaseService } from '../auth/firebase.service';

@Controller('webhooks/remote-config')
export class RemoteConfigController {
  constructor(private readonly firebaseService: FirebaseService) {}

  @Get(':key')
  @HttpCode(200)
  async getRemoteConfigValue(
    @Param('key') key: string,
    @Query('defaultValue') defaultValue?: string,
  ) {
    // Parse the default value if provided
    let parsedDefaultValue: any = defaultValue;
    if (defaultValue) {
      try {
        parsedDefaultValue = JSON.parse(defaultValue);
      } catch {
        // If parsing fails, use the string value as is
      }
    }

    const value = await this.firebaseService.getRemoteConfigValue(key, parsedDefaultValue);

    return {
      success: true,
      data: {
        key,
        value,
      },
    };
  }

  @Post('update')
  @HttpCode(200)
  async handleRemoteConfigUpdate(
    @Body() payload: { key: string; value: any; description?: string },
  ) {
    const { key, value, description } = payload;

    // Validate the payload
    if (!key || value === undefined) {
      throw new Error('Invalid payload: key and value are required');
    }

    // Update the remote config value
    await this.firebaseService.updateRemoteConfigValue(key, value, description);

    return {
      success: true,
      message: `Remote config parameter '${key}' updated successfully`,
    };
  }

  @Post('sync')
  @HttpCode(200)
  async syncRemoteConfig() {
    // Invalidate all cache before syncing
    await this.firebaseService.invalidateCache();

    // Get all values (this will also update the cache)
    const values = await this.firebaseService.getAllRemoteConfigValues();

    return {
      success: true,
      message: 'Remote config sync completed',
      data: values,
    };
  }

  @Post('invalidate-cache')
  @HttpCode(200)
  async invalidateCache(@Body() payload?: { key?: string }) {
    await this.firebaseService.invalidateCache(payload?.key);

    return {
      success: true,
      message: payload?.key
        ? `Cache invalidated for key '${payload.key}'`
        : 'All remote config cache invalidated',
    };
  }
}
