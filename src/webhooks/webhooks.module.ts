import { forward<PERSON><PERSON>, <PERSON>du<PERSON>, Logger } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { TerminusModule } from '@nestjs/terminus';

import { PostgresUserService } from '../common/services/postgres-user.service';
import { User } from '../users/entities/user.entity';
import { WebhookHandler } from './handlers/webhook.handler';
import { Webhook } from './entities/webhook.entity';
import { WebhooksController } from './webhooks.controller';
import { RemoteConfigController } from './remote-config.controller';
import { DlqController } from './controllers/dlq.controller';
import { SingleWebhookService } from './services/single-webhook.service';
import { BatchWebhookService } from './services/batch-webhook.service';
import { DlqService } from './services/dlq.service';
import { GeolocationModule } from '../geolocation/geolocation.module';
import { KwsModule } from '../kws/kws.module';
import { UsersModule } from '../users/users.module';
import { UsersService } from '../users/users.service';
import { EventsService } from './events.service';
import { RedisModule } from '../common/redis.module';
import { WebhookProcessor } from './processors/webhook.processor';
import { DlqProcessor } from './processors/dlq.processor';
import { redisConfig } from '../config/redis.config';
import { MetricsService } from './services/metrics.service';
import { TimestampValidationService } from './services/timestamp-validation.service';
import { AuthModule } from '../auth/auth.module';
import { LoggerModule } from '../common/logger.module';
import { PendingUserModule } from '../pending-user';

@Module({
  imports: [
    TypeOrmModule.forFeature([Webhook, User]),
    forwardRef(() => UsersModule),
    PendingUserModule,
    RedisModule,
    LoggerModule,
    TerminusModule,
    BullModule.registerQueue({
      name: 'webhooks',
      defaultJobOptions: {
        ...redisConfig.queue.defaultJobOptions,
        removeOnComplete: true,
        removeOnFail: false,
      },
    }),
    GeolocationModule,
    KwsModule,
    forwardRef(() => AuthModule),
  ],
  controllers: [WebhooksController, DlqController, RemoteConfigController],
  providers: [
    SingleWebhookService,
    BatchWebhookService,
    DlqService,
    PostgresUserService,
    WebhookHandler,
    UsersService,
    EventsService,
    WebhookProcessor,
    DlqProcessor,
    MetricsService,
    TimestampValidationService,
    Logger,
  ],
  exports: [
    SingleWebhookService,
    BatchWebhookService,
    DlqService,
    PostgresUserService,
    EventsService,
    MetricsService,
    TimestampValidationService,
  ],
})
export class WebhooksModule {}
