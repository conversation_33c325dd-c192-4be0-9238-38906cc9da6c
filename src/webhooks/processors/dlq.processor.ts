import { Process, Processor, OnQueueError } from '@nestjs/bull';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bull';
import { ErrorLoggerService } from '../../common/services/error-logger.service';

/**
 * DLQ Processor
 *
 * This processor handles jobs in the Dead Letter Queue (DLQ).
 * Unlike the main webhook processor, it doesn't attempt to process the jobs,
 * but simply logs them for monitoring and debugging purposes.
 */
@Injectable()
@Processor('webhooks-dlq')
export class DlqProcessor {
  private readonly logger = new Logger(DlqProcessor.name);

  constructor(private readonly errorLogger: ErrorLoggerService) {}

  /**
   * Process a job from the DLQ
   * This doesn't actually process the job, but logs it for monitoring
   */
  @Process('process-dlq')
  async processDlqJob(job: Job) {
    this.logger.debug(`Received job ${job.id} in DLQ`);

    try {
      // Extract error information
      const errorInfo = job.data.error || { message: 'Unknown error', stack: 'No stack trace' };

      // Log detailed information about the failed job
      this.logger.warn({
        message: `Job ${job.id} in DLQ: ${errorInfo.message}`,
        jobId: job.id,
        originalJobId: job.data.originalJobId,
        attemptsMade: job.data.attemptsMade,
        timestamp: new Date().toISOString(),
        error: errorInfo,
        originalData: job.data.originalData,
      });

      // We don't actually process the job, just acknowledge it
      return {
        processed: true,
        jobId: job.id,
      };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error handling DLQ job',
        context: 'DlqProcessor',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId: job.id },
      });
      throw error;
    }
  }

  @OnQueueError()
  onError(error: Error) {
    this.errorLogger.logError(error, undefined, {
      errorName: 'DLQ queue error',
      context: 'DlqProcessor',
      includeStack: true,
      includeRequest: false,
      metadata: { error },
    });
  }
}
