import { Process, Processor, OnQueueError, OnQueueFailed } from '@nestjs/bull';
import { Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bull';
import { Validator } from 'jsonschema';
import { BatchWebhookService } from '../services/batch-webhook.service';
import { Webhook } from '../entities/webhook.entity';
import { batchConfig } from '../../config/batch.config';
import { baseEventSchema } from '../schemas/base.schema';
import { RedisService } from '../../common/services/redis.service';
import { TimestampValidationService } from '../services/timestamp-validation.service';
import { ErrorLoggerService } from '../../common/services/error-logger.service';

interface ProcessingResult {
  success: boolean;
  jobId: number | string;
  error?: string;
  eventId?: string;
}

@Processor('webhooks')
export class WebhookProcessor {
  private readonly logger = new Logger(WebhookProcessor.name);
  private readonly validator: Validator;
  private isInitialized = false;

  constructor(
    private readonly batchWebhookService: BatchWebhookService,
    @Inject(forwardRef(() => RedisService))
    private readonly redisService: RedisService,
    private readonly timestampValidationService: TimestampValidationService,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    try {
      this.logger.log('Initializing WebhookProcessor...');
      this.isInitialized = true;
      this.validator = new Validator();
      this.logger.log('WebhookProcessor successfully initialized');
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to initialize WebhookProcessor',
        context: 'WebhookProcessor',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
      throw error;
    }
  }

  @Process({
    name: 'process-webhook',
    concurrency: 1, // Process one webhook at a time to maintain order
  })
  async processWebhook(job: Job<Webhook>): Promise<ProcessingResult> {
    if (!this.isInitialized) {
      const error = new Error('WebhookProcessor not properly initialized');
      this.errorLogger.logError(error, undefined, {
        errorName: 'WebhookProcessor not properly initialized',
        context: 'WebhookProcessor',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
      throw error;
    }

    try {
      this.logger.debug(
        `Starting to process webhook job ${job.id} - Data: ${JSON.stringify(job.data)}`,
      );

      // Validation Phase
      const validationError = await this.validateWebhook(job.data);
      if (validationError) {
        this.errorLogger.logError(new Error(validationError), undefined, {
          errorName: 'Validation failed for webhook',
          context: 'WebhookProcessor',
          includeStack: true,
          includeRequest: false,
          metadata: { jobId: job.id },
        });
        return {
          success: false,
          jobId: job.id,
          error: validationError,
        };
      }

      this.logger.debug(`Validation passed for webhook ${job.id}`);
      await job.progress(20);

      // Extract event_control fields if present
      const eventControl = (job.data as any).event_control || {};
      if (eventControl.device_id) {
        job.data.eventControlDeviceId = eventControl.device_id;
      }
      if (eventControl.timestamp || eventControl.event_timestamp) {
        job.data.eventControlTimestamp = String(
          eventControl.event_timestamp ?? eventControl.timestamp,
        );
      }

      // Sequential Processing Phase
      try {
        this.logger.debug(
          `Processing webhook job ${job.id} with batchWebhookService.processWebhookJob`,
        );
        await this.batchWebhookService.processWebhookJob(job.data);
        await job.progress(100);

        this.logger.debug(`Successfully processed webhook job ${job.id}`);
        return {
          success: true,
          jobId: job.id,
          eventId: job.data.id?.toString(),
        };
      } catch (error) {
        // Check if we should retry based on error type
        if (this.isRetryableError(error) && job.attemptsMade < batchConfig.retry.attempts) {
          this.logger.warn(
            `Retryable error processing webhook ${job.id} (attempt ${job.attemptsMade + 1}/${
              batchConfig.retry.attempts
            }): ${error.message}`,
          );
          throw error; // Let Bull handle the retry
        }

        // Non-retryable error or max attempts reached
        this.errorLogger.logError(error, undefined, {
          errorName: 'Failed to process webhook job',
          context: 'WebhookProcessor',
          includeStack: true,
          includeRequest: false,
          metadata: { jobId: job.id },
        });
        await job.moveToFailed(error, true);
        return {
          success: false,
          jobId: job.id,
          error: error.message,
          eventId: job.data.id?.toString(),
        };
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Unexpected error processing webhook job',
        context: 'WebhookProcessor',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId: job.id },
      });
      throw error;
    }
  }

  @OnQueueError()
  onError(error: Error) {
    this.errorLogger.logError(error, undefined, {
      errorName: 'Queue error',
      context: 'WebhookProcessor',
      includeStack: true,
      includeRequest: false,
      metadata: { error },
    });
  }

  @OnQueueFailed()
  async onFailed(job: Job, error: Error) {
    this.errorLogger.logError(error, undefined, {
      errorName: 'Job failed',
      context: 'WebhookProcessor',
      includeStack: true,
      includeRequest: false,
      metadata: { jobId: job.id, attemptsMade: job.attemptsMade, attempts: job.opts.attempts },
    });

    // Only move to DLQ if max attempts reached
    if (job.attemptsMade >= (job.opts.attempts || batchConfig.retry.attempts)) {
      try {
        // Prepare data for DLQ
        const dlqData = {
          originalData: job.data,
          originalJobId: job.id,
          attemptsMade: job.attemptsMade,
          error: {
            message: error.message,
            stack: error.stack,
            name: error.name,
          },
          timestamp: new Date().toISOString(),
        };

        // Add to DLQ
        await this.redisService.addToDlq(dlqData, {
          jobId: `dlq-${job.id}-${Date.now()}`,
        });

        this.logger.log(`Moved failed job ${job.id} to DLQ after ${job.attemptsMade} attempts`);
      } catch (dlqError) {
        this.errorLogger.logError(dlqError, undefined, {
          errorName: 'Failed to move job to DLQ',
          context: 'WebhookProcessor',
          includeStack: true,
          includeRequest: false,
          metadata: { jobId: job.id },
        });
      }
    }
  }

  private async validateWebhook(webhook: Webhook): Promise<string | null> {
    try {
      this.logger.debug(`Validating webhook: ${JSON.stringify(webhook)}`);

      // Convert Webhook entity to base event format
      const baseEvent = {
        event_name: webhook.eventName,
        event_control: {
          device_id: webhook.deviceId,
          timestamp:
            typeof webhook.eventTimestamp === 'number'
              ? webhook.eventTimestamp
              : Date.parse(webhook.eventTimestamp),
        },
        provider: webhook.provider,
        fcid: webhook.fcid,
        store: webhook.store,
        payload: webhook.payload,
      };

      // Validate against base event schema
      const result = this.validator.validate(baseEvent, baseEventSchema);

      if (!result.valid) {
        const errors = result.errors.map(error => `${error.property}: ${error.message}`).join(', ');
        return `Schema validation failed: ${errors}`;
      }

      // Validate provider is valid
      const validProviders = ['FlipaClip', 'purchasely', 'Purchasely', 'paywall', 'ad'];
      if (!validProviders.includes(webhook.provider)) {
        return `Invalid webhook provider: ${webhook.provider}`;
      }

      // Validate required fields based on provider
      if (webhook.provider === 'purchasely' && !webhook.fcid) {
        return 'FCID is required for Purchasely webhooks';
      }

      // Timestamp validation for non-purchasely events
      if (webhook.provider !== 'purchasely') {
        const timestampResult = await this.timestampValidationService.validateEventByTimestamp(
          baseEvent,
        );
        if (timestampResult && (timestampResult as any).alreadyProcessed) {
          return 'already_processed';
        }
      }

      this.logger.debug('Webhook validation successful');
      return null;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Validation error',
        context: 'WebhookProcessor',
        includeStack: true,
        includeRequest: false,
        metadata: { error },
      });
      return `Validation error: ${error.message}`;
    }
  }

  private isRetryableError(error: any): boolean {
    // Network-related errors that might be temporary
    const retryableErrors = [
      'ECONNRESET',
      'ETIMEDOUT',
      'ECONNREFUSED',
      'ENOTFOUND',
      'EAI_AGAIN',
      'ETIMEOUT',
      'ECONNABORTED',
      'ENETUNREACH',
      'EHOSTUNREACH',
    ];

    // Database-related errors that might be temporary
    const retryableDbErrors = ['deadlock', 'timeout', 'connection', 'transaction', 'lock'];

    return (
      retryableErrors.some(
        retryableError => error.message?.includes(retryableError) || error.code === retryableError,
      ) || retryableDbErrors.some(dbError => error.message?.toLowerCase().includes(dbError))
    );
  }
}
