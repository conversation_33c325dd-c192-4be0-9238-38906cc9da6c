import { Entity, Column, PrimaryGeneratedColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('webhooks')
export class Webhook {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'event_timestamp', type: 'timestamp' })
  @Index()
  eventTimestamp: string;

  @Column()
  @Index()
  fcid: string;

  @ManyToOne(() => User, user => user.webhooks, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'fcid', referencedColumnName: 'fcid' })
  user: User;

  @Column({ name: 'event_name' })
  @Index()
  eventName: string;

  @Column()
  provider: string;

  @Column({ nullable: true })
  store: string;

  @Column({ type: 'jsonb' })
  payload: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  modified_properties: Record<string, any>;

  @Column({ name: 'device_id', nullable: true })
  @Index()
  deviceId: string;

  @Column({ name: 'session_id', type: 'bigint', nullable: true })
  @Index()
  sessionId: number;

  @Column({ name: 'event_control_device_id', type: 'varchar', nullable: true })
  @Index()
  eventControlDeviceId?: string;

  @Column({ name: 'event_control_timestamp', type: 'varchar', nullable: true })
  @Index()
  eventControlTimestamp?: string;
}
