import { Logger } from '@nestjs/common';

import { calculateUserCurrentAge } from '../../common/utils/calculate-user-age';
import { toModifiedProperties } from '../../common/utils/modified-properties';
import { getUpdatedProperties } from './webhook.handler';

import type { User } from '../../users/entities/user.entity';
import type { Webhook } from '../entities/webhook.entity';
import type { BaseEvent } from '../schemas/types';
import type { ModifiedProperty, PropertyUpdate } from '../types';

const IDENTIFY_OPS: Record<string, 'add' | 'set' | 'setArray' | 'setOnce'> = {
  totalProjectsBackedUpCount: 'add',
  totalAppUseOfflineTime: 'add',
  totalAppUseTime: 'add',
  attPermissionStatus: 'set',
  pushNotificationStatus: 'set',
  notificationChannels: 'setArray',
  mdmUser: 'set',
  mdmInstitutionId: 'set',
  birthDate: 'setOnce',
  moengageMigrated: 'set',
};

function processIdentifyProperties(
  user: User,
  eventData: BaseEvent,
  hasFcaid: boolean,
  logger: Logger,
): Record<string, PropertyUpdate> {
  const updates: Record<string, PropertyUpdate> = {};
  for (const [key, value] of Object.entries(eventData.payload || {})) {
    const operation = IDENTIFY_OPS[key];
    if (!operation) continue; // Skip unknown properties

    const currentValue = user.properties[key];
    switch (operation) {
      case 'set':
        if (JSON.stringify(currentValue) !== JSON.stringify(value)) {
          updates[key] = { op: 'set', value };
        }
        break;
      case 'setOnce':
        if (key === 'birthDate' && !hasFcaid) {
          logger.warn(`Cannot set birthDate for user without FCAID: ${eventData.fcid}`);
          break;
        }
        if (currentValue === undefined || currentValue === null) {
          updates[key] = { op: 'set', value };
        } else {
          logger.debug(`Property ${key} already set, skipping setOnce operation`);
        }
        break;
      case 'setArray':
        if (Array.isArray(value) && JSON.stringify(currentValue) !== JSON.stringify(value)) {
          updates[key] = { op: 'set', value };
        }
        break;
      case 'add':
        if (typeof value === 'number') {
          updates[key] = { op: 'add', value };
        }
        break;
      default:
        logger.warn(`Unknown operation ${operation} for key ${key}`);
        break;
    }
  }
  return updates;
}

export function createIdentifyWebhookData(
  eventData: BaseEvent & { timestamp: string },
  modifiedProperties: ModifiedProperty[],
): Partial<Webhook> {
  const webhookData = {
    eventTimestamp: eventData.timestamp,
    fcid: eventData.fcid,
    eventName: eventData.event_name,
    provider: eventData.provider,
    store: eventData.store,
    // Only include modified properties in the payload
    payload: Object.fromEntries(
      modifiedProperties.map(prop => [prop.affected_property, prop.affected_value]),
    ),
    modified_properties: modifiedProperties,
    sessionId: eventData.session_id,
    saveToDatabase: eventData.payload?.saveToDatabase !== false,
  };
  return webhookData;
}

export async function processIdentifyEvent(
  user: User,
  eventData: BaseEvent & { timestamp: string },
  updateUserProps: (properties: Record<string, any>) => Promise<void>,
  logger: Logger,
): Promise<Partial<Webhook>> {
  const hasFcaid = !!user.fcaid;
  const updates = processIdentifyProperties(user, eventData, hasFcaid, logger);
  if (hasFcaid && user.installed_at && user.properties.birthDate) {
    const age = calculateUserCurrentAge(
      user.installed_at,
      user.properties.userDeclaredAge,
      user.properties.birthDate,
    );
    if (age !== null) {
      const isDate = user.installed_at instanceof Date;
      if (!isDate) {
        logger.warn(
          `user.installed_at not a Date, got ${typeof user.installed_at}, value ${
            user.installed_at
          }. User ${user.type} - ${user.fcid}. Converting.`,
        );
      }
      const fixedToDate = isDate ? user.installed_at : new Date(user.installed_at);
      const installYear = fixedToDate.getUTCFullYear();
      const declaredAgeAtInstall = age - (new Date().getUTCFullYear() - installYear);
      if (user.properties.userDeclaredAge !== declaredAgeAtInstall) {
        updates.userDeclaredAge = { value: declaredAgeAtInstall, op: 'set' };
      }
      const currentAgeFromBirthDate = calculateUserCurrentAge(
        user.installed_at,
        declaredAgeAtInstall,
        user.properties.birthDate,
      );
      if (
        currentAgeFromBirthDate !== null &&
        user.properties.userCurrentAge !== currentAgeFromBirthDate
      ) {
        updates.userCurrentAge = { value: currentAgeFromBirthDate, op: 'set' };
      }
    }
  }
  const updatedProperties = getUpdatedProperties(user.properties, updates);
  const wasUpdated = Object.keys(updatedProperties).length > 0;
  if (wasUpdated) await updateUserProps(updatedProperties);

  const modifiedProperties = wasUpdated
    ? toModifiedProperties(updatedProperties, eventData.timestamp)
    : [];

  const webhookData = createIdentifyWebhookData(eventData, modifiedProperties);
  logger.log(`✅ Successfully processed ${eventData.provider} event: ${eventData.event_name}.`);
  return webhookData;
}
