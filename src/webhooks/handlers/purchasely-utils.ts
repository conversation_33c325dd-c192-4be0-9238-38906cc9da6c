import type { PurchaselyEventPayload } from '../schemas/purchasely.schema';

// Add a mapping object for event name translations
export const eventNameMapping: Record<string, string> = {
  ENTERED_BILLING_RETRY: 'Subscription Billing Status Changed',
  GRACE_PERIOD_STARTED: 'Subscription Billing Status Changed',
  GRACE_PERIOD_TERMINATED: 'Subscription Billing Status Changed',
  SUBSCRIPTION_RECOVERED_FROM_GRACE_PERIOD: 'Subscription Billing Status Changed',
  SUBSCRIPTION_CANCELLED_DURING_GRACE_PERIOD: 'Subscription Billing Status Changed',
  SUBSCRIPTION_RECOVERED_FROM_BILLING_RETRY: 'Subscription Billing Status Changed',
  SUBSCRIPTION_DEFERRED: 'Subscription Deferred',
  INTRO_OFFER_CONVERTED: 'Subscription Offer Converted',
  PROMOTIONAL_OFFER_CONVERTED: 'Subscription Offer Converted',
  TRIAL_CONVERTED: 'Subscription Offer Converted',
  PROMO_CODE_CONVERTED: 'Subscription Offer Converted',
  INTRO_OFFER_STARTED: 'Subscription Offer Started',
  PROMOTIONAL_OFFER_STARTED: 'Subscription Offer Started',
  TRIAL_STARTED: 'Subscription Offer Started',
  PROMO_CODE_STARTED: 'Subscription Offer Started',
  SUBSCRIPTION_RECEIVED: 'Subscription Owner Changed',
  SUBSCRIPTION_TRANSFERRED: 'Subscription Owner Changed',
  SUBSCRIPTION_CANCELLED_DURING_PAUSE: 'Subscription Pause Status Changed',
  SUBSCRIPTION_PAUSED: 'Subscription Pause Status Changed',
  SUBSCRIPTION_UNPAUSED: 'Subscription Pause Status Changed',
  SUBSCRIPTION_WILL_NOT_PAUSE: 'Subscription Pause Status Changed',
  SUBSCRIPTION_WILL_PAUSE: 'Subscription Pause Status Changed',
  SUBSCRIPTION_CROSSGRADED: 'Subscription Plan Changed',
  SUBSCRIPTION_DOWNGRADED: 'Subscription Plan Changed',
  SUBSCRIPTION_UPGRADED: 'Subscription Plan Changed',
  SUBSCRIPTION_REACTIVATED: 'Subscription Reactivated',
  SUBSCRIPTION_REFUNDED_REVOKED: 'Subscription Refunded',
  RENEWAL_DISABLED: 'Subscription Renewal Changed',
  RENEWAL_ENABLED: 'Subscription Renewal Changed',
  SUBSCRIPTION_RENEWED: 'Subscription Renewed',
  SUBSCRIPTION_STARTED: 'Subscription Started',
  SUBSCRIPTION_TERMINATED: 'Subscription Terminated',
  TRANSACTION_PROCESSED: 'Subscription Transaction Processed',
};

// Add a rename mapping object for payload keys
export const renameMapping: Record<string, string> = {
  event_id: 'Purchasely Event ID',
  anonymous_user_id: 'Purchasely Anonymous User ID',
  source_event_name: 'Trigger Action',
  transferred_from_user_id: 'Transferred From',
  transferred_to_user_id: 'Transferred To',
  subscription_status: 'Subscription State',
  grace_period_expires_at: 'Grace Period Expiration Date',
  effective_next_renewal_at: 'Effective Next Renewal Date',
  next_renewal_at: 'Next Renewal Date',
  defer_end_at: 'Defer Ending Date',
  auto_resume_at: 'Auto Resume Date',
  presentation: 'Paywall ID',
  placement: 'Placement ID',
  ab_test: 'Purchasely AB Test ID',
  ab_test_variant: 'Purchasely AB Test Variant',
  audience: 'Purchasely Audience',
  offer_code_ref_name: 'Offer Code Ref Name',
  offer_identifier: 'Offer ID',
};

// Utility function to convert strings to proper case
export function toProperCase(value: string): string {
  return value
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Extract the last part of an event name and capitalize it
export function extractActionFromEvent(eventName: string): string {
  const action = eventName.split('_').pop() || '';
  return action.charAt(0).toUpperCase() + action.slice(1).toLowerCase();
}

export function getAdditionalProperties(eventData: PurchaselyEventPayload): Record<string, any> {
  const additionalProperties: Record<string, any> = {};

  // Translate the event name using the mapping
  const translatedEventName = eventNameMapping[eventData.event_name] || eventData.event_name;

  switch (translatedEventName) {
    case 'Subscription Transaction Processed':
      additionalProperties['$revenue'] = eventData.payload?.amount_in_usd;
      break;
    case 'Subscription Billing Status Changed':
      // For billing status events, we need special handling
      // Events like ENTERED_BILLING_RETRY, GRACE_PERIOD_STARTED, etc.
      if (eventData.event_name.includes('GRACE_PERIOD')) {
        additionalProperties['Billing Status'] = 'Grace Period';
      } else if (eventData.event_name.includes('BILLING_RETRY')) {
        additionalProperties['Billing Status'] = 'Billing Retry';
      } else {
        // Fallback to extracting the action
        additionalProperties['Billing Status'] = extractActionFromEvent(eventData.event_name);
      }
      break;
    case 'Subscription Owner Changed':
      // Extract just the action part (after the last underscore) and convert to proper case
      additionalProperties['Ownership'] = extractActionFromEvent(eventData.event_name);
      break;
    case 'Subscription Pause Status Changed':
      // Extract just the action part for pause status
      additionalProperties['Pause Status'] = extractActionFromEvent(eventData.event_name);
      break;
    case 'Subscription Plan Changed':
      // Extract just the action part for plan change
      additionalProperties['Plan Change'] = extractActionFromEvent(eventData.event_name);
      break;
    case 'Subscription Renewal Changed':
      // Extract just the action part for renewal change
      additionalProperties['Renewal Change'] = extractActionFromEvent(eventData.event_name);
      break;
    default:
      break;
  }
  return additionalProperties;
}

// Utility function to rename keys in the payload
export function renamePayloadKeys(
  payload: Record<string, any>,
  translatedEventName: string,
): Record<string, any> {
  const renamedPayload: Record<string, any> = {};
  for (const key in payload) {
    if (payload.hasOwnProperty(key)) {
      const newKey = renameMapping[key] || key;
      let value = payload[key];

      // Convert specific columns to proper case
      if (
        [
          'store',
          'purchase_type',
          'environment',
          'previous_offer_type',
          'offer_type',
          'subscription_status',
          'device_type',
          'source_event_name',
        ].includes(key)
      ) {
        value = toProperCase(value);
      }

      renamedPayload[newKey] = value;
    }
  }
  // Add the translated event name to the payload
  renamedPayload['event_name'] = translatedEventName;
  return renamedPayload;
}
