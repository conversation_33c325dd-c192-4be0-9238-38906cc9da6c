import { Injectable, Logger } from '@nestjs/common';

import { KwsService } from '../../kws/kws.service';
import { User } from '../../users/entities/user.entity';
import { processIdentifyEvent } from './identify-utils';
import {
  getAdditionalProperties,
  renamePayloadKeys,
  eventNameMapping,
  toProperCase,
} from './purchasely-utils';
import { PostgresUserService } from '../../common/services/postgres-user.service';
import { calculateUserCurrentAge } from '../../common/utils/calculate-user-age';
import { toModifiedProperties } from '../../common/utils/modified-properties';

import type { Webhook } from '../entities/webhook.entity';
import type { BaseEvent } from '../schemas/types';
import type { PropertyUpdate } from '../types';
import { ErrorLoggerService } from '../../common/services/error-logger.service';

export function getUpdatedProperties(
  properties: Record<string, unknown>,
  updates: Record<string, PropertyUpdate>,
): Record<string, unknown> {
  const updatedProperties: Record<string, unknown> = {};
  for (const [key, { value, op = 'set' }] of Object.entries(updates)) {
    const current = properties[key];
    let newValue = value;

    if (op === 'add') {
      const cur = typeof current === 'number' ? current : 0;
      const inc = typeof value === 'number' ? value : 0;
      newValue = cur + inc;
    }

    if (JSON.stringify(current) !== JSON.stringify(newValue)) {
      updatedProperties[key] = newValue;
    }
  }
  return updatedProperties;
}

const isAdEvent = (eventData: BaseEvent): boolean =>
  ['ad_shown', 'ad_impression'].includes(eventData.event_name) ||
  eventData.provider === 'iron-source';

@Injectable()
export class WebhookHandler {
  private readonly logger = new Logger(WebhookHandler.name);

  constructor(
    private readonly pgUserService: PostgresUserService,
    private readonly kwsService: KwsService,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  async handle(
    eventData: BaseEvent & { timestamp: string },
    ip?: string,
  ): Promise<Partial<Webhook> | null> {
    this.logger.debug(`Processing event for fcid: ${eventData.fcid}`);

    if (!eventData.fcid) {
      this.errorLogger.logError(new Error('FCID is missing from the event data'), undefined, {
        errorName: 'FCID is missing from the event data',
        context: 'WebhookHandler',
        includeStack: true,
        includeRequest: false,
        fcid: eventData.fcid,
        metadata: { eventData },
      });
      throw new Error('FCID is required');
    }

    // Use PostgresUserService for proper caching and user retrieval
    const user = await this.pgUserService.findByAttribute('fcid', eventData.fcid);

    if (!user) {
      this.logger.warn(`User not found for fcid: ${eventData.fcid}`);
      return null;
    }

    let updates: Record<string, PropertyUpdate> = {};

    // Handle ad events for all providers (including iron-source)
    if (isAdEvent(eventData)) {
      const revenueIncrement = eventData.payload?.revenue ? Number(eventData.payload.revenue) : 0;
      updates = { totalAdsShown: { op: 'add', value: 1 } };
      if (revenueIncrement) {
        updates.totalAdRevenue = { op: 'add', value: revenueIncrement };
        const newTotalAdRevenue = (user.properties.totalAdRevenue || 0) + revenueIncrement;
        updates.totalRevenue = {
          op: 'set',
          value: newTotalAdRevenue + (user.properties.totalSubscriptionRevenue || 0),
        };
      }
    } else if (eventData.provider === 'purchasely') {
      return this.processPurchaselyEvent(user, eventData);
    } else if (eventData.provider === 'FlipaClip') {
      if (eventData.event_name === 'age_selected') {
        return this.processAgeEvent(user, eventData, ip);
      } else if (eventData.event_name === 'identify') {
        return processIdentifyEvent(
          user,
          eventData,
          async (properties: Record<string, any>) => {
            await this.pgUserService.updateUser(user.fcid, {
              ...user,
              properties: { ...user.properties, ...properties },
            });
            this.logger.debug(`Updated properties for user ${user.fcid}`);
          },
          this.logger,
        );
      } else if (eventData.event_name === 'project_backed_up') {
        updates.totalProjectsBackedUpCount = { op: 'add', value: 1 };
      } else if (eventData.event_name === 'subscription_offer_shown') {
        updates.totalSubscriptionOffers = { op: 'add', value: 1 };
      } else if (eventData.event_name === 'subscription_offer_aborted') {
        updates.totalSubscriptionOffersAborted = { op: 'add', value: 1 };
      }
    }

    const updatedProperties = getUpdatedProperties(user.properties, updates);
    if (Object.keys(updatedProperties).length) {
      // Use PostgresUserService for proper caching and updates
      await this.pgUserService.updateUser(user.fcid, {
        ...user,
        properties: { ...user.properties, ...updatedProperties },
      });
      this.logger.debug(`Updated properties for user ${user.fcid}`);
    }

    const webhookData = {
      eventTimestamp: eventData.timestamp,
      fcid: eventData.fcid,
      eventName: eventData.event_name,
      provider: eventData.provider,
      store: eventData.store,
      payload: eventData.payload,
      modified_properties: toModifiedProperties(updatedProperties, eventData.timestamp),
      sessionId: eventData.session_id,
    };

    this.logger.log(
      `✅ Successfully processed ${eventData.provider} event: ${eventData.event_name}.`,
    );
    return webhookData;
  }

  async processAgeEvent(user: User, eventData: BaseEvent & { timestamp: string }, ip?: string) {
    let additionalWebhookPayload: Record<string, any> = {
      ageSelectorId: eventData.payload.ageSelectorId,
    };
    const updates: Record<string, PropertyUpdate> = {};
    if (user.properties.userDeclaredAge !== eventData.payload.userDeclaredAge) {
      const declaredAge = eventData.payload.userDeclaredAge;
      updates.userDeclaredAge = { value: declaredAge, op: 'set' };
      const age = calculateUserCurrentAge(
        user.installed_at,
        declaredAge,
        user.properties.birthDate,
      );
      if (age !== null && user.properties.userCurrentAge !== age) {
        updates.userCurrentAge = { value: age, op: 'set' };
        const kwsResult = await this.kwsService.getAgeGateData({
          ip,
          age,
        });
        const isMinor = kwsResult.consentAge >= age;
        updates.isMinor = { op: 'set', value: isMinor };
        additionalWebhookPayload = {
          ...additionalWebhookPayload,
          isMinor,
        };
      }
    }
    const updatedProperties = getUpdatedProperties(user.properties, updates);
    if (Object.keys(updatedProperties).length) {
      // Use PostgresUserService for proper caching and updates
      await this.pgUserService.updateUser(user.fcid, {
        ...user,
        properties: { ...user.properties, ...updatedProperties },
      });
      this.logger.debug(`Updated properties for user ${user.fcid}`);
    }

    const webhookData = {
      eventTimestamp: eventData.timestamp,
      fcid: eventData.fcid,
      eventName: eventData.event_name,
      provider: eventData.provider,
      store: eventData.store,
      payload: { ...eventData.payload, ...additionalWebhookPayload },
      modified_properties: toModifiedProperties(updatedProperties, eventData.timestamp),
      sessionId: eventData.session_id,
    };

    this.logger.log(
      `✅ Successfully processed ${eventData.provider} event: ${eventData.event_name}.`,
    );
    return webhookData;
  }

  async processPurchaselyEvent(
    user: User,
    eventData: BaseEvent & { timestamp: string },
  ): Promise<Partial<Webhook>> {
    const revenueIncrement = eventData.payload?.amount_in_usd || 0;
    const updates: Record<string, PropertyUpdate> = {
      subscriptionType: { op: 'set', value: eventData.payload?.plan },
      subscriptionState: {
        op: 'set',
        value: toProperCase(eventData.payload?.subscription_status ?? ''),
      },
      purchasely_subscription_id: {
        op: 'set',
        value: eventData.payload?.purchasely_subscription_id,
      },
    };
    if (revenueIncrement) {
      updates.totalSubscriptionRevenue = { op: 'add', value: revenueIncrement };
      updates.totalRevenue = {
        op: 'set',
        value:
          (user.properties.totalAdRevenue || 0) +
          (user.properties.totalSubscriptionRevenue || 0) +
          revenueIncrement,
      };
    }
    const updatedProperties = getUpdatedProperties(user.properties, updates);
    if (Object.keys(updatedProperties).length) {
      // Use PostgresUserService for proper caching and updates
      await this.pgUserService.updateUser(user.fcid, {
        ...user,
        properties: { ...user.properties, ...updatedProperties },
      });
      this.logger.debug(`Updated properties for user ${user.fcid}`);
    }

    const translatedEventName = eventNameMapping[eventData.event_name] || eventData.event_name;
    const overwritedPayload = renamePayloadKeys(
      { ...eventData.payload, ...getAdditionalProperties(eventData) },
      translatedEventName,
    );

    const webhookData = {
      eventTimestamp: eventData.timestamp,
      fcid: eventData.fcid,
      eventName: translatedEventName,
      provider: eventData.provider,
      store: eventData.store,
      payload: overwritedPayload,
      modified_properties: toModifiedProperties(updatedProperties, eventData.timestamp),
      sessionId: eventData.session_id,
    };

    this.logger.log(
      `✅ Successfully processed ${eventData.provider} event: ${
        translatedEventName || eventData.event_name
      }.`,
    );
    return webhookData;
  }
}
