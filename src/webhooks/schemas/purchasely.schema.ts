import { Schema } from 'jsonschema';

import type { BaseEvent, EventControl } from './types';

export interface PurchaselyEventPayload extends BaseEvent {
  event_name: string;
  event_control: EventControl;
  provider: string;
  fcid: string;
  payload: {
    amount_in_usd?: number;
    subscription_status?: string;
    store?: string;
    plan?: string;
    purchasely_subscription_id?: string;
  };
}

export const purchaselyEventSchema: Schema = {
  type: 'object',
  properties: {
    event_name: { type: 'string' },
    provider: { type: 'string' },
    fcid: { type: 'string' },
    payload: {
      type: 'object',
      additionalProperties: true,
    },
  },
  required: ['event_name', 'provider', 'fcid'],
  additionalProperties: true,
};
