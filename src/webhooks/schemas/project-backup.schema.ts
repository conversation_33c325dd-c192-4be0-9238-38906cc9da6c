import { Schema } from 'jsonschema';

import type { BaseEvent, EventControl } from './types';

export interface ProjectBackupEventPayload extends BaseEvent {
  event_name: string;
  event_control: EventControl;
  provider: string;
  fcid: string;
  store?: string;
  payload: {
    canvasSize: string;
    fps: number;
    backgroundType: string;
    totalFramesCount: number;
    projectType: string;
    projectId: string;
    templateId: string;
    isImportedProject: boolean;
    triggerAction: string;
  };
}

export const projectBackupEventSchema: Schema = {
  type: 'object',
  properties: {
    event_name: {
      type: 'string',
      enum: ['project_backed_up'],
    },
    event_control: {
      type: 'object',
      additionalProperties: {
        type: ['string', 'number'],
      },
    },
    provider: { type: 'string' },
    fcid: { type: 'string' },
    session_id: {
      type: 'number',
      description:
        'The start time of the session in milliseconds since epoch (Unix Timestamp). A session_id of -1 is the same as no session_id specified.',
    },
    store: {
      type: 'string',
      enum: ['google_play', 'apple_app_store', 'amazon_store', 'huawei_store'],
    },
    payload: {
      type: 'object',
      properties: {
        canvasSize: {
          type: 'string',
          description: 'The canvas size of the project backed up',
        },
        fps: {
          type: 'integer',
          minimum: 1,
          description: 'The FPS of the project backed up',
        },
        backgroundType: {
          type: 'string',
          description: 'The background type of the project backed up',
        },
        totalFramesCount: {
          type: 'integer',
          minimum: 1,
          description: 'The frames count of the project backed up',
        },
        projectType: {
          type: 'string',
          description: 'The type of the project backed up',
        },
        projectId: {
          type: 'string',
          description: 'The project ID of project backed up',
        },
        templateId: {
          type: 'string',
          description: 'Formerly crmb_id of the project backed up',
        },
        isImportedProject: {
          type: 'boolean',
          description: 'Was the backed up project originally imported?',
        },
        triggerAction: {
          type: 'string',
          description: 'The action that triggered the backup',
        },
      },
      required: ['canvasSize', 'fps'],
      additionalProperties: false,
    },
  },
  required: ['event_name', 'event_control', 'provider', 'fcid', 'store', 'payload'],
  additionalProperties: true,
};
