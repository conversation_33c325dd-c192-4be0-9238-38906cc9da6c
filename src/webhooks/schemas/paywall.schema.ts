import { Schema } from 'jsonschema';

import type { BaseEvent, EventControl } from './types';

export interface PaywallEventPayload extends BaseEvent {
  event_name: string;
  event_control: EventControl;
  provider: string;
  fcid: string;
  store?: string;
  payload: Record<string, any>;
}

export const paywallEventSchema: Schema = {
  type: 'object',
  properties: {
    event_name: { type: 'string' },
    provider: { type: 'string' },
    fcid: { type: 'string' },
    store: {
      type: 'string',
      enum: ['google_play', 'apple_app_store', 'amazon_store', 'huawei_store'],
    },
    payload: {
      type: 'object',
      additionalProperties: true,
    },
  },
  required: ['event_name', 'provider', 'fcid', 'store'],
  additionalProperties: true,
};
