import { Schema } from 'jsonschema';

export interface MoEngageEvent {
  event_name: string;
  event_code: string;
  event_uuid: string;
  event_time: number;
  event_type: string;
  event_source: string;
  uid: string;
  event_attributes: Record<string, any>;
  user_attributes: Record<string, any>;
  device_attributes: Record<string, any>;
  push_id?: string;
  email_id?: string;
  mobile_number?: string;
}

export interface MoEngageEventPayload {
  app_name: string;
  source: string;
  moe_request_id: string;
  events: MoEngageEvent[];
}

export const moengageEventSchema: Schema = {
  type: 'object',
  properties: {
    app_name: { type: 'string' },
    source: { type: 'string' },
    moe_request_id: { type: 'string' },
    events: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          event_name: { type: 'string' },
          event_code: { type: 'string' },
          event_uuid: { type: 'string' },
          event_time: { type: 'number' },
          event_type: { type: 'string' },
          event_source: { type: 'string' },
          uid: { type: 'string' },
          event_attributes: {
            type: 'object',
            additionalProperties: true,
          },
          user_attributes: {
            type: 'object',
            additionalProperties: true,
          },
          device_attributes: {
            type: 'object',
            additionalProperties: true,
          },
          push_id: { type: 'string' },
          email_id: { type: 'string' },
          mobile_number: { type: 'string' },
        },
        required: [
          'event_name',
          'event_code',
          'event_uuid',
          'event_time',
          'event_type',
          'event_source',
          'uid',
          'event_attributes',
          'user_attributes',
          'device_attributes',
        ],
        additionalProperties: true,
      },
    },
  },
  required: ['app_name', 'source', 'moe_request_id', 'events'],
  additionalProperties: true,
};

export const isMoEngageEventPayload = (event: unknown): event is MoEngageEventPayload => {
  return (
    !!event &&
    typeof event === 'object' &&
    (event as MoEngageEventPayload).source === 'MOENGAGE' &&
    Array.isArray((event as MoEngageEventPayload).events)
  );
};
