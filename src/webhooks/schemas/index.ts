import { BadRequestException } from '@nestjs/common';
import { Schema, Validator } from 'jsonschema';

import { adEventSchema } from './ad.schema';
import { ageSelectedEventSchema } from './age-selected.schema';
import { baseEventSchema } from './base.schema';
import { moengageEventSchema } from './moengage.schema';
import { paywallEventSchema } from './paywall.schema';
import { purchaselyEventSchema } from './purchasely.schema';
import { projectBackupEventSchema } from './project-backup.schema';

import type { BaseEvent } from './types';

const validator = new Validator();

export function validateEventSchema(event: unknown) {
  if (event && typeof event === 'object') {
    let customSchema: Schema | undefined;
    if ('source' in event && event.source === 'MOENGAGE') {
      customSchema = moengageEventSchema;
    }
    if ('provider' in event && event.provider === 'purchasely') {
      customSchema = purchaselyEventSchema;
    }

    const baseEvent = event as BaseEvent;
    if (!customSchema) {
      const validationResult = validator.validate(event, baseEventSchema);
      if (!validationResult.valid) {
        throw new BadRequestException({
          message: 'Invalid webhook event format: base fields are wrong.',
          errors: validationResult.errors,
        });
      }

      const schemaMap: Record<string, Schema> = {
        subscription_offer_shown: paywallEventSchema,
        subscription_offer_aborted: paywallEventSchema,
        ad_shown: adEventSchema,
        ad_impression: adEventSchema,
        age_selected: ageSelectedEventSchema,
        project_backed_up: projectBackupEventSchema,
      };

      customSchema = schemaMap[baseEvent.event_name];
      if (!customSchema && baseEvent.provider === 'iron-source') {
        customSchema = adEventSchema;
      }
    }

    if (customSchema) {
      const validationResult = validator.validate(baseEvent, customSchema);
      if (!validationResult.valid) {
        throw new BadRequestException({
          message: `Invalid ${(event as any).provider || (event as any).source} ${
            baseEvent.event_name
          } webhook event format.`,
          errors: validationResult.errors,
        });
      }
    }
  }
}
