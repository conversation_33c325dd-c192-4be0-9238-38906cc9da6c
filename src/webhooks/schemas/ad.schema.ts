import { Schema } from 'jsonschema';

import type { BaseEvent, EventControl } from './types';

export interface AdEventPayload extends BaseEvent {
  event_name: string;
  event_control: EventControl;
  provider: string;
  fcid: string;
  store: string;
  payload: {
    revenue: number;
    adType: 'Interstitial' | 'Rewarded';
    abTestGroup?: string;
    instanceName?: string;
    segmentName?: string;
    loadTime: number;
    adUnitId: string;
    isRewardGranted?: boolean;
    publisherNetwork: string;
    triggerAction: string;
  };
}

export const adEventSchema: Schema = {
  id: '/AdEvent',
  type: 'object',
  properties: {
    event_name: { type: 'string' },
    event_control: {
      type: 'object',
      additionalProperties: {
        type: ['string', 'number'],
      },
    },
    provider: { type: 'string' },
    fcid: { type: 'string' },
    session_id: { type: 'number' },
    store: {
      type: 'string',
      enum: ['google_play', 'apple_app_store', 'amazon_store', 'huawei_store'],
    },
    payload: {
      type: 'object',
      properties: {
        revenue: {
          type: 'number',
          minimum: 0.000001,
          maximum: 1,
          description:
            'The amount of revenue generated by this impression using Amplitude specific property',
        },
        adType: {
          type: 'string',
          enum: ['Interstitial', 'Rewarded', 'Native', 'Banner'],
          description: 'The type of ad that was shown',
        },
        abTestGroup: {
          type: 'string',
          description: 'Mediation A/B test group',
        },
        instanceName: {
          type: 'string',
          description: 'Mediation instance name',
        },
        segmentName: {
          type: 'string',
          description: 'Mediation segment name',
        },
        loadTime: {
          type: 'number',
          minimum: 0,
          description: 'The loading time in seconds for the ad to be shown',
        },
        adUnitId: {
          type: 'string',
          description: 'The unique identifier for the ad unit that was shown',
        },
        isRewardGranted: {
          type: 'boolean',
          description: 'Whether the user was granted a reward for watching the ad',
        },
        publisherNetwork: {
          type: 'string',
          description: 'The ad network that served the ad',
        },
        triggerAction: {
          type: 'string',
          description: 'What caused the ad to trigger. Sometimes called placement',
        },
      },
      required: ['revenue', 'adType', 'loadTime', 'adUnitId', 'publisherNetwork', 'triggerAction'],
      additionalProperties: false,
    },
  },
  required: ['event_name', 'provider', 'fcid', 'store', 'payload'],
  additionalProperties: false,
};
