import { Schema } from 'jsonschema';

import type { BaseEvent, EventControl } from './types';

interface AgeSelectedPayload {
  ageSelectorId?: string;
  userDeclaredAge: number;
}

export interface AgeSelectedEventPayload extends BaseEvent {
  event_name: string;
  event_control: EventControl;
  provider: string;
  fcid: string;
  store?: string;
  payload: AgeSelectedPayload;
}

export const ageSelectedEventSchema: Schema = {
  type: 'object',
  properties: {
    event_name: { type: 'string' },
    provider: { type: 'string' },
    fcid: { type: 'string' },
    store: {
      type: 'string',
      enum: ['google_play', 'apple_app_store', 'amazon_store', 'huawei_store'],
    },
    payload: {
      type: 'object',
      additionalProperties: true,
    },
  },
  required: ['event_name', 'provider', 'fcid', 'store'],
  additionalProperties: true,
};
