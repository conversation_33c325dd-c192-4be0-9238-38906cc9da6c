import { Schema } from 'jsonschema';

export const baseEventSchema: Schema = {
  type: 'object',
  properties: {
    event_name: {
      type: 'string',
      enum: [
        'ad_shown',
        'age_selected',
        'subscription_offer_shown',
        'subscription_offer_aborted',
        'project_backed_up',
        'ENTERED_BILLING_RETRY',
        'GRACE_PERIOD_STARTED',
        'GRACE_PERIOD_TERMINATED',
        'SUBSCRIPTION_RECOVERED_FROM_GRACE_PERIOD',
        'SUBSCRIPTION_CANCELLED_DURING_GRACE_PERIOD',
        'SUBSCRIPTION_RECOVERED_FROM_BILLING_RETRY',
        'SUBSCRIPTION_DEFERRED',
        'INTRO_OFFER_CONVERTED',
        'PROMOTIONAL_OFFER_CONVERTED',
        'TRIAL_CONVERTED',
        'PROMO_CODE_CONVERTED',
        'INTRO_OFFER_STARTED',
        'PROMOTIONAL_OFFER_STARTED',
        'TRIAL_STARTED',
        'PROMO_CODE_STARTED',
        'SUBSCRIPTION_RECEIVED',
        'SUBSCRIPTION_TRANSFERRED',
        'SUBSCRIPTION_CANCELLED_DURING_PAUSE',
        'SUBSCRIPTION_PAUSED',
        'SUBSCRIPTION_UNPAUSED',
        'SUBSCRIPTION_WILL_NOT_PAUSE',
        'SUBSCRIPTION_WILL_PAUSE',
        'SUBSCRIPTION_CROSSGRADED',
        'SUBSCRIPTION_DOWNGRADED',
        'SUBSCRIPTION_UPGRADED',
        'SUBSCRIPTION_REACTIVATED',
        'SUBSCRIPTION_REFUNDED_REVOKED',
        'RENEWAL_DISABLED',
        'RENEWAL_ENABLED',
        'SUBSCRIPTION_RENEWED',
        'SUBSCRIPTION_STARTED',
        'SUBSCRIPTION_TERMINATED',
        'TRANSACTION_PROCESSED',
        'identify',
        'user_merged',
      ],
    },
    event_control: {
      type: 'object',
      additionalProperties: {
        type: ['string', 'number'],
      },
    },
    provider: { type: 'string' },
    fcid: { type: 'string' },
    session_id: {
      type: 'number',
      description:
        'The start time of the session in milliseconds since epoch (Unix Timestamp). A session_id of -1 is the same as no session_id specified.',
    },
    store: {
      type: 'string',
      enum: ['google_play', 'apple_app_store', 'amazon_store', 'huawei_store'],
    },
    payload: {
      type: 'object',
      additionalProperties: true,
    },
  },
  required: ['event_name', 'event_control', 'provider', 'fcid'],
  additionalProperties: true,
};
