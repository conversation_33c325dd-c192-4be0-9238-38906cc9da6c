import {
  Body,
  BadRequestException,
  ConflictException,
  Controller,
  InternalServerErrorException,
  Ip,
  Post,
  UseGuards,
  HttpStatus,
  Response,
} from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';
import { Response as ExpressResponse } from 'express';

import { throttleConfig } from '../config/throttle.config';
import { batchConfig } from '../config/batch.config';
import { JwtAuthGuard } from '../guards/auth.guard';
import { Webhook } from './entities/webhook.entity';
import { modifiedPropertiesArrayToObject } from './utils/modified-properties.util';
import { validateEventSchema } from './schemas';
import { isMoEngageEventPayload } from './schemas/moengage.schema';
import { BatchWebhookService } from './services/batch-webhook.service';
import { SingleWebhookService } from './services/single-webhook.service';
import { TimestampValidationService } from './services/timestamp-validation.service';

import type { BaseEvent } from './schemas/types';

function enrichEvent<T extends Record<string, any>>(
  event: T,
): T & { timestamp: string; store: string } {
  return { ...event, timestamp: new Date().toISOString(), store: event.store || 'unknown' };
}

@Controller('webhooks')
@UseGuards(JwtAuthGuard)
export class WebhooksController {
  constructor(
    private readonly singleWebhookService: SingleWebhookService,
    private readonly batchWebhookService: BatchWebhookService,
    private readonly timestampValidationService: TimestampValidationService,
  ) {}

  /**
   * Handles incoming webhook events by validating, enriching, and processing them.
   * Supports both FlipaClip and Purchasely events.
   */
  @Post()
  @Throttle(throttleConfig.webhooks)
  async handleWebhook(@Body() event: unknown, @Ip() ip: string) {
    try {
      validateEventSchema(event);
      if (isMoEngageEventPayload(event)) {
        await this.singleWebhookService.processMoengageEvents(
          event.events,
          new Date().toISOString(),
        );
        return {
          message: 'Success',
          statusCode: 201,
          status: 'success',
          processed_events: event.events.map(e => e.event_uuid),
          timestamp: new Date().toISOString(),
        };
      }
      const baseEvent = event as BaseEvent;
      // Skip timestamp validation for Purchasely webhooks
      if (baseEvent.provider !== 'purchasely') {
        const timestampResult = await this.timestampValidationService.validateEventByTimestamp(
          baseEvent,
        );
        if (timestampResult && timestampResult.alreadyProcessed) {
          return {
            message: 'Webhook event already processed: timestamp is outdated.',
            statusCode: 201,
            status: 'already_processed',
          };
        }
      }
      const result = await this.singleWebhookService.processEvent(enrichEvent(baseEvent), ip);
      const modified_properties = Array.isArray(result?.modified_properties)
        ? { [String(result.fcid)]: modifiedPropertiesArrayToObject(result.modified_properties) }
        : result?.modified_properties || {};

      // Return the modified properties as a property inside data
      return {
        message: 'Success',
        statusCode: 201,
        status: 'success',
        modified_properties,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to process webhook event. Error message: ${error.message}`,
      );
    }
  }

  /**
   * Handles batch webhook processing.
   * Only supports FlipaClip events.
   */
  @Post('batch')
  @Throttle(throttleConfig.webhooksBatch)
  async handleWebhookBatch(
    @Body() payload: { events: unknown[] },
    @Ip() ip: string,
    @Response() response: ExpressResponse,
  ) {
    if (!Array.isArray(payload.events)) {
      throw new BadRequestException('Batch payload must contain an events array');
    }

    if (payload.events.length > batchConfig.maxBatchSize) {
      throw new BadRequestException(`Batch size cannot exceed ${batchConfig.maxBatchSize} events`);
    }

    const validatedEvents: Array<BaseEvent & { timestamp: string }> = [];
    const validationErrors: Array<{ index: number; error: string; event: unknown }> = [];

    // Validate all events first
    for (const [index, event] of payload.events.entries()) {
      try {
        if (!event || typeof event !== 'object' || !('event_name' in event)) {
          validationErrors.push({
            index,
            error: 'Missing required field: event_name',
            event,
          });
          continue;
        }

        try {
          validateEventSchema(event);
        } catch (error) {
          validationErrors.push({
            index,
            error: error.message,
            event,
          });
          continue;
        }
        const baseEvent = event as BaseEvent;
        if (baseEvent.provider !== 'purchasely') {
          const timestampResult = await this.timestampValidationService.validateEventByTimestamp(
            baseEvent,
          );
          if (timestampResult && timestampResult.alreadyProcessed) {
            validationErrors.push({
              index,
              error: 'Webhook event already processed: timestamp is outdated.',
              event,
            });
            continue;
          }
        }
        validatedEvents.push(enrichEvent(baseEvent));
      } catch (error) {
        validationErrors.push({
          index,
          error: error.message,
          event,
        });
      }
    }

    const buildBatchResponse = (
      statusCode: number,
      message: string,
      status: string,
      extra: any = {},
    ) => {
      return {
        message,
        statusCode,
        status,
        ...extra,
      };
    };

    if (validatedEvents.length === 0) {
      // Check if all validation errors are 'Webhook event already processed: timestamp is outdated.'
      const allOutdated =
        validationErrors.length > 0 &&
        validationErrors.every(
          err => err.error === 'Webhook event already processed: timestamp is outdated.',
        );
      if (allOutdated) {
        response.status(HttpStatus.OK);
        return response.json(
          buildBatchResponse(
            200,
            'Webhook event already processed: timestamp is outdated.',
            'already_processed',
            { validationErrors },
          ),
        );
      }
      response.status(HttpStatus.BAD_REQUEST);
      return response.json(
        buildBatchResponse(400, 'No valid events found in batch', 'error', { validationErrors }),
      );
    }

    try {
      // Add each validated event to the queue and collect results
      const results: Array<{ status: string; modified_properties: any }> = [];
      const processingErrors: Array<{ eventId: string; error: string }> = [];
      let processedCount = 0;
      let failedCount = 0;

      for (const event of validatedEvents) {
        try {
          // Add event to queue with retry configuration and get result
          const result = await this.batchWebhookService.processWebhook(
            {
              eventName: event.event_name,
              provider: event.provider,
              store: event.store || 'unknown',
              deviceId: event.event_control.device_id,
              eventTimestamp: event.timestamp,
              payload: event.payload,
              fcid: event.fcid,
              sessionId: event.session_id,
              modified_properties: {},
              eventControlDeviceId: event.event_control.device_id,
              eventControlTimestamp: String(
                event.event_control.event_timestamp ?? event.event_control.timestamp,
              ),
            } as Webhook,
            ip,
          );

          const modifiedProps = Array.isArray(result?.modified_properties)
            ? { [String(result.fcid)]: modifiedPropertiesArrayToObject(result.modified_properties) }
            : result?.modified_properties || {};
          results.push({
            status: 'success',
            modified_properties: modifiedProps,
          });
          processedCount++;
        } catch (error) {
          failedCount++;
          processingErrors.push({
            eventId: event.fcid,
            error: error.message,
          });
        }
      }

      const totalCount = validatedEvents.length;

      // If all events failed
      if (processedCount === 0) {
        response.status(HttpStatus.BAD_REQUEST);
        return response.json(
          buildBatchResponse(400, 'All webhook events failed to process', 'error', {
            validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
            processingErrors,
          }),
        );
      }

      // If some events failed or have validation errors
      if (failedCount > 0 || validationErrors.length > 0) {
        response.status(HttpStatus.ACCEPTED);
        return response.json(
          buildBatchResponse(
            202,
            `Processed ${processedCount} out of ${totalCount} events successfully`,
            'partial_success',
            {
              processed: processedCount,
              failed: failedCount + validationErrors.length,
              results,
              validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
              processingErrors,
            },
          ),
        );
      }

      // If all events succeeded
      response.status(HttpStatus.CREATED);
      return response.json(
        buildBatchResponse(201, `Successfully processed all ${totalCount} events`, 'success', {
          processed: processedCount,
          failed: 0,
          results,
        }),
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        response.status(HttpStatus.BAD_REQUEST);
        return response.json(buildBatchResponse(400, error.message, 'error'));
      }
      response.status(HttpStatus.INTERNAL_SERVER_ERROR);
      return response.json(
        buildBatchResponse(500, `Failed to process batch events: ${error.message}`, 'error'),
      );
    }
  }
}
