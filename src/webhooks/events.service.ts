import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, QueryFailedError } from 'typeorm';

import { Webhook } from './entities/webhook.entity';
import { DeviceIdentifiers } from '../users/user.dto';
import { Config } from '../config/interfaces/config.interface';
import { ErrorLoggerService } from '../common/services/error-logger.service';

const getPlatform = (identifiers?: DeviceIdentifiers): string => {
  if (!identifiers) return 'Unknown';
  const keys = Object.keys(identifiers);
  const platform =
    keys.includes('idfa') || keys.includes('idfv')
      ? 'iOS'
      : keys.includes('adid') || keys.includes('gaid')
      ? 'Android'
      : 'Unknown';
  return platform;
};

type DebugTriggerAction = 'User Updated' | 'User Created' | 'JWT Refreshed' | 'JWT Created';

@Injectable()
export class EventsService {
  private readonly logger = new Logger(EventsService.name);

  private eventsPool = new Array<Webhook>();

  /**
   * Flushes the events pool to the database. Prevents concurrent flushes and retries on transient errors.
   * Uses exponential backoff for up to 3 retries. Discards events that fail due to foreign key constraint violations
   * (PostgreSQL SQLSTATE 23503). Logs detailed errors and warnings if the pool grows too large.
   * @private
   */
  private isFlushing = false;

  constructor(
    @InjectRepository(Webhook)
    private readonly webhookRepository: Repository<Webhook>,
    private readonly configService: ConfigService<Config>,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  async addWebhookToPool(data: Partial<Webhook>) {
    this.addEventToPool(data as Webhook);
  }

  async generateEvent(
    eventName: string,
    fcid: string,
    payload: Record<string, string | boolean>,
  ): Promise<void> {
    try {
      this.addEventToPool({
        eventTimestamp: new Date().toISOString(),
        fcid,
        eventName,
        provider: 'Roshi',
        payload,
      } as Webhook);
    } catch (error) {
      this.logger.error('Error generating event', error);
    }
  }

  async generateDebugEvent(
    trigger_action: DebugTriggerAction,
    deviceId: string,
    fcid: string,
    identifiers?: DeviceIdentifiers,
    fcaid?: string,
    platform?: string,
    payload?: Record<string, string>,
  ): Promise<void> {
    try {
      const data: Partial<Webhook> = {
        eventTimestamp: new Date().toISOString(),
        fcid: fcid,
        eventName: 'user_identification_started',
        provider: 'Roshi',
        payload: {
          platform: platform || getPlatform(identifiers as DeviceIdentifiers),
          device_id: deviceId,
          advertising_id: identifiers
            ? (identifiers['idfa'] || identifiers['gaid'] || [''])[0]
            : '',
          trigger_action,
          ...(fcaid ? { fcaid: fcaid } : {}),
          ...(payload ? payload : {}),
        },
        modified_properties: [],
        deviceId: deviceId,
      };
      const isDisabled =
        process.env.NODE_ENV === 'test' ||
        !this.configService.get('webhooks.debugEvents.enabled', { infer: true });
      if (isDisabled) {
        this.logger.debug(`Debug event generation disabled: ${JSON.stringify(data)}`);
        return;
      } else {
        this.logger.debug(`Debug event data generated: ${JSON.stringify(data)}`);
      }
      await this.addEventToPool(data as Webhook);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error generating debug event',
        context: 'EventsService',
        includeStack: true,
        includeRequest: false,
      });
    }
  }

  private async addEventToPool(data: Webhook) {
    this.eventsPool.push(data);
    const maxEventsPerFlush =
      this.configService.get<number>('webhooks.debugEvents.maxEventsPerFlush', { infer: true }) ||
      30;
    if (this.eventsPool.length > maxEventsPerFlush) {
      this.errorLogger.logError(new Error('Event pool exceeded maxEventsPerFlush'), undefined, {
        errorName: 'Event pool exceeded maxEventsPerFlush',
        context: 'EventsService',
        includeStack: true,
        includeRequest: false,
        metadata: { maxEventsPerFlush, eventsPoolLength: this.eventsPool.length },
      });
    }
    if (this.eventsPool.length >= maxEventsPerFlush) {
      await this.flushEventsPool();
    }
  }

  private async flushEventsPool(): Promise<void> {
    if (this.isFlushing) {
      this.logger.debug('Flush already in progress, skipping this flush call.');
      return;
    }

    if (this.eventsPool.length === 0) {
      return;
    }

    this.isFlushing = true;
    try {
      const MAX_RETRIES = 3;
      const RETRY_DELAY_MS = 200;
      const POOL_WARN_THRESHOLD = 1000;

      // Take a snapshot of the events to flush. This is crucial to prevent a race condition
      // where new events are added to the pool while an async flush operation is in progress.
      const eventsToFlush = [...this.eventsPool];

      let attempt = 0;
      let lastError: unknown = null;

      while (attempt < MAX_RETRIES) {
        try {
          const eventInstances = this.webhookRepository.create(eventsToFlush);
          await this.webhookRepository.insert(eventInstances);
          this.logger.debug(`Flushed ${eventsToFlush.length} events to the database`);
          // Atomically remove the flushed events from the start of the pool.
          this.eventsPool.splice(0, eventsToFlush.length);
          return; // Success!
        } catch (error) {
          lastError = error;
          const errorMsg = error instanceof Error ? error.message : String(error);
          const errorStack = error instanceof Error ? error.stack : undefined;

          // Check for PostgreSQL foreign key constraint violation (SQLSTATE 23503)
          if (error instanceof QueryFailedError && error.driverError?.code === '23503') {
            // Foreign key constraint violation - these events are invalid and should be discarded
            this.logger.debug(
              `Discarding ${eventsToFlush.length} events due to foreign key constraint violation (invalid fcid)`,
            );
            // Remove the events from the pool since they're invalid
            this.eventsPool.splice(0, eventsToFlush.length);
            return; // Exit early, don't retry invalid events
          } else {
            this.errorLogger.logError(new Error('Error flushing events pool'), undefined, {
              errorName: 'Error flushing events pool',
              context: 'EventsService',
              includeStack: true,
              includeRequest: false,
              metadata: { attempt, maxRetries: MAX_RETRIES, errorMsg, errorStack },
            });
            // Exponential backoff
            await new Promise(res => setTimeout(res, RETRY_DELAY_MS * Math.pow(2, attempt)));
            attempt++;
          }
        }
      }

      // If we reach here, all retries failed. The events remain in the pool for the next attempt.
      this.errorLogger.logError(new Error('Failed to flush events pool'), undefined, {
        errorName: 'Failed to flush events pool',
        context: 'EventsService',
        includeStack: true,
        includeRequest: false,
        metadata: { maxRetries: MAX_RETRIES, eventsPoolLength: this.eventsPool.length },
      });

      if (this.eventsPool.length > POOL_WARN_THRESHOLD) {
        this.logger.warn(
          `Events pool has grown very large (${this.eventsPool.length} events). Consider investigating database health or implementing a persistent queue.`,
        );
        // TODO: move to bullmq and add dlq
      }
    } finally {
      this.isFlushing = false;
      const maxEventsPerFlush =
        this.configService.get<number>('webhooks.debugEvents.maxEventsPerFlush', { infer: true }) ||
        30;
      if (this.eventsPool.length >= maxEventsPerFlush) {
        // Schedule another flush if needed
        setImmediate(() => this.flushEventsPool());
      }
    }
  }
}
