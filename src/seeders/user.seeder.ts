import { faker } from '@faker-js/faker';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { User } from '../users/entities/user.entity';
import { UserType } from '../users/user.dto';

const generateFcid = (existingFcid?: string | null): string => {
  return (
    existingFcid ??
    `${uuidv4()}-${Math.floor(Math.random() * 100)
      .toString()
      .padStart(2, '0')}`
  );
};

export async function seedUsers(dataSource: DataSource) {
  console.log('🚀 Seeding Users...');

  const userRepository = dataSource.getRepository(User);
  const users: User[] = [];

  for (let i = 0; i < 10; i++) {
    const user = new User();
    user.fcid = generateFcid(faker.string.uuid());
    user.fcaid = Math.random() > 0.5 ? faker.string.uuid() : undefined;
    user.type = faker.helpers.arrayElement([
      UserType.ANONYMOUS,
      UserType.REGISTERED,
      UserType.MERGED,
    ]);
    user.identifiers = {
      idfa: faker.string.uuid(),
      idfv: faker.string.uuid(),
      gaid: faker.string.uuid(),
      adid: faker.string.uuid(),
    };
    user.device_ids = [faker.string.uuid(), faker.string.uuid()];
    user.installed_at = faker.date.past();
    user.updated_at = new Date();
    user.properties = {
      totalAdRevenue: parseFloat(faker.finance.amount()),
      totalSubscriptionRevenue: parseFloat(faker.finance.amount()),
      totalAdsShown: faker.number.int({ min: 0, max: 1 }),
      subscriptionType: '',
      subscriptionState: faker.helpers.arrayElement(['active', 'inactive']),
      totalSubscriptionOffers: faker.number.int({ min: 0, max: 20 }),
      totalSubscriptionOffersAborted: faker.number.int({ min: 0, max: 20 }),
      totalRevenue: parseFloat(faker.finance.amount()),
      daysAfterInstall: faker.number.int({ min: 0, max: 30 }),
    };
    user.mergedFcids = Math.random() > 0.7 ? [faker.string.uuid()] : [];
    users.push(user);
  }

  await userRepository.save(users);
  console.log(`✅ Successfully seeded ${users.length} users!`);
}
