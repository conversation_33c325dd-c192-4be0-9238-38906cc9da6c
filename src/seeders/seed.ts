import 'reflect-metadata';
import { ConfigService } from '@nestjs/config';
import { createDataSource } from '../config/typeorm.config';
import { seedUsers } from './user.seeder';
import { seedWebhooks } from './webhook.seeder';
import { Config } from '../config/interfaces/config.interface';
import configuration from '../config/configuration';

async function runSeeders() {
  console.log('🌱 Running database seeders...');

  // Create a temporary ConfigService instance for seeding
  const configService = new ConfigService<Config>(configuration());
  const dataSource = createDataSource(configService);

  await dataSource.initialize();

  try {
    await seedUsers(dataSource);
    await seedWebhooks(dataSource);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed.');
  }
}

runSeeders();
