import { DataSource } from 'typeorm';
import { Webhook } from '../webhooks/entities/webhook.entity';
import { User } from '../users/entities/user.entity';
import { faker } from '@faker-js/faker';
import {
  eventNameMapping,
  renamePayloadKeys,
  getAdditionalProperties,
} from '../webhooks/handlers/purchasely-utils';

import type { PurchaselyEventPayload } from '../webhooks/schemas/purchasely.schema';

interface WebhookEvent {
  event_name: string;
  provider: string;
  store?: string;
  generatePayload: () => object;
}

// Define event types
export const webhookEventTypes: WebhookEvent[] = [
  {
    event_name: 'ad_impression',
    provider: 'iron-source',
    store: faker.helpers.arrayElement([
      'google_play',
      'apple_app_store',
      'amazon_store',
      'huawei_store',
    ]),
    generatePayload: () => ({
      revenue: parseFloat(faker.finance.amount()),
      adType: faker.helpers.arrayElement(['Rewarded', 'Interstitial', 'Banner']),
      abTestGroup: faker.helpers.arrayElement(['A', 'B']),
      instanceName: faker.helpers.arrayElement(['Applovin $3', 'Bidding']),
      segmentName: faker.helpers.arrayElement(['non_coppa', 'coppa']),
      loadTime: faker.number.int({ min: 0, max: 10 }),
      adUnitId: faker.helpers.arrayElement(['DefaultRewardedVideo', 'InterstitialAd', 'BannerAd']),
      isRewardGranted: faker.datatype.boolean(),
      publisherNetwork: faker.helpers.arrayElement([
        'Ironsource',
        'Applovin',
        'Unityads',
        'Vungle',
      ]),
      triggerAction: faker.helpers.arrayElement(['user_initiated', 'auto']),
    }),
  },
  {
    event_name: 'subscription_offer_shown',
    provider: 'iron-source',
    store: faker.helpers.arrayElement([
      'google_play',
      'apple_app_store',
      'amazon_store',
      'huawei_store',
    ]),
    generatePayload: () => ({
      paywall_id: 'default_light_qa',
      placement_id: 'home_subscription_button',
      trigger_action: 'app_open',
      plans: [
        {
          id: 'flipaclip_599_1m_7d0',
          offers_free_trial: true,
          period: 'MONTH',
        },
        {
          id: 'flipaclip_2999_1y_7d0',
          offers_free_trial: true,
          period: 'YEAR',
        },
      ],
      ab_test_id: faker.helpers.arrayElement(['test_001', null]),
      ab_test_variant: faker.helpers.arrayElement(['variant_A', null]),
    }),
  },
  {
    event_name: 'subscription_offer_aborted',
    provider: 'iron-source',
    store: faker.helpers.arrayElement([
      'google_play',
      'apple_app_store',
      'amazon_store',
      'huawei_store',
    ]),
    generatePayload: () => ({
      paywall_id: 'default_light_qa',
      placement_id: 'home_subscription_button',
      trigger_action: 'app_open',
      plans: [
        {
          id: 'flipaclip_599_1m_7d0',
          offers_free_trial: true,
          period: 'MONTH',
        },
        {
          id: 'flipaclip_2999_1y_7d0',
          offers_free_trial: true,
          period: 'YEAR',
        },
      ],
      offer_selected: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_2999_1y_7d0']),
      abort_reason: faker.helpers.arrayElement(['User Cancelled', 'Payment Failed']),
    }),
  },
  {
    event_name: 'TRANSACTION_PROCESSED',
    provider: 'purchasely',
    generatePayload: () => ({
      plan: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_999_3m_7d0']),
      store: faker.helpers.arrayElement(['GOOGLE_PLAY_STORE', 'APPLE_APP_STORE']),
      product: faker.helpers.arrayElement(['flipaclip_plus_7d0', 'flipaclip_premium_14d0']),
      user_id: faker.string.uuid(),
      event_id: faker.string.uuid(),
      event_name: 'TRANSACTION_PROCESSED',
      offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      api_version: 3,
      device_type: faker.helpers.arrayElement(['PHONE', 'TABLET']),
      environment: faker.helpers.arrayElement(['SANDBOX', 'PRODUCTION']),
      purchased_at: faker.date.past().toISOString(),
      amount_in_usd: faker.finance.amount(),
      purchase_type: faker.helpers.arrayElement(['RENEWING_SUBSCRIPTION', 'ONE_TIME_PURCHASE']),
      store_country: faker.location.countryCode(),
      next_renewal_at: faker.date.future().toISOString(),
      purchased_at_ms: faker.date.past().getTime(),
      event_created_at: faker.date.recent().toISOString(),
      is_family_shared: faker.datatype.boolean(),
      store_product_id: faker.commerce.product(),
      customer_currency: faker.finance.currencyCode(),
      plan_price_in_usd: faker.finance.amount(),
      source_event_name: faker.helpers.arrayElement(['SUBSCRIPTION_RENEWED', 'NEW_SUBSCRIPTION']),
      next_renewal_at_ms: faker.date.future().getTime(),
      event_created_at_ms: faker.date.recent().getTime(),
      previous_offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      store_app_bundle_id: faker.string.uuid(),
      subscription_status: faker.helpers.arrayElement(['AUTO_RENEWING', 'CANCELLED']),
      store_transaction_id: faker.string.uuid(),
      original_purchased_at: faker.date.past().toISOString(),
      original_purchased_at_ms: faker.date.past().getTime(),
      cumulated_revenues_in_usd: faker.finance.amount(),
      effective_next_renewal_at: faker.date.future().toISOString(),
      purchasely_subscription_id: faker.string.uuid(),
      amount_in_customer_currency: faker.finance.amount(),
      effective_next_renewal_at_ms: faker.date.future().getTime(),
      store_original_transaction_id: faker.string.uuid(),
      plan_price_in_customer_currency: faker.finance.amount(),
    }),
  },
  {
    event_name: 'SUBSCRIPTION_STARTED',
    provider: 'purchasely',
    generatePayload: () => ({
      plan: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_999_3m_7d0']),
      store: faker.helpers.arrayElement(['GOOGLE_PLAY_STORE', 'APPLE_APP_STORE']),
      product: faker.helpers.arrayElement(['flipaclip_plus_7d0', 'flipaclip_premium_14d0']),
      event_id: faker.string.uuid(),
      event_name: 'SUBSCRIPTION_STARTED',
      offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      api_version: 3,
      device_type: faker.helpers.arrayElement(['PHONE', 'TABLET']),
      environment: faker.helpers.arrayElement(['SANDBOX', 'PRODUCTION']),
      purchased_at: faker.date.past().toISOString(),
      purchase_type: faker.helpers.arrayElement(['RENEWING_SUBSCRIPTION', 'ONE_TIME_PURCHASE']),
      store_country: faker.location.countryCode(),
      next_renewal_at: faker.date.future().toISOString(),
      purchased_at_ms: faker.date.past().getTime(),
      event_created_at: faker.date.recent().toISOString(),
      is_family_shared: faker.datatype.boolean(),
      store_product_id: faker.commerce.product(),
      anonymous_user_id: faker.string.uuid(),
      customer_currency: faker.finance.currencyCode(),
      plan_price_in_usd: faker.finance.amount(),
      next_renewal_at_ms: faker.date.future().getTime(),
      event_created_at_ms: faker.date.recent().getTime(),
      store_app_bundle_id: faker.string.uuid(),
      subscription_status: faker.helpers.arrayElement(['AUTO_RENEWING', 'CANCELLED']),
      store_transaction_id: faker.string.uuid(),
      original_purchased_at: faker.date.past().toISOString(),
      original_purchased_at_ms: faker.date.past().getTime(),
      cumulated_revenues_in_usd: faker.finance.amount(),
      effective_next_renewal_at: faker.date.future().toISOString(),
      purchasely_subscription_id: faker.string.uuid(),
      effective_next_renewal_at_ms: faker.date.future().getTime(),
      store_original_transaction_id: faker.string.uuid(),
      plan_price_in_customer_currency: faker.finance.amount(),
    }),
  },
  {
    event_name: 'SUBSCRIPTION_RENEWED',
    provider: 'purchasely',
    generatePayload: () => ({
      plan: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_999_3m_7d0']),
      store: faker.helpers.arrayElement(['GOOGLE_PLAY_STORE', 'APPLE_APP_STORE']),
      product: faker.helpers.arrayElement(['flipaclip_plus_7d0', 'flipaclip_premium_14d0']),
      user_id: faker.string.uuid(),
      event_id: faker.string.uuid(),
      event_name: 'SUBSCRIPTION_RENEWED',
      offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      api_version: 3,
      device_type: faker.helpers.arrayElement(['PHONE', 'TABLET']),
      environment: faker.helpers.arrayElement(['SANDBOX', 'PRODUCTION']),
      purchased_at: faker.date.past().toISOString(),
      purchase_type: faker.helpers.arrayElement(['RENEWING_SUBSCRIPTION', 'ONE_TIME_PURCHASE']),
      store_country: faker.location.countryCode(),
      next_renewal_at: faker.date.future().toISOString(),
      purchased_at_ms: faker.date.past().getTime(),
      event_created_at: faker.date.recent().toISOString(),
      is_family_shared: faker.datatype.boolean(),
      store_product_id: faker.commerce.product(),
      customer_currency: faker.finance.currencyCode(),
      plan_price_in_usd: faker.finance.amount(),
      next_renewal_at_ms: faker.date.future().getTime(),
      event_created_at_ms: faker.date.recent().getTime(),
      previous_offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      store_app_bundle_id: faker.string.uuid(),
      subscription_status: faker.helpers.arrayElement(['AUTO_RENEWING', 'CANCELLED']),
      store_transaction_id: faker.string.uuid(),
      original_purchased_at: faker.date.past().toISOString(),
      original_purchased_at_ms: faker.date.past().getTime(),
      cumulated_revenues_in_usd: faker.finance.amount(),
      effective_next_renewal_at: faker.date.future().toISOString(),
      purchasely_subscription_id: faker.string.uuid(),
      effective_next_renewal_at_ms: faker.date.future().getTime(),
      store_original_transaction_id: faker.string.uuid(),
      plan_price_in_customer_currency: faker.finance.amount(),
    }),
  },
  {
    event_name: 'SUBSCRIPTION_TERMINATED',
    provider: 'purchasely',
    generatePayload: () => ({
      plan: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_999_3m_7d0']),
      store: faker.helpers.arrayElement(['GOOGLE_PLAY_STORE', 'APPLE_APP_STORE']),
      product: faker.helpers.arrayElement(['flipaclip_plus_7d0', 'flipaclip_premium_14d0']),
      user_id: faker.string.uuid(),
      event_id: faker.string.uuid(),
      event_name: 'SUBSCRIPTION_TERMINATED',
      offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      api_version: 3,
      device_type: faker.helpers.arrayElement(['PHONE', 'TABLET']),
      environment: faker.helpers.arrayElement(['SANDBOX', 'PRODUCTION']),
      purchased_at: faker.date.past().toISOString(),
      purchase_type: faker.helpers.arrayElement(['RENEWING_SUBSCRIPTION', 'ONE_TIME_PURCHASE']),
      store_country: faker.location.countryCode(),
      next_renewal_at: faker.date.future().toISOString(),
      purchased_at_ms: faker.date.past().getTime(),
      event_created_at: faker.date.recent().toISOString(),
      is_family_shared: faker.datatype.boolean(),
      store_product_id: faker.commerce.product(),
      customer_currency: faker.finance.currencyCode(),
      plan_price_in_usd: faker.finance.amount(),
      next_renewal_at_ms: faker.date.future().getTime(),
      event_created_at_ms: faker.date.recent().getTime(),
      previous_offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      store_app_bundle_id: faker.string.uuid(),
      subscription_status: faker.helpers.arrayElement(['UNPAID', 'CANCELLED']),
      store_transaction_id: faker.string.uuid(),
      original_purchased_at: faker.date.past().toISOString(),
      original_purchased_at_ms: faker.date.past().getTime(),
      cumulated_revenues_in_usd: faker.finance.amount(),
      effective_next_renewal_at: faker.date.future().toISOString(),
      purchasely_subscription_id: faker.string.uuid(),
      effective_next_renewal_at_ms: faker.date.future().getTime(),
      store_original_transaction_id: faker.string.uuid(),
      plan_price_in_customer_currency: faker.finance.amount(),
    }),
  },
  {
    event_name: 'TRIAL_NOT_CONVERTED',
    provider: 'purchasely',
    generatePayload: () => ({
      plan: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_999_3m_7d0']),
      store: faker.helpers.arrayElement(['APPLE_APP_STORE', 'GOOGLE_PLAY_STORE']),
      product: faker.helpers.arrayElement(['flipaclip_plus_7d0', 'flipaclip_premium_14d0']),
      event_id: faker.string.uuid(),
      placement: faker.helpers.arrayElement(['feature_import_video', 'feature_drawing_tool']),
      event_name: 'TRIAL_NOT_CONVERTED',
      offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      api_version: 3,
      device_type: faker.helpers.arrayElement(['PHONE', 'PAD']),
      environment: faker.helpers.arrayElement(['PRODUCTION', 'SANDBOX']),
      presentation: faker.helpers.arrayElement(['Default-072324', 'Special-Promo-080123']),
      purchased_at: faker.date.past().toISOString(),
      purchase_type: faker.helpers.arrayElement(['RENEWING_SUBSCRIPTION', 'ONE_TIME_PURCHASE']),
      store_country: faker.location.countryCode(),
      next_renewal_at: faker.date.future().toISOString(),
      purchased_at_ms: faker.date.past().getTime(),
      event_created_at: faker.date.recent().toISOString(),
      is_family_shared: faker.datatype.boolean(),
      store_product_id: faker.commerce.product(),
      anonymous_user_id: faker.string.uuid(),
      customer_currency: faker.finance.currencyCode(),
      plan_price_in_usd: faker.finance.amount(),
      source_event_name: faker.helpers.arrayElement([
        'SUBSCRIPTION_TERMINATED',
        'SUBSCRIPTION_RENEWED',
      ]),
      next_renewal_at_ms: faker.date.future().getTime(),
      event_created_at_ms: faker.date.recent().getTime(),
      previous_offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      store_app_bundle_id: faker.system.semver(),
      subscription_status: faker.helpers.arrayElement(['DEACTIVATED', 'EXPIRED']),
      store_transaction_id: faker.string.numeric(18),
      original_purchased_at: faker.date.past().toISOString(),
      original_purchased_at_ms: faker.date.past().getTime(),
      cumulated_revenues_in_usd: faker.finance.amount(),
      effective_next_renewal_at: faker.date.future().toISOString(),
      purchasely_subscription_id: faker.string.uuid(),
      effective_next_renewal_at_ms: faker.date.future().getTime(),
      store_original_transaction_id: faker.string.numeric(18),
      plan_price_in_customer_currency: faker.finance.amount(),
    }),
  },
  {
    event_name: 'TRIAL_CONVERTED',
    provider: 'purchasely',
    generatePayload: () => ({
      plan: faker.helpers.arrayElement(['flipaclip_599_1m_7d0', 'flipaclip_999_3m_7d0']),
      store: faker.helpers.arrayElement(['APPLE_APP_STORE', 'GOOGLE_PLAY_STORE']),
      product: faker.helpers.arrayElement(['flipaclip_plus_7d0', 'flipaclip_premium_14d0']),
      event_id: faker.string.uuid(),
      placement: faker.helpers.arrayElement(['house_ad', 'featured_promo']),
      event_name: 'TRIAL_CONVERTED',
      offer_type: faker.helpers.arrayElement(['NONE', 'DISCOUNTED_TRIAL']),
      api_version: 3,
      device_type: faker.helpers.arrayElement(['PHONE', 'PAD']),
      environment: faker.helpers.arrayElement(['PRODUCTION', 'SANDBOX']),
      presentation: faker.helpers.arrayElement(['default_light_121823', 'custom_promo_080123']),
      purchased_at: faker.date.past().toISOString(),
      purchase_type: faker.helpers.arrayElement(['RENEWING_SUBSCRIPTION', 'ONE_TIME_PURCHASE']),
      store_country: faker.location.countryCode(),
      next_renewal_at: faker.date.future().toISOString(),
      purchased_at_ms: faker.date.past().getTime(),
      event_created_at: faker.date.recent().toISOString(),
      is_family_shared: faker.datatype.boolean(),
      store_product_id: faker.commerce.product(),
      anonymous_user_id: faker.string.uuid(),
      customer_currency: faker.finance.currencyCode(),
      plan_price_in_usd: faker.finance.amount(),
      source_event_name: 'TRIAL_CONVERTED',
      next_renewal_at_ms: faker.date.future().getTime(),
      event_created_at_ms: faker.date.recent().getTime(),
      previous_offer_type: faker.helpers.arrayElement(['FREE_TRIAL', 'DISCOUNTED_TRIAL']),
      store_app_bundle_id: faker.system.semver(),
      subscription_status: faker.helpers.arrayElement(['AUTO_RENEWING', 'CANCELLED']),
      store_transaction_id: faker.string.numeric(18),
      original_purchased_at: faker.date.past().toISOString(),
      original_purchased_at_ms: faker.date.past().getTime(),
      cumulated_revenues_in_usd: faker.finance.amount(),
      effective_next_renewal_at: faker.date.future().toISOString(),
      purchasely_subscription_id: faker.string.uuid(),
      effective_next_renewal_at_ms: faker.date.future().getTime(),
      store_original_transaction_id: faker.string.numeric(18),
      plan_price_in_customer_currency: faker.finance.amount(),
    }),
  },
];

export async function seedWebhooks(dataSource: DataSource, maxEvents = 10) {
  console.log('🚀 Seeding Webhook Events...');

  const userRepository = dataSource.getRepository(User);
  const webhookRepository = dataSource.getRepository(Webhook);

  const users = await userRepository.find();
  if (users.length === 0) {
    console.log('⚠️ No users found! Run the user seeder first.');
    return;
  }

  const webhooks: Webhook[] = [];
  const modifiedUsers: User[] = [];
  let totalEvents = 0;

  for (const user of users) {
    if (totalEvents >= maxEvents) break;

    // Each user gets between 1 and 5 random events
    const eventCount = Math.min(faker.number.int({ min: 1, max: 5 }), maxEvents - totalEvents);

    for (let i = 0; i < eventCount; i++) {
      if (totalEvents >= maxEvents) break;

      const randomEvent = faker.helpers.arrayElement(webhookEventTypes);
      const webhook = new Webhook();

      webhook.fcid = user.fcid;
      webhook.eventName = randomEvent.event_name;
      webhook.provider = randomEvent.provider;
      webhook.payload = randomEvent.generatePayload();

      // Add store field for iron-source and paywall events
      if (webhook.provider === 'iron-source' || webhook.provider === 'flipaclip') {
        webhook.store = faker.helpers.arrayElement([
          'google_play',
          'apple_app_store',
          'amazon_store',
          'huawei_store',
        ]);
      }

      if (webhook.provider === 'purchasely') {
        webhook.eventName = eventNameMapping[randomEvent.event_name] || randomEvent.event_name;
        webhook.payload = renamePayloadKeys(webhook.payload, webhook.eventName);
        const additionalProps = getAdditionalProperties(webhook.payload as PurchaselyEventPayload);
        webhook.payload = { ...webhook.payload, ...additionalProps };

        user.properties.totalSubscriptionRevenue += webhook.payload?.amount_in_usd || 0;
        user.properties.subscriptionType = webhook.payload?.plan;
        user.properties.subscriptionState = webhook.payload?.subscription_status;
        user.properties.totalRevenue =
          user.properties.totalAdRevenue + user.properties.totalSubscriptionRevenue;
        user.properties.purchasely_subscription_id = webhook.payload?.purchasely_subscription_id;
      }

      webhook.eventTimestamp = faker.date.recent({ days: 1 }).toISOString(); // Store timestamp as human-readable format
      webhook.modified_properties = {};

      webhooks.push(webhook);
      modifiedUsers.push(user);
      totalEvents++;
    }
  }

  await webhookRepository.save(webhooks);
  await userRepository.save(modifiedUsers);
  console.log(`✅ Successfully seeded ${webhooks.length} webhook events!`);
}
