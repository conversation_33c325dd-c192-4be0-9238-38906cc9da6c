import { Schema } from 'jsonschema';

type ComparisonOperator = '$eq' | '$ne' | '$gt' | '$gte' | '$lt' | '$lte' | '$in' | '$nin';

type ComparisonQuery<T> = {
  [K in keyof T]?:
    | T[K]
    | {
        [Op in ComparisonOperator]?: T[K] | T[K][];
      };
};

interface Variant {
  distribution: number;
  exclude_from_future: boolean;
}

interface ActivationCondition {
  name: string;
  event_properties: Record<string, ComparisonQuery<string>>;
  is_new_user: boolean;
}

type UserProp = string | number | ComparisonQuery<string>;
type UserCondition = Record<string, UserProp>;

interface Conditions {
  activation_event: ActivationCondition;
  user_properties: UserCondition;
}

export interface ABTest {
  test_name: string;
  enabled: boolean;
  exclude_from_future: boolean;
  variants: Record<string, Variant>;
  conditions: Conditions;
}

export const abTestSchema: Schema = {
  id: '/abTest',
  type: 'object',
  required: ['test_name'],
  properties: {
    test_name: { type: 'string' },
    enabled: { type: 'boolean' },
    exclude_from_future: { type: 'boolean' },
    variants: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        required: ['distribution', 'exclude_from_future'],
        properties: {
          distribution: { type: 'number' },
          exclude_from_future: { type: 'boolean ' },
        },
      },
    },
    conditions: {
      type: 'object',
      properties: {
        activation_event: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            event_properties: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                additionalProperties: true,
              },
            },
            is_new_user: { type: 'boolean' },
          },
        },
        user_properties: {
          type: 'object',
          additionalProperties: {
            oneOf: [
              { type: ['string', 'number'] },
              {
                type: 'object',
                properties: {
                  $eq: {},
                  $ne: {},
                  $gt: {},
                  $gte: {},
                  $lt: {},
                  $lte: {},
                  $in: {
                    type: 'array',
                  },
                  $nin: {
                    type: 'array',
                  },
                },
                additionalProperties: false,
              },
            ],
          },
        },
      },
    },
  },
};
