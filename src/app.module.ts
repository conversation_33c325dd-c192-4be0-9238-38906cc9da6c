import { CacheModule } from '@nestjs/cache-manager';
import { <PERSON><PERSON><PERSON>, Logger, DynamicModule, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TerminusModule } from '@nestjs/terminus';
import { ThrottlerModule } from '@nestjs/throttler';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule } from './auth/auth.module';
import { ConfigModule as CustomConfigModule } from './config/config.module';
import { HealthController } from './health/health.controller';
import { HealthModule } from './health/health.module';
import { UsersController } from './users/users.controller';
import { UsersModule } from './users/users.module';
import { WebhooksController } from './webhooks/webhooks.controller';
import { WebhooksModule } from './webhooks/webhooks.module';
import { KwsModule } from './kws/kws.module';
import { FirebaseService } from './auth/firebase.service';
import { TokenValidationGuard } from './guards/token-validation.guard';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { GeolocationModule } from './geolocation/geolocation.module';
import configuration from './config/configuration';
import { Config } from './config/interfaces/config.interface';
import { getTypeOrmConfig } from './config/typeorm.config';
import { RedisModule } from './common/redis.module';
import { SlackOrJwtAuthGuard } from './guards/slack-or-jwt.guard';
import { JwtAuthGuard } from './guards/auth.guard';
import { LoggerModule } from './common/logger.module';
import { UserAgentMiddleware } from './common/middleware/user-agent.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      cache: true,
    }),
    ScheduleModule.forRoot(),
    LoggerModule,
    CacheModule.register({
      store: 'memory',
      isGlobal: true,
      ttl: 300, // 5 minutes cache TTL - reduced to handle more frequent updates
      max: 5000, // increased to handle more concurrent users
    }),
    CustomConfigModule,
    CacheModule.registerAsync({
      isGlobal: true,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService<Config>) => ({
        ttl: configService.get('cache').ttl,
        max: configService.get('cache').max,
      }),
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService<Config>) => ({
        ...getTypeOrmConfig(configService),
        autoLoadEntities: true,
      }),
    }),
    RedisModule,
    UsersModule,
    AuthModule,
    WebhooksModule,
    KwsModule,
    TerminusModule,
    HealthModule,
    GeolocationModule,
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService<Config>) => {
        const throttlerConfig = configService.get('throttler');
        return {
          throttlers: [
            {
              ttl: throttlerConfig.auth.default.ttl,
              limit: throttlerConfig.auth.default.limit,
            },
            {
              ttl: throttlerConfig.users.default.ttl,
              limit: throttlerConfig.users.default.limit,
            },
            {
              ttl: throttlerConfig.webhooks.default.ttl,
              limit: throttlerConfig.webhooks.default.limit,
            },
            {
              ttl: throttlerConfig.webhooksBatch.default.ttl,
              limit: throttlerConfig.webhooksBatch.default.limit,
            },
            {
              ttl: throttlerConfig.health.default.ttl,
              limit: throttlerConfig.health.default.limit,
            },
          ],
        };
      },
    }),
  ],
  controllers: [UsersController, WebhooksController, HealthController],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    FirebaseService,
    TokenValidationGuard,
    JwtAuthGuard,
    SlackOrJwtAuthGuard,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(UserAgentMiddleware).exclude('/health').forRoutes('*');
  }
}
