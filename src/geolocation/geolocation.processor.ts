import { Process, Processor, OnQueueError, OnQueueFailed } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { GeolocationService } from './geolocation.service';
import { ErrorLoggerService } from '../common/services/error-logger.service';

interface GeolocationJob {
  fcid: string;
  ip: string;
}

@Processor('geolocation')
export class GeolocationProcessor {
  private readonly logger = new Logger(GeolocationProcessor.name);

  constructor(
    private readonly geolocationService: GeolocationService,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  @Process('process-geolocation')
  async processGeolocation(job: Job<GeolocationJob>): Promise<void> {
    this.logger.debug(
      `Processing geolocation job ${job.id} for user ${job.data.fcid} with IP ${job.data.ip}`,
    );

    try {
      await this.geolocationService.processGeolocation(job.data);
      this.logger.debug(`Successfully processed geolocation job ${job.id}`);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Geolocation processor error',
        context: 'GeolocationProcessor',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId: job.id },
      });

      // For certain errors, we might want to handle them differently
      if (
        error.message.includes('Invalid response format') ||
        error.message.includes('Empty response') ||
        error.message.includes('KWS API')
      ) {
        // For KWS API errors, try to update the user with a default country
        try {
          this.logger.debug(
            `Attempting to update user ${job.data.fcid} with default country due to API error`,
          );
          await this.geolocationService.processGeolocationWithFallback(job.data);
          this.logger.debug(`Successfully processed geolocation job ${job.id} with fallback`);
          return; // Don't throw error if fallback succeeds
        } catch (fallbackError) {
          this.errorLogger.logError(fallbackError, undefined, {
            errorName: 'Geolocation fallback error',
            context: 'GeolocationProcessor',
            includeStack: true,
            includeRequest: false,
            metadata: { jobId: job.id },
          });
        }
      }

      throw error; // Let Bull handle retries for other errors or if fallback fails
    }
  }

  @OnQueueError()
  onError(error: Error) {
    this.errorLogger.logError(error, undefined, {
      errorName: 'Geolocation queue error',
      context: 'GeolocationProcessor',
      includeStack: true,
      includeRequest: false,
    });
  }

  @OnQueueFailed()
  async onFailed(job: Job<GeolocationJob>, error: Error) {
    this.errorLogger.logError(error, undefined, {
      errorName: 'Geolocation job failed',
      context: 'GeolocationProcessor',
      includeStack: true,
      includeRequest: false,
      metadata: { jobId: job.id, attemptsMade: job.attemptsMade, attempts: job.opts.attempts },
    });

    // Only move to DLQ if max attempts reached
    if (job.attemptsMade >= (job.opts.attempts || 3)) {
      try {
        await this.geolocationService.addToDlq(job.data, error);
        this.logger.log(
          `Moved failed geolocation job ${job.id} to DLQ after ${job.attemptsMade} attempts`,
        );
      } catch (dlqError) {
        this.errorLogger.logError(dlqError, undefined, {
          errorName: 'Failed to move geolocation job to DLQ',
          context: 'GeolocationProcessor',
          includeStack: true,
          includeRequest: false,
          metadata: { jobId: job.id },
        });
      }
    }
  }
}

@Processor('geolocation-dlq')
export class GeolocationDlqProcessor {
  private readonly logger = new Logger(GeolocationDlqProcessor.name);

  @Process('process-dlq')
  async processDlqJob(job: Job) {
    this.logger.debug(`Received job ${job.id} in geolocation DLQ`);
    // Just log the job, no processing needed for DLQ
    return { processed: true };
  }

  @OnQueueError()
  onError(error: Error) {
    this.logger.error(`Geolocation DLQ queue error: ${error.message}`, error.stack);
  }
}
