import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';

import { Config } from '../config/interfaces/config.interface';
import { KwsService } from '../kws/kws.service';
import { KwsAgeGateResponse } from '../kws/interfaces/kws-response.interface';
import { getCountryNameFromCode } from '../common/utils/country-utils';
import { PostgresUserService } from '../common/services/postgres-user.service';
import { ErrorLoggerService } from '../common/services/error-logger.service';

interface GeolocationJob {
  fcid: string;
  ip: string;
}

@Injectable()
export class GeolocationService {
  private readonly logger = new Logger(GeolocationService.name);
  private readonly fallbackIp: string;
  private readonly skipLocalIps: boolean;

  constructor(
    @InjectQueue('geolocation') private readonly geoQueue: Queue<GeolocationJob>,
    @InjectQueue('geolocation-dlq') private readonly dlqQueue: Queue<GeolocationJob>,
    private readonly postgresUserService: PostgresUserService,
    private readonly configService: ConfigService<Config>,
    private readonly kwsService: KwsService,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    const geoConfig = this.configService.get('geolocation');
    this.fallbackIp = geoConfig.fallbackIp;
    this.skipLocalIps = geoConfig.skipLocalIps;
  }

  /**
   * Queue a geolocation job for a newly created user
   * @param fcid User's fcid
   * @param ip User's IP address
   */
  async queueGeolocation(fcid: string, ip: string): Promise<void> {
    let processedIp: string | null = null;
    try {
      // Check if the IP is a local/private IP address
      processedIp = this.processIpAddress(ip);
      if (!processedIp) {
        this.logger.debug(`Skipping geolocation for local IP address: ${ip}`);
        return;
      }

      const redisConfig = this.configService.get('redis');
      await this.geoQueue.add(
        'process-geolocation',
        { fcid, ip: processedIp },
        redisConfig.queue.defaultJobOptions,
      );
      this.logger.debug(`Queued geolocation job for user ${fcid} with IP ${processedIp}`);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        context: 'GeolocationService.queueGeolocation',
        fcid,
        includeStack: true,
        includeRequest: false,
        metadata: { ip: processedIp || ip },
      });
    }
  }

  /**
   * Process an IP address to handle local/private IPs
   * @param ip The original IP address
   * @returns Processed IP address or null if it should be skipped
   */
  private processIpAddress(ip: string): string | null {
    // Check if it's a local/private IP
    if (this.isLocalIp(ip)) {
      // If we should skip local IPs and not use fallback, return null
      if (this.skipLocalIps && !this.fallbackIp) {
        return null;
      }

      // If we have a fallback IP, use it
      if (this.fallbackIp) {
        this.logger.debug(`Using fallback IP ${this.fallbackIp} instead of local IP ${ip}`);
        return this.fallbackIp;
      }
    }

    return ip;
  }

  /**
   * Check if an IP address is a local/private IP
   * @param ip IP address to check
   * @returns True if it's a local IP
   */
  private isLocalIp(ip: string): boolean {
    // IPv4 local patterns
    const ipv4Patterns = [
      /^127\./, // *********/8
      /^10\./, // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./, // **********/12
      /^192\.168\./, // ***********/16
      /^169\.254\./, // ***********/16 (link-local)
      /^::1$/, // IPv6 localhost
      /^[fF][cCdD]/, // IPv6 unique local addresses
      /^::ffff:127\./, // IPv4-mapped IPv6 for *********/8
      /^::ffff:10\./, // IPv4-mapped IPv6 for 10.0.0.0/8
      /^::ffff:172\.(1[6-9]|2[0-9]|3[0-1])\./, // IPv4-mapped IPv6 for **********/12
      /^::ffff:192\.168\./, // IPv4-mapped IPv6 for ***********/16
    ];

    return ipv4Patterns.some(pattern => pattern.test(ip));
  }

  /**
   * Process a geolocation job by calling the IP geolocation API
   * @param job Geolocation job data
   */
  async processGeolocation(job: GeolocationJob): Promise<void> {
    try {
      this.logger.debug(`Processing geolocation for user ${job.fcid} with IP ${job.ip}`);

      // Call the IP geolocation API
      const country = await this.getCountryFromIp(job.ip);

      // Update the user record with the country
      await this.updateUserCountry(job.fcid, country);

      this.logger.debug(`Updated user ${job.fcid} with country: ${country}`);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        context: 'GeolocationService.processGeolocation',
        fcid: job.fcid,
        includeStack: true,
        includeRequest: false,
        metadata: { ip: job.ip },
      });
      throw error; // Let Bull handle retries
    }
  }

  /**
   * Process a geolocation job with fallback country when API fails
   * @param job Geolocation job data
   */
  async processGeolocationWithFallback(job: GeolocationJob): Promise<void> {
    try {
      this.logger.debug(`Processing geolocation with fallback for user ${job.fcid}`);

      // Use a default country
      const defaultCountry = 'Unknown';

      // Update the user record with the default country
      await this.updateUserCountry(job.fcid, defaultCountry);

      this.logger.debug(`Updated user ${job.fcid} with fallback country: ${defaultCountry}`);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        context: 'GeolocationService.processGeolocationWithFallback',
        fcid: job.fcid,
        includeStack: true,
        includeRequest: false,
        metadata: { ip: job.ip },
      });
      throw error;
    }
  }

  /**
   * Call the KWS service to get the country for an IP address
   * @param ip IP address to lookup
   * @returns Country name
   */
  private async getCountryFromIp(ip: string): Promise<string> {
    try {
      this.logger.debug(`Getting country for IP: ${ip}`);

      // Use the KWS service to get country information
      const ageGateData: KwsAgeGateResponse = await this.kwsService.getAgeGateData({
        ip: ip,
      });

      this.logger.debug(`KWS response for IP ${ip}: ${JSON.stringify(ageGateData)}`);

      if (!ageGateData) {
        throw new Error('Empty response from KWS API');
      }

      if (!ageGateData.country) {
        // If no country is returned, use a default for testing
        this.logger.debug(`No country found for IP ${ip}, using default country`);
        return 'Unknown';
      }

      // Convert country code to country name if needed
      // KWS returns country codes (e.g., "US"), but we want full country names
      const countryName = getCountryNameFromCode(ageGateData.country);
      this.logger.debug(`Converted country code ${ageGateData.country} to ${countryName}`);

      return countryName;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        context: 'GeolocationService.getCountryFromIp',
        includeStack: true,
        includeRequest: false,
        metadata: { ip },
      });

      // For certain errors, we might want to use a default country instead of failing
      if (
        error.message &&
        (error.message.includes('Invalid response format') ||
          error.message.includes('Empty response'))
      ) {
        this.logger.debug(`Using default country for IP ${ip} due to API error`);
        return 'Unknown';
      }

      throw error;
    }
  }

  /**
   * Update a user's country in the database
   * @param fcid User's fcid
   * @param country Country name
   */
  private async updateUserCountry(fcid: string, country: string): Promise<void> {
    try {
      // Find the user first to get current properties
      const user = await this.postgresUserService.findByAttribute('fcid', fcid);
      if (!user) {
        throw new Error(`User with fcid ${fcid} not found`);
      }

      // Update the properties with the new country
      const updatedProperties = {
        ...user.properties,
        countryKws: country,
      };

      // Use PostgresUserService to update the user, which will handle cache invalidation
      const updatedUser = {
        ...user,
        properties: updatedProperties,
      };

      await this.postgresUserService.updateUser(fcid, updatedUser);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        context: 'GeolocationService.updateUserCountry',
        fcid,
        includeStack: true,
        includeRequest: false,
        metadata: { country },
      });
      throw error;
    }
  }

  /**
   * Add a failed job to the DLQ
   * @param job Failed geolocation job
   * @param error Error that caused the failure
   */
  async addToDlq(job: GeolocationJob, error: Error): Promise<void> {
    try {
      const dlqData = {
        ...job,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        timestamp: new Date().toISOString(),
      };

      await this.dlqQueue.add('process-dlq', dlqData, {
        jobId: `dlq-geo-${job.fcid}-${Date.now()}`,
      });

      this.logger.debug(`Added geolocation job for user ${job.fcid} to DLQ`);
    } catch (dlqError) {
      this.errorLogger.logError(dlqError, undefined, {
        context: 'GeolocationService.addToDlq',
        fcid: job.fcid,
        includeStack: true,
        includeRequest: false,
        metadata: { originalError: error.message, ip: job.ip },
      });
    }
  }
}
