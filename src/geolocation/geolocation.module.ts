import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GeolocationService } from './geolocation.service';
import { GeolocationProcessor } from './geolocation.processor';
import { redisConfig } from '../config/redis.config';
import { KwsModule } from '../kws/kws.module';
import { PostgresUserService } from '../common/services/postgres-user.service';
import { RedisModule } from '../common/redis.module';
import { DeviceId } from '../users/entities/device-id.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([DeviceId, User]),
    KwsModule,
    RedisModule,
    BullModule.registerQueue({
      name: 'geolocation',
      defaultJobOptions: {
        ...redisConfig.queue.defaultJobOptions,
        removeOnComplete: true,
        removeOnFail: false,
      },
    }),
    BullModule.registerQueue({
      name: 'geolocation-dlq',
      defaultJobOptions: {
        removeOnComplete: false,
        removeOnFail: false,
      },
    }),
  ],
  providers: [GeolocationService, GeolocationProcessor, PostgresUserService],
  exports: [GeolocationService],
})
export class GeolocationModule {}
