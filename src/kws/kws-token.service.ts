import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { ErrorLoggerService } from '../common/services/error-logger.service';

interface TokenCache {
  token: string;
  expiryTime: number;
}

@Injectable()
export class KwsTokenService {
  private readonly logger = new Logger(KwsTokenService.name);
  private tokenCache: Record<string, TokenCache> = {};

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  async getToken(scope = 'verification settings family consent'): Promise<string> {
    try {
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if the token for the requested scope is still valid
      if (this.tokenCache[scope] && this.tokenCache[scope].expiryTime > currentTime) {
        return this.tokenCache[scope].token;
      }

      const productClientId = this.configService.get<string>('kws.productClientId');
      const apiKey = this.configService.get<string>('kws.apiKey');

      if (!productClientId || !apiKey) {
        throw new Error('KWS credentials not configured');
      }

      const credentials = `${productClientId}:${apiKey}`;
      const clientCredentials = Buffer.from(credentials).toString('base64');

      const authUrl = this.configService.get<string>('kws.authUrl');
      if (!authUrl) {
        throw new Error('KWS auth URL not configured');
      }

      const response = await firstValueFrom(
        this.httpService.post(
          authUrl,
          new URLSearchParams({
            grant_type: 'client_credentials',
            scope: scope,
          }).toString(),
          {
            headers: {
              Authorization: `Basic ${clientCredentials}`,
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        ),
      );

      const { access_token, expires_in } = response.data;

      if (!access_token) {
        throw new Error('Failed to retrieve access token');
      }

      // Cache the token and its expiry time
      this.tokenCache[scope] = {
        token: access_token,
        expiryTime: currentTime + expires_in - 60, // Subtract 60 seconds as a buffer
      };

      return access_token;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error fetching KWS access token',
        context: 'KwsTokenService',
        includeStack: true,
        includeRequest: false,
        metadata: { scope },
      });
      throw error;
    }
  }
}
