import { Module } from '@nestjs/common';
import { KwsController } from './kws.controller';
import { KwsService } from './kws.service';
import { KwsTokenService } from './kws-token.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [HttpModule],
  controllers: [KwsController],
  providers: [KwsService, KwsTokenService],
  exports: [KwsService, KwsTokenService],
})
export class KwsModule {}
