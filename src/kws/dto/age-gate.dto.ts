import { IsOptional, IsString, IsN<PERSON>ber, IsInt } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class AgeGateQueryDto {
  @IsOptional()
  @IsString()
  ip?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  dob?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsInt()
  @Transform(({ value }: { value: string | undefined }) =>
    value ? parseInt(value, 10) : undefined,
  )
  age?: number;
}
