import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

import { KwsTokenService } from './kws-token.service';
import { AgeGateQueryDto } from './dto/age-gate.dto';
import { KwsAgeGateResponse, KwsAgeGateQueryParams } from './interfaces/kws-response.interface';
import { ErrorLoggerService } from '../common/services/error-logger.service';

@Injectable()
export class KwsService {
  private readonly logger = new Logger(KwsService.name);
  private readonly userAgent = 'Flipaclip Age Gate Client';

  constructor(
    private readonly httpService: HttpService,
    private readonly kwsTokenService: KwsTokenService,
    private readonly configService: ConfigService,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  /**
   * Get age gate data from KWS API
   * This method can be used by other services to verify age
   * @param params Query parameters for age verification
   * @returns Age gate response data
   * @throws Error if the API call fails or response format is invalid
   */
  async getAgeGateData(params: KwsAgeGateQueryParams): Promise<KwsAgeGateResponse> {
    try {
      const token = await this.kwsTokenService.getToken('age-gate');
      const apiUrl = this.configService.get<string>('kws.apiUrl');

      if (!apiUrl) {
        throw new Error('KWS API URL not configured');
      }

      this.logger.debug(`Calling KWS API with params: ${JSON.stringify(params)}`);

      const response = await firstValueFrom(
        this.httpService.get(apiUrl, {
          params,
          headers: {
            Authorization: `Bearer ${token}`,
            'User-Agent': this.userAgent,
          },
        }),
      );

      this.logger.debug(
        `KWS API response structure: ${JSON.stringify(Object.keys(response.data))}`,
      );

      if (response.data && typeof response.data.response === 'object') {
        return response.data.response as KwsAgeGateResponse;
      }

      this.errorLogger.logError(new Error('Unexpected KWS API response format'), undefined, {
        errorName: 'Unexpected KWS API response format',
        context: 'KwsService',
        includeStack: true,
        includeRequest: false,
        metadata: { response: response.data },
      });
      throw new Error('Invalid response format from KWS API');
    } catch (error) {
      if (error.response) {
        this.errorLogger.logError(new Error('KWS API error response'), undefined, {
          errorName: 'KWS API error response',
          context: 'KwsService',
          includeStack: true,
          includeRequest: false,
          metadata: { response: error.response.data },
        });
      }
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error in getAgeGateData',
        context: 'KwsService',
        includeStack: true,
        includeRequest: false,
        metadata: { params },
      });
      // Re-throw the error to maintain the original error message for tests
      throw error;
    }
  }

  /**
   * Get age gate response from KWS API
   * @param queryParams Query parameters for age verification
   * @returns Age gate response with status and data
   * @throws Error if the API call fails
   */
  async getAgeGate(queryParams: AgeGateQueryDto) {
    try {
      const token = await this.kwsTokenService.getToken('age-gate');
      const apiUrl = this.configService.get<string>('kws.apiUrl');

      if (!apiUrl) {
        throw new Error('KWS API URL not configured');
      }

      const response = await firstValueFrom(
        this.httpService.get(apiUrl, {
          params: queryParams,
          headers: {
            Authorization: `Bearer ${token}`,
            'User-Agent': this.userAgent,
          },
        }),
      );

      return {
        status: 200,
        data: response.data,
      };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error in getAgeGate',
        context: 'KwsService',
        includeStack: true,
        includeRequest: false,
        metadata: { queryParams },
      });
      throw new Error('Failed to fetch Age Gate data');
    }
  }
}
