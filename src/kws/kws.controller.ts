import { Controller, Get, Query } from '@nestjs/common';
import { KwsService } from './kws.service';
import { AgeGateQueryDto } from './dto/age-gate.dto';

@Controller('kws')
export class KwsController {
  constructor(private readonly kwsService: KwsService) {}

  @Get('age-gate')
  async getAgeGate(@Query() queryParams: AgeGateQueryDto) {
    const response = await this.kwsService.getAgeGate(queryParams);
    return response.data;
  }
}
