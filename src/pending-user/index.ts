// Main module export
export { PendingUserModule } from './pending-user.module';

// Application layer exports
export { PendingUserService } from './application/services/pending-user.service';

// Domain layer exports
export * from './domain/entities/pending-user.entity';
export * from './domain/repositories/pending-user.repository.interface';
export * from './domain/repositories/user.repository.interface';
export * from './domain/use-cases/find-or-create-user-for-jwt.use-case';
export * from './domain/use-cases/create-pending-user.use-case';

// Infrastructure layer exports
export { PendingUserConfig } from './infrastructure/config/pending-user.config';
export { RedisPendingUserRepository } from './infrastructure/repositories/redis-pending-user.repository';
export { PostgresUserRepository } from './infrastructure/repositories/postgres-user.repository';
