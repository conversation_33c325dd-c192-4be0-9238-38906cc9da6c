/**
 * Custom exceptions for the pending user management system
 */

export class PendingUserError extends Error {
  constructor(message: string, public readonly code?: string) {
    super(message);
    this.name = 'PendingUserError';
  }
}

export class RedisUnavailableError extends PendingUserError {
  constructor(message = 'Redis is unavailable') {
    super(message, 'REDIS_UNAVAILABLE');
    this.name = 'RedisUnavailableError';
  }
}

export class DeviceIdConstraintViolationError extends PendingUserError {
  constructor(deviceId: string) {
    super(
      `Device ID constraint violation: Multiple users found for device ID ${deviceId}`,
      'CONSTRAINT_VIOLATION',
    );
    this.name = 'DeviceIdConstraintViolationError';
  }
}

export class UserCreationInProgressError extends PendingUserError {
  constructor(deviceId: string) {
    super(`User creation already in progress for device ID: ${deviceId}`, 'CREATION_IN_PROGRESS');
    this.name = 'UserCreationInProgressError';
  }
}

export class InvalidInputError extends PendingUserError {
  constructor(message: string) {
    super(message, 'INVALID_INPUT');
    this.name = 'InvalidInputError';
  }
}

export class FcidValidationError extends PendingUserError {
  constructor(fcid: string, deviceId: string) {
    super(`FCID ${fcid} does not contain device ID ${deviceId}`, 'FCID_VALIDATION_FAILED');
    this.name = 'FcidValidationError';
  }
}
