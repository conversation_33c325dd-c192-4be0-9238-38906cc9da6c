import { Injectable, Logger, Inject } from '@nestjs/common';
import { PendingUser, UserForJwt, UserType, DeviceIdInput } from '../entities/pending-user.entity';
import { IPendingUserRepository } from '../repositories/pending-user.repository.interface';
import { IUserRepository } from '../repositories/user.repository.interface';
import { mapPendingUserToUserForJwt } from '../utils/pending-user.mapper';
import { CreatePendingUserUseCase } from './create-pending-user.use-case';
import { DeviceIdConstraintViolationError } from '../exceptions/pending-user.exceptions';

/**
 * Use case for finding or creating a user for JWT token generation.
 *
 * This use case implements the complex business logic for user lookup:
 * 1. If only device ID provided: Look for ANONYMOUS first, then PENDING, then create new PENDING
 * 2. If device ID + FCID provided: Validate FCID exists and has the device ID
 * 3. Enforce the constraint: only one user (ANONYMOUS or PENDING) per device ID
 */
@Injectable()
export class FindOrCreateUserForJwtUseCase {
  private readonly logger = new Logger(FindOrCreateUserForJwtUseCase.name);

  constructor(
    @Inject('IPendingUserRepository')
    private readonly pendingUserRepository: IPendingUserRepository,
    @Inject('IUserRepository') private readonly userRepository: IUserRepository,
    private readonly createPendingUserUseCase: CreatePendingUserUseCase,
  ) {}

  /**
   * Execute the use case to find or create a user for JWT generation
   * @param input Device ID input with optional FCID
   * @returns Promise that resolves to a user suitable for JWT creation
   */
  async execute(input: DeviceIdInput): Promise<UserForJwt> {
    this.logger.debug(`FindOrCreateUserForJwt called with: ${JSON.stringify(input)}`);

    // Flow 2.b: If both device ID and FCID are provided
    if (input.fcid && input.deviceId) {
      return this.handleDeviceIdWithFcid(input);
    }

    // Flow 2.a: If only device ID is provided
    return this.handleDeviceIdOnly(input);
  }

  /**
   * Handle the case where both device ID and FCID are provided
   * @param input Device ID input with FCID
   * @returns Promise that resolves to a user for JWT creation
   */
  private async handleDeviceIdWithFcid(input: DeviceIdInput): Promise<UserForJwt> {
    const { fcid, deviceId } = input;

    this.logger.debug(`Handling device ID with FCID: ${fcid}, deviceId: ${deviceId}`);

    // 2.b.1: Check if the provided FCID exists
    const userByFcid = await this.userRepository.findByFcid(fcid!);

    if (!userByFcid) {
      // 2.b.1.1: FCID doesn't exist, ignore it and go to Flow 2.a
      this.logger.debug(`FCID ${fcid} not found, falling back to device ID only flow`);
      return this.handleDeviceIdOnly({ ...input, fcid: undefined });
    }

    // 2.b.1.2: FCID exists, check if device ID exists in the FCID
    const hasDeviceId = userByFcid.device_ids.includes(deviceId);

    if (!hasDeviceId) {
      // Device ID doesn't exist in the FCID, ignore FCID and go to Flow 2.a
      this.logger.debug(
        `Device ID ${deviceId} not found in FCID ${fcid}, falling back to device ID only flow`,
      );
      return this.handleDeviceIdOnly({ ...input, fcid: undefined });
    }

    // 2.b.1.3: Both FCID and device ID exist and match, return the user
    this.logger.debug(`FCID ${fcid} and device ID ${deviceId} match, returning user`);
    return userByFcid;
  }

  /**
   * Handle the case where only device ID is provided
   * @param input Device ID input without FCID
   * @returns Promise that resolves to a user for JWT creation
   */
  private async handleDeviceIdOnly(input: DeviceIdInput): Promise<UserForJwt> {
    const { deviceId } = input;

    this.logger.debug(`Handling device ID only: ${deviceId}`);

    // 2.a.1: Look for ANONYMOUS user first
    const anonymousUser = await this.userRepository.findAnonymousUser(deviceId);

    if (anonymousUser) {
      this.logger.debug(`Found ANONYMOUS user for device ID ${deviceId}: ${anonymousUser.fcid}`);
      return anonymousUser;
    }

    // 2.a.1: Look for PENDING user in database (legacy)
    const databasePendingUser = await this.userRepository.findPendingUser(deviceId);

    if (databasePendingUser) {
      this.logger.debug(
        `Found database PENDING user for device ID ${deviceId}: ${databasePendingUser.fcid}`,
      );
      return databasePendingUser;
    }

    // 2.a.1: Look for PENDING user in Redis
    const redisPendingUser = await this.pendingUserRepository.findByDeviceId(deviceId);

    if (redisPendingUser) {
      this.logger.debug(
        `Found Redis PENDING user for device ID ${deviceId}: ${redisPendingUser.fcid}`,
      );
      return mapPendingUserToUserForJwt(redisPendingUser);
    }

    // 2.a.2: No user found, create a new PENDING user
    this.logger.debug(`No user found for device ID ${deviceId}, creating new PENDING user`);

    // First validate the constraint: ensure no conflicting users exist
    await this.validateDeviceIdConstraint(deviceId);

    const newPendingUser = await this.createPendingUserUseCase.execute(input);
    return mapPendingUserToUserForJwt(newPendingUser);
  }

  /**
   * Validate that only one user (ANONYMOUS or PENDING) exists per device ID
   * @param deviceId The device ID to validate
   * @throws DeviceIdConstraintViolationError if constraint is violated
   */
  private async validateDeviceIdConstraint(deviceId: string): Promise<void> {
    const isValid = await this.userRepository.validateDeviceIdConstraint(deviceId);

    if (!isValid) {
      this.logger.error(`Device ID constraint violation for ${deviceId}`);
      throw new DeviceIdConstraintViolationError(deviceId);
    }
  }
}
