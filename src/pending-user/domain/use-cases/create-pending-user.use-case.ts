import { Injectable, Logger, Inject } from '@nestjs/common';
import { DeviceIdInput, PendingUser, DeviceIdentifiers } from '../entities/pending-user.entity';
import { IPendingUserRepository } from '../repositories/pending-user.repository.interface';
import { PendingUserConfig } from '../../infrastructure/config/pending-user.config';
import { UserCreationInProgressError } from '../exceptions/pending-user.exceptions';
import { FcidGenerator } from '../../../common/utils/fcid-generator.util';

/**
 * Use case for creating a new pending user in Redis.
 *
 * This use case handles the atomic creation of pending users with
 * distributed locking to prevent race conditions and duplicate users.
 */
@Injectable()
export class CreatePendingUserUseCase {
  private readonly logger = new Logger(CreatePendingUserUseCase.name);

  constructor(
    @Inject('IPendingUserRepository')
    private readonly pendingUserRepository: IPendingUserRepository,
    private readonly config: PendingUserConfig,
  ) {}

  /**
   * Execute the use case to create a new pending user
   * @param input Device ID input for user creation
   * @returns Promise that resolves to the created pending user
   */
  async execute(input: DeviceIdInput): Promise<PendingUser> {
    const { deviceId } = input;

    this.logger.debug(`Creating pending user for device ID: ${deviceId}`);

    const lockKey = `pending:lock:${deviceId}`;
    const lockTtlMs = this.config.lockTtlMs;
    const maxRetries = 3;
    const baseDelayMs = 100;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      let lockAcquired = false;

      try {
        // Acquire distributed lock for atomic operation
        lockAcquired = await this.pendingUserRepository.acquireLock(lockKey, lockTtlMs);

        if (!lockAcquired) {
          // If this is not the last attempt, wait and retry
          if (attempt < maxRetries) {
            const delayMs = baseDelayMs * Math.pow(2, attempt); // Exponential backoff
            this.logger.debug(
              `Failed to acquire lock for device ID: ${deviceId}, attempt ${attempt + 1}/${
                maxRetries + 1
              }. Retrying in ${delayMs}ms...`,
            );
            await this.delay(delayMs);
            continue;
          }

          // Last attempt failed, check if user was created by another process
          this.logger.debug(
            `Failed to acquire lock for device ID: ${deviceId} after ${
              maxRetries + 1
            } attempts. Checking if user exists...`,
          );

          const existingUser = await this.pendingUserRepository.findByDeviceId(deviceId);
          if (existingUser) {
            this.logger.debug(
              `Found existing pending user created by another process: ${existingUser.fcid}`,
            );
            return existingUser;
          }

          // No user found and can't acquire lock - this shouldn't happen in normal operation
          this.logger.warn(
            `Failed to acquire lock for device ID: ${deviceId} and no existing user found. User creation in progress.`,
          );
          throw new UserCreationInProgressError(deviceId);
        }

        // Lock acquired successfully, proceed with creation
        // Double-check that user doesn't exist (race condition protection)
        const existingUser = await this.pendingUserRepository.findByDeviceId(deviceId);
        if (existingUser) {
          this.logger.debug(`Pending user already exists for device ID: ${deviceId}`);
          return existingUser;
        }

        // Create the new pending user
        const pendingUser = this.createPendingUserEntity(input);

        // Store in Redis with TTL
        await this.pendingUserRepository.store(pendingUser);

        this.logger.debug(`Created pending user: ${pendingUser.fcid} for device ID: ${deviceId}`);
        return pendingUser;
      } catch (error) {
        if (error instanceof UserCreationInProgressError) {
          throw error; // Re-throw this specific error
        }
        this.logger.error(
          `Error creating pending user for device ID: ${deviceId}, attempt ${attempt + 1}`,
          error,
        );

        // If this is the last attempt, throw the error
        if (attempt === maxRetries) {
          throw error;
        }

        // Otherwise, continue to next attempt
      } finally {
        // Always release the lock if acquired
        if (lockAcquired) {
          try {
            await this.pendingUserRepository.releaseLock(lockKey);
          } catch (releaseError) {
            this.logger.error(`Error releasing lock for device ID: ${deviceId}`, releaseError);
          }
        }
      }
    }

    // This should never be reached, but just in case
    throw new Error(
      `Unexpected error: failed to create pending user after ${maxRetries + 1} attempts`,
    );
  }

  /**
   * Create a pending user entity from device input
   * @param input Device ID input
   * @returns PendingUser entity
   */
  private createPendingUserEntity(input: DeviceIdInput): PendingUser {
    const identifiers = this.buildDeviceIdentifiers(input);
    const fcid = FcidGenerator.generateDeterministicFcid({
      deviceId: input.deviceId,
      adid: input.adid,
      idfv: input.idfv,
      idfa: input.idfa,
      gaid: input.gaid,
    });
    const now = new Date();

    return {
      fcid,
      deviceId: input.deviceId,
      identifiers,
      createdAt: now,
      ttl: this.config.pendingUserTtlSeconds,
    };
  }

  /**
   * Build device identifiers from input
   * @param input Device ID input
   * @returns DeviceIdentifiers object
   */
  private buildDeviceIdentifiers(input: DeviceIdInput): DeviceIdentifiers {
    const identifiers: DeviceIdentifiers = {};

    if (input.idfv) {
      identifiers.idfv = [input.idfv];
    }
    if (input.adid) {
      identifiers.adid = [input.adid];
    }
    if (input.idfa) {
      identifiers.idfa = [input.idfa];
    }
    if (input.gaid) {
      identifiers.gaid = [input.gaid];
    }

    return identifiers;
  }

  /**
   * Utility method to delay execution
   * @param ms Milliseconds to delay
   * @returns Promise that resolves after the delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
