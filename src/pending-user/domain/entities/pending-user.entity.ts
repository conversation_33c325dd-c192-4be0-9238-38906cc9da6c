/**
 * Core domain entity representing a pending user stored in Redis.
 *
 * A pending user is a temporary user entity that exists only in Redis
 * until it becomes a permanent user in the database. This entity contains
 * minimal information needed for JWT creation and user identification.
 */
export interface PendingUser {
  /** Deterministic UUID generated from device identifiers */
  fcid: string;

  /** Primary device identifier used for lookup */
  deviceId: string;

  /** All known device identifiers for this user */
  identifiers: DeviceIdentifiers;

  /** Timestamp when the pending user was created */
  createdAt: Date;

  /** Time to live in seconds (for Redis TTL) */
  ttl: number;
}

/**
 * Device identifiers supported by the system
 */
export interface DeviceIdentifiers {
  /** iOS Identifier for Vendor */
  idfv?: string[];

  /** Android Device ID */
  adid?: string[];

  /** iOS Identifier for Advertisers */
  idfa?: string[];

  /** Google Advertising ID */
  gaid?: string[];
}

/**
 * User entity optimized for JWT creation
 * This represents either a database user or a pending user
 */
export interface UserForJwt {
  /** User's Firebase Client ID */
  fcid: string;

  /** User type (ANONYMOUS, PENDING, REGISTERED, etc.) */
  type: UserType;

  /** Device identifiers */
  identifiers: DeviceIdentifiers;

  /** All device IDs associated with this user */
  device_ids: string[];

  /** Creation timestamp */
  created_at?: Date;

  /** Last update timestamp */
  updated_at?: Date;
}

/**
 * Supported user types in the system
 */
export enum UserType {
  ANONYMOUS = 'ANONYMOUS',
  PENDING = 'PENDING',
  REGISTERED = 'REGISTERED',
  MERGED = 'MERGED',
}

/**
 * Input for device-based user lookup and creation
 */
export interface DeviceIdInput {
  /** Primary device identifier (required) */
  deviceId: string;

  /** Optional Firebase Client ID */
  fcid?: string;

  /** iOS Identifier for Vendor */
  idfv?: string;

  /** Android Device ID */
  adid?: string;

  /** iOS Identifier for Advertisers */
  idfa?: string;

  /** Google Advertising ID */
  gaid?: string;
}
