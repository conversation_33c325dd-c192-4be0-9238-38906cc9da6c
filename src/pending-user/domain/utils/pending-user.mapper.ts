import { PendingUser } from '../entities/pending-user.entity';
import { UserForJwt, UserType } from '../entities/pending-user.entity';

/**
 * Utility functions for mapping between PendingUser and UserForJwt entities.
 *
 * This module centralizes the mapping logic to ensure consistency and avoid code duplication.
 */

/**
 * Maps a PendingUser entity to a UserForJwt object.
 *
 * This function consolidates the device IDs from both the primary deviceId
 * and the identifiers object, filtering out any falsy values.
 *
 * @param pendingUser The PendingUser entity to map
 * @returns UserForJwt object with all device IDs consolidated
 */
export function mapPendingUserToUserForJwt(pendingUser: PendingUser): UserForJwt {
  return {
    fcid: pendingUser.fcid,
    type: UserType.PENDING,
    identifiers: pendingUser.identifiers,
    device_ids: [pendingUser.deviceId, ...Object.values(pendingUser.identifiers).flat()].filter(
      <PERSON><PERSON><PERSON>,
    ),
    created_at: pendingUser.createdAt,
    updated_at: pendingUser.createdAt,
  };
}

/**
 * Maps a UserForJwt object back to a PendingUser entity.
 *
 * This function extracts the primary deviceId from the device_ids array
 * and reconstructs the identifiers object.
 *
 * @param userForJwt The UserForJwt object to map
 * @param primaryDeviceId The primary device ID to use (usually the first in device_ids)
 * @param ttl Time to live in seconds (required for Redis storage)
 * @returns PendingUser entity
 */
export function mapUserForJwtToPendingUser(
  userForJwt: UserForJwt,
  primaryDeviceId: string,
  ttl: number,
): PendingUser {
  return {
    fcid: userForJwt.fcid,
    deviceId: primaryDeviceId,
    identifiers: userForJwt.identifiers,
    createdAt: userForJwt.created_at || new Date(),
    ttl,
  };
}
