import { UserForJwt, UserType } from '../entities/pending-user.entity';

/**
 * Repository interface for database user operations.
 *
 * This interface defines the contract for interacting with
 * permanent users stored in the PostgreSQL database.
 */
export interface IUserRepository {
  /**
   * Find a user by device ID, prioritizing REGISTERED users
   * @param deviceId The device ID to search for
   * @returns Promise that resolves to the user or null if not found
   */
  findByDeviceId(deviceId: string): Promise<UserForJwt | null>;

  /**
   * Find a user by Firebase Client ID
   * @param fcid The Firebase Client ID to search for
   * @returns Promise that resolves to the user or null if not found
   */
  findByFcid(fcid: string): Promise<UserForJwt | null>;

  /**
   * Find an ANONYMOUS user by device ID
   * @param deviceId The device ID to search for
   * @returns Promise that resolves to the anonymous user or null if not found
   */
  findAnonymousUser(deviceId: string): Promise<UserForJwt | null>;

  /**
   * Find a PENDING user by device ID (for database-stored pending users)
   * @param deviceId The device ID to search for
   * @returns Promise that resolves to the pending user or null if not found
   */
  findPendingUser(deviceId: string): Promise<UserForJwt | null>;

  /**
   * Check if a user with the given FCID exists and has the specified device ID
   * @param fcid The Firebase Client ID to check
   * @param deviceId The device ID to verify
   * @returns Promise that resolves to the user if both FCID and device ID match
   */
  findUniqueUser(fcid: string, deviceId: string): Promise<UserForJwt | null>;

  /**
   * Get all users with the specified device ID (for constraint validation)
   * @param deviceId The device ID to search for
   * @returns Promise that resolves to an array of users with that device ID
   */
  findAllByDeviceId(deviceId: string): Promise<UserForJwt[]>;

  /**
   * Count users by type for a specific device ID
   * @param deviceId The device ID to search for
   * @param userType The user type to count
   * @returns Promise that resolves to the count of users
   */
  countByDeviceIdAndType(deviceId: string, userType: UserType): Promise<number>;

  /**
   * Validate that only one user (ANONYMOUS or PENDING) exists per device ID
   * @param deviceId The device ID to validate
   * @returns Promise that resolves to true if constraint is satisfied
   */
  validateDeviceIdConstraint(deviceId: string): Promise<boolean>;
}
