import { PendingUser } from '../entities/pending-user.entity';

/**
 * Repository interface for pending user operations in Redis.
 *
 * This interface defines the contract for storing and retrieving
 * pending users from Redis with proper TTL management and
 * atomic operations.
 */
export interface IPendingUserRepository {
  /**
   * Store a pending user in Redis with TTL
   * @param pendingUser The pending user to store
   * @returns Promise that resolves when the user is stored
   */
  store(pendingUser: PendingUser): Promise<void>;

  /**
   * Retrieve a pending user by device ID
   * @param deviceId The device ID to look up
   * @returns Promise that resolves to the pending user or null if not found
   */
  findByDeviceId(deviceId: string): Promise<PendingUser | null>;

  /**
   * Retrieve a pending user by FCID
   * @param fcid The Firebase Client ID to look up
   * @returns Promise that resolves to the pending user or null if not found
   */
  findByFcid(fcid: string): Promise<PendingUser | null>;

  /**
   * Remove a pending user from Redis
   * @param deviceId The device ID of the user to remove
   * @returns Promise that resolves when the user is removed
   */
  remove(deviceId: string): Promise<void>;

  /**
   * Check if a pending user exists for the given device ID
   * @param deviceId The device ID to check
   * @returns Promise that resolves to true if the user exists
   */
  exists(deviceId: string): Promise<boolean>;

  /**
   * Acquire a distributed lock for atomic operations
   * @param key The lock key
   * @param ttlMs Time to live for the lock in milliseconds
   * @returns Promise that resolves to true if lock was acquired
   */
  acquireLock(key: string, ttlMs: number): Promise<boolean>;

  /**
   * Release a distributed lock
   * @param key The lock key to release
   * @returns Promise that resolves when the lock is released
   */
  releaseLock(key: string): Promise<void>;

  /**
   * Get all pending users (for debugging/monitoring)
   * @returns Promise that resolves to an array of all pending users
   */
  findAll(): Promise<PendingUser[]>;

  /**
   * Clean up expired pending users (maintenance operation)
   * @returns Promise that resolves to the number of cleaned up users
   */
  cleanup(): Promise<number>;
}
