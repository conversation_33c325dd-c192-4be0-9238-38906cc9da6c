# Pending User Management Library

A modular, Redis-based pending user management system designed for JWT token creation with clean architecture principles and separation of concerns.

## Overview

This library replaces the previous bulma implementation with a more robust, scalable solution that:

- Stores pending users exclusively in Redis with configurable TTL
- Implements atomic operations using distributed locks
- Enforces strict business rules for user lookup and creation
- Provides clean interfaces for future library extraction
- Maintains backward compatibility with existing JWT creation flow

## Architecture

The pending user module follows a clean architecture pattern with clear separation of concerns:

### Domain Layer

- **Entities**: Core business objects (`PendingUser`, `UserForJwt`)
- **Use Cases**: Business logic orchestration
- **Repositories**: Data access interfaces
- **Exceptions**: Domain-specific error types
- **Utils**: Shared utilities including the centralized mapper

### Application Layer

- **Services**: Application services that coordinate use cases
- **Config**: Configuration management

### Infrastructure Layer

- **Repositories**: Concrete implementations of data access
- **Config**: Infrastructure-specific configuration

## Key Components

### Entities

#### PendingUser

Represents a user in the pending state, stored in Redis with TTL.

#### UserForJwt

Represents a user suitable for JWT token generation, used across the application.

### Use Cases

#### FindOrCreateUserForJwtUseCase

Implements the complex business logic for user lookup and creation:

1. If only device ID provided: Look for ANONYMOUS first, then PENDING, then create new PENDING
2. If device ID + FCID provided: Validate FCID exists and has the device ID
3. Enforce the constraint: only one user (ANONYMOUS or PENDING) per device ID

#### CreatePendingUserUseCase

Handles the creation of new pending users with proper validation and constraint checking.

### Services

#### PendingUserService

Application service that orchestrates the pending user operations and provides a clean API for other modules.

### Repositories

#### IPendingUserRepository

Interface for pending user data access operations.

#### RedisPendingUserRepository

Redis-based implementation with TTL support and SCAN operations for performance.

### Utilities

#### PendingUserMapper

Centralized mapping utilities to eliminate code duplication:

- `mapPendingUserToUserForJwt(pendingUser: PendingUser): UserForJwt`

  - Maps a PendingUser entity to a UserForJwt object
  - Consolidates device IDs from both primary deviceId and identifiers
  - Filters out falsy values

- `mapUserForJwtToPendingUser(userForJwt: UserForJwt, primaryDeviceId: string): PendingUser`
  - Maps a UserForJwt object back to a PendingUser entity
  - Extracts primary deviceId and reconstructs identifiers

### Exceptions

- `PendingUserError`: Base exception for all pending user errors
- `InvalidInputError`: Invalid input data
- `DeviceIdConstraintViolationError`: Device ID already in use
- `RedisUnavailableError`: Redis connectivity issues

## Business Logic

The system implements complex user lookup logic as specified in the requirements:

### Flow 2.a: Device ID Only

1. Look for ANONYMOUS user first
2. Then look for PENDING user (database or Redis)
3. Create new PENDING user if none found

### Flow 2.b: Device ID + FCID

1. Validate FCID exists in database
2. Validate device ID exists in the FCID
3. Fall back to Flow 2.a if validation fails

### Constraints

- Only one user (ANONYMOUS or PENDING) per device ID
- PENDING users stored in Redis with TTL
- **Shared deterministic FCID generation**: ANONYMOUS and PENDING users with the same device identifiers will have the same FCID, ensuring consistency when a PENDING user transitions to ANONYMOUS
- **Automatic Redis cleanup**: When a PENDING user transitions to ANONYMOUS (moves from Redis to database), the Redis entry is automatically removed to prevent data duplication

## Configuration

Add the following environment variables:

```bash
# Pending User Configuration
PENDING_USER_TTL_SECONDS=86400                    # 24 hours
PENDING_USER_LOCK_TTL_MS=5000                     # 5 seconds
PENDING_USER_KEY_PREFIX=pending:user:
PENDING_USER_FCID_KEY_PREFIX=pending:fcid:
PENDING_USER_LOCK_KEY_PREFIX=pending:lock:
PENDING_USER_ENABLE_REDIS_FALLBACK=true
PENDING_USER_MAX_RETRY_ATTEMPTS=3
PENDING_USER_RETRY_DELAY_MS=1000
PENDING_USER_ENABLE_STRICT_CONSTRAINTS=true
PENDING_USER_ENABLE_CLEANUP=true
PENDING_USER_CLEANUP_INTERVAL_SECONDS=3600        # 1 hour
```

## Usage

### Basic Usage

```typescript
import { PendingUserService, DeviceIdInput } from '../pending-user';

// Inject the service
constructor(private readonly pendingUserService: PendingUserService) {}

// Find or create user for JWT
const input: DeviceIdInput = {
  deviceId: 'device-123',
  idfv: '12345678-1234-1234-1234-123456789012',
};

const user = await this.pendingUserService.findOrCreateUserForJwt(input);
```

### With FCID Validation

```typescript
const input: DeviceIdInput = {
  deviceId: 'device-123',
  fcid: 'existing-fcid-456',
  idfv: '12345678-1234-1234-1234-123456789012',
};

const user = await this.pendingUserService.findOrCreateUserForJwt(input);
```

## Error Handling

The library provides specific exception types:

- `InvalidInputError`: Invalid input parameters
- `RedisUnavailableError`: Redis connectivity issues
- `DeviceIdConstraintViolationError`: Multiple users for same device ID
- `UserCreationInProgressError`: Concurrent user creation attempt
- `FcidValidationError`: FCID validation failed

## Redis Storage Schema

- **Pending User Data**: `pending:user:{deviceId}` → JSON serialized PendingUser
- **FCID Mapping**: `pending:fcid:{fcid}` → deviceId (for reverse lookups)
- **Lock Keys**: `pending:lock:{deviceId}` → temporary lock for atomic operations

## Migration from Bulma

### Before (Bulma)

```typescript
import { findOrCreatePendingUser } from '@visualblasters/bulma';

const user = await findOrCreatePendingUser(input, options);
```

### After (New Library)

```typescript
import { PendingUserService } from '../pending-user';

const user = await this.pendingUserService.findOrCreateUserForJwt(input);
```

### Key Differences

1. **Storage**: Redis-only vs Database storage
2. **Configuration**: Environment-based vs Options-based
3. **Error Handling**: Typed exceptions vs Generic errors
4. **Architecture**: Clean Architecture vs Functional approach

## Testing

Run the tests:

```bash
# Unit tests
npm test src/pending-user/tests/pending-user.service.spec.ts

# Integration tests
npm test src/pending-user/tests/jwt-creation.integration.spec.ts
```

## Redis Cleanup

The system automatically cleans up Redis entries when PENDING users transition to ANONYMOUS:

### Automatic Cleanup

When a PENDING user is converted to ANONYMOUS (via user creation), the system:

1. Saves the user to the database
2. Automatically removes the corresponding Redis entry
3. Logs the cleanup operation for monitoring

### Manual Cleanup Methods

```typescript
// Remove pending user after transition (called automatically)
await pendingUserService.removePendingUserAfterTransition(fcid, deviceId);

// Remove pending user by FCID only
await pendingUserService.removePendingUserByFcid(fcid);

// General maintenance cleanup
const cleanedCount = await pendingUserService.cleanup();
```

### Error Handling

- Redis cleanup failures don't affect user creation
- All cleanup operations are logged for monitoring
- Failed cleanups are logged as warnings, not errors

## Monitoring

The service provides statistics and cleanup methods:

```typescript
// Get statistics
const stats = await pendingUserService.getStatistics();

// Manual cleanup
const cleanedCount = await pendingUserService.cleanup();
```

## FCID Generation

The library uses a shared `FcidGenerator` utility that ensures consistent FCID generation across the system:

### Deterministic Generation

- **PENDING users** (Redis): Use deterministic FCID based on device identifiers
- **ANONYMOUS users** (Database): Use the same deterministic FCID for the same device identifiers
- **Priority order**: `adid > idfv > idfa > gaid > deviceId`

### Format

- **Pattern**: `xxxxxxxx-xxxx-4xxx-xxxx-xxxxxxxxxxxx-XX`
- **Structure**: UUID v4 + 2-digit deterministic suffix
- **Example**: `12345678-1234-4567-8901-123456789012-42`

### Consistency Guarantee

```typescript
// Same device identifiers = Same FCID
const identifiers = { deviceId: 'device-123', idfv: 'uuid-here' };

const pendingFcid = FcidGenerator.generateDeterministicFcid(identifiers);
const anonFcid = FcidGenerator.generateDeterministicFcid(identifiers);

console.log(pendingFcid === anonFcid); // true
```

## Future Enhancements

- Extract as standalone npm package
- Add metrics and monitoring
- Implement circuit breaker pattern
- Add batch operations support
- Implement user migration utilities
