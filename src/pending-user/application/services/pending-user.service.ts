import { Injectable, Logger, Inject } from '@nestjs/common';
import { DeviceIdInput, UserForJwt, UserType } from '../../domain/entities/pending-user.entity';
import { FindOrCreateUserForJwtUseCase } from '../../domain/use-cases/find-or-create-user-for-jwt.use-case';
import { PendingUserConfig } from '../../infrastructure/config/pending-user.config';
import { IUserRepository } from '../../domain/repositories/user.repository.interface';
import { IPendingUserRepository } from '../../domain/repositories/pending-user.repository.interface';
import {
  RedisUnavailableError,
  InvalidInputError,
  PendingUserError,
} from '../../domain/exceptions/pending-user.exceptions';
import { isRedisError } from '../../../common/utils/redis-error.util';
import { mapPendingUserToUserForJwt } from '../../domain/utils/pending-user.mapper';

/**
 * Main service for pending user management.
 *
 * This service provides the public API for JWT creation and orchestrates
 * the complex user lookup and creation logic defined in the requirements.
 * It serves as the main entry point for the pending user management system.
 */
@Injectable()
export class PendingUserService {
  private readonly logger = new Logger(PendingUserService.name);

  constructor(
    private readonly findOrCreateUserForJwtUseCase: FindOrCreateUserForJwtUseCase,
    private readonly config: PendingUserConfig,
    @Inject('IUserRepository') private readonly userRepository: IUserRepository,
    @Inject('IPendingUserRepository')
    private readonly pendingUserRepository: IPendingUserRepository,
  ) {}

  /**
   * Find or create a user for JWT token generation.
   *
   * This method implements the complex business logic specified in the requirements:
   *
   * Flow 2.a (device ID only):
   * - Look for ANONYMOUS user first
   * - Then look for PENDING user
   * - Create new PENDING user if none found
   *
   * Flow 2.b (device ID + FCID):
   * - Validate FCID exists
   * - Validate device ID exists in FCID
   * - Fall back to Flow 2.a if validation fails
   *
   * @param input Device ID input with optional FCID
   * @returns Promise that resolves to a user suitable for JWT creation
   */
  async findOrCreateUserForJwt(input: DeviceIdInput): Promise<UserForJwt> {
    this.logger.debug(
      `PendingUserService.findOrCreateUserForJwt called with: ${JSON.stringify(input)}`,
    );

    try {
      // Validate input
      this.validateInput(input);

      // Execute the main use case
      const user = await this.findOrCreateUserForJwtUseCase.execute(input);

      this.logger.debug(`Successfully resolved user for JWT: ${user.fcid} (type: ${user.type})`);
      return user;
    } catch (error) {
      this.logger.error(`Error in findOrCreateUserForJwt: ${error.message}`, error);

      // If Redis is unavailable and fallback is enabled, try fallback logic
      if (this.config.enableRedisFallback && this.isRedisError(error)) {
        this.logger.warn('Redis unavailable, attempting fallback logic');
        try {
          return await this.handleRedisFallback(input);
        } catch (fallbackError) {
          this.logger.error('Fallback also failed', fallbackError);
          throw new RedisUnavailableError('Both primary and fallback user lookup failed');
        }
      }

      // Re-throw as PendingUserError if it's not already one
      if (error instanceof PendingUserError) {
        throw error;
      }

      throw new PendingUserError(`User lookup failed: ${error.message}`);
    }
  }

  /**
   * Find an existing pending user by device ID without creating a new one.
   * This is used for user lookup during the transition process.
   *
   * @param deviceId The device ID to search for
   * @returns Promise that resolves to existing pending user or null
   */
  async findExistingPendingUser(deviceId: string): Promise<UserForJwt | null> {
    try {
      this.logger.debug(`Looking for existing PENDING user with deviceId: ${deviceId}`);

      // Check Redis for existing pending user
      const pendingUser = await this.pendingUserRepository.findByDeviceId(deviceId);

      if (!pendingUser) {
        this.logger.debug(`No existing PENDING user found in Redis for deviceId: ${deviceId}`);
        return null;
      }

      this.logger.debug(`Found existing PENDING user in Redis: ${pendingUser.fcid}`);

      // Map to UserForJwt format using centralized mapper
      return mapPendingUserToUserForJwt(pendingUser);
    } catch (error) {
      this.logger.error(`Error finding existing pending user for deviceId: ${deviceId}`, error);
      return null;
    }
  }

  /**
   * Get statistics about pending users (for monitoring)
   * @returns Promise that resolves to pending user statistics
   */
  async getStatistics(): Promise<PendingUserStatistics> {
    try {
      // This would be implemented with the repository
      return {
        totalPendingUsers: 0,
        oldestPendingUser: null,
        newestPendingUser: null,
        averageAge: 0,
      };
    } catch (error) {
      this.logger.error('Error getting pending user statistics', error);
      return {
        totalPendingUsers: 0,
        oldestPendingUser: null,
        newestPendingUser: null,
        averageAge: 0,
      };
    }
  }

  /**
   * Remove a pending user from Redis when they transition to ANONYMOUS.
   * This should be called after a PENDING user is successfully saved to the database.
   *
   * @param fcid The FCID of the user that transitioned
   * @param deviceId The device ID to remove from Redis
   * @returns Promise that resolves when cleanup is complete
   */
  async removePendingUserAfterTransition(fcid: string, deviceId: string): Promise<void> {
    try {
      this.logger.debug(
        `Attempting Redis cleanup after transition: FCID=${fcid}, deviceId=${deviceId}`,
      );

      // Check if the pending user exists in Redis before attempting removal
      const pendingUser = await this.pendingUserRepository.findByDeviceId(deviceId);

      if (!pendingUser) {
        this.logger.debug(
          `No pending user found in Redis for deviceId=${deviceId}, cleanup not needed`,
        );
        return;
      }

      this.logger.debug(
        `Found pending user in Redis: FCID=${pendingUser.fcid}, deviceId=${deviceId}`,
      );

      // Verify the FCID matches to ensure we're removing the correct user
      if (pendingUser.fcid !== fcid) {
        this.logger.warn(
          `FCID mismatch during cleanup: expected=${fcid}, found=${pendingUser.fcid}. Skipping removal for safety.`,
        );
        return;
      }

      this.logger.debug(`FCID matches, proceeding with Redis removal: ${fcid}`);

      // Remove the pending user from Redis
      await this.pendingUserRepository.remove(deviceId);

      this.logger.debug(
        `✅ Successfully removed pending user from Redis: FCID=${fcid}, deviceId=${deviceId}`,
      );
    } catch (error) {
      // Log the error but don't throw - we don't want to fail the user creation if Redis cleanup fails
      this.logger.error(
        `❌ Error removing pending user from Redis after transition: FCID=${fcid}, deviceId=${deviceId}`,
        error,
      );
    }
  }

  /**
   * Remove a pending user from Redis by FCID.
   * This is useful when we only have the FCID and need to find and remove the pending user.
   *
   * @param fcid The FCID of the user to remove
   * @returns Promise that resolves when cleanup is complete
   */
  async removePendingUserByFcid(fcid: string): Promise<void> {
    try {
      this.logger.debug(`Starting Redis cleanup for pending user by FCID: ${fcid}`);

      // Find the pending user by FCID
      const pendingUser = await this.pendingUserRepository.findByFcid(fcid);

      if (!pendingUser) {
        this.logger.debug(`No pending user found in Redis for FCID=${fcid}, cleanup not needed`);
        return;
      }

      this.logger.debug(
        `Found pending user in Redis: FCID=${fcid}, deviceId=${pendingUser.deviceId}`,
      );

      // Remove using the device ID
      await this.removePendingUserAfterTransition(fcid, pendingUser.deviceId);

      this.logger.debug(`Redis cleanup completed for FCID: ${fcid}`);
    } catch (error) {
      // Log the error but don't throw
      this.logger.error(`Error removing pending user from Redis by FCID: ${fcid}`, error);
    }
  }

  /**
   * Clean up expired pending users (maintenance operation)
   * @returns Promise that resolves to the number of cleaned up users
   */
  async cleanup(): Promise<number> {
    try {
      if (!this.config.enableCleanup) {
        this.logger.debug('Cleanup is disabled');
        return 0;
      }

      this.logger.debug('Starting pending user cleanup');
      const cleanedCount = await this.pendingUserRepository.cleanup();
      this.logger.debug(`Cleanup completed. Removed ${cleanedCount} expired entries.`);

      return cleanedCount;
    } catch (error) {
      this.logger.error('Error during cleanup operation', error);
      return 0;
    }
  }

  /**
   * Validate the input parameters
   * @param input Device ID input to validate
   * @throws InvalidInputError if input is invalid
   */
  private validateInput(input: DeviceIdInput): void {
    if (!input.deviceId) {
      throw new InvalidInputError('Device ID is required');
    }

    if (typeof input.deviceId !== 'string' || input.deviceId.trim() === '') {
      throw new InvalidInputError('Device ID must be a non-empty string');
    }

    // Validate FCID format if provided
    if (input.fcid && typeof input.fcid !== 'string') {
      throw new InvalidInputError('FCID must be a string');
    }

    // Validate device identifier formats if provided
    if (input.idfv && !this.isValidUUID(input.idfv)) {
      throw new InvalidInputError('Invalid IDFV format');
    }

    if (input.adid && !this.isValidAdid(input.adid)) {
      throw new InvalidInputError('Invalid ADID format');
    }
  }

  /**
   * Check if a string is a valid UUID format
   * @param value String to validate
   * @returns True if valid UUID format
   */
  private isValidUUID(value: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}(?:-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  }

  /**
   * Check if a string is a valid ADID format
   * @param value String to validate
   * @returns True if valid ADID format
   */
  private isValidAdid(value: string): boolean {
    const adidRegex = /^[0-9a-fA-F]{14,16}$/;
    return adidRegex.test(value);
  }

  /**
   * Check if an error is related to Redis connectivity
   * @param error The error to check
   * @returns True if it's a Redis-related error
   */
  private isRedisError(error: any): boolean {
    return isRedisError(error);
  }

  /**
   * Handle fallback logic when Redis is unavailable
   * @param input Device ID input
   * @returns Promise that resolves to a user for JWT creation
   */
  private async handleRedisFallback(input: DeviceIdInput): Promise<UserForJwt> {
    this.logger.warn('Executing Redis fallback logic');

    // In fallback mode, we only look up existing database users
    // We don't create new pending users since Redis is unavailable

    try {
      // Try to find existing user in database
      const user = await this.findExistingDatabaseUser(input);

      if (user) {
        this.logger.debug(`Found existing database user in fallback mode: ${user.fcid}`);
        return user;
      }

      // No existing user found and can't create pending user without Redis
      throw new Error('No existing user found and Redis is unavailable for pending user creation');
    } catch (fallbackError) {
      this.logger.error('Fallback logic also failed', fallbackError);
      throw new Error('Both primary and fallback user lookup failed');
    }
  }

  /**
   * Find existing user in database (fallback logic)
   * @param input Device ID input
   * @returns Promise that resolves to existing user or null
   */
  private async findExistingDatabaseUser(input: DeviceIdInput): Promise<UserForJwt | null> {
    const { deviceId, fcid } = input;

    // If FCID is provided, validate it first
    if (fcid) {
      const userByFcid = await this.userRepository.findByFcid(fcid);
      if (userByFcid && userByFcid.device_ids.includes(deviceId)) {
        return userByFcid;
      }
    }

    // Look for ANONYMOUS user first
    const anonymousUser = await this.userRepository.findAnonymousUser(deviceId);
    if (anonymousUser) {
      return anonymousUser;
    }

    // Look for PENDING user in database (legacy)
    const pendingUser = await this.userRepository.findPendingUser(deviceId);
    if (pendingUser) {
      return pendingUser;
    }

    return null;
  }
}

/**
 * Statistics about pending users
 */
export interface PendingUserStatistics {
  totalPendingUsers: number;
  oldestPendingUser: Date | null;
  newestPendingUser: Date | null;
  averageAge: number; // in seconds
}
