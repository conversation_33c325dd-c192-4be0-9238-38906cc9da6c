import { Injectable, Logger } from '@nestjs/common';
import { PendingUser } from '../../domain/entities/pending-user.entity';
import { IPendingUserRepository } from '../../domain/repositories/pending-user.repository.interface';
import { PendingUserConfig } from '../config/pending-user.config';
import { RedisService } from '../../../common/services/redis.service';
import { Redis } from 'ioredis';

/**
 * Redis-based implementation of the pending user repository.
 *
 * This repository handles all Redis operations for pending users,
 * including storage with TTL, distributed locking, and FCID mappings.
 */
@Injectable()
export class RedisPendingUserRepository implements IPendingUserRepository {
  private readonly logger = new Logger(RedisPendingUserRepository.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly config: PendingUserConfig,
  ) {}

  /**
   * Store a pending user in Redis with TTL
   */
  async store(pendingUser: PendingUser): Promise<void> {
    try {
      const userKey = this.config.buildPendingUserKey(pendingUser.deviceId);
      const fcidKey = this.config.buildFcidMappingKey(pendingUser.fcid);

      const userData = JSON.stringify({
        fcid: pendingUser.fcid,
        deviceId: pendingUser.deviceId,
        identifiers: pendingUser.identifiers,
        createdAt: pendingUser.createdAt.toISOString(),
        ttl: pendingUser.ttl,
      });

      // Use Redis pipeline for atomic operations
      const redis = this.redisService.getClient();
      const pipeline = redis.pipeline();

      // Store user data with TTL
      pipeline.setex(userKey, pendingUser.ttl, userData);

      // Store FCID mapping with TTL
      pipeline.setex(fcidKey, pendingUser.ttl, pendingUser.deviceId);

      await pipeline.exec();

      this.logger.debug(
        `Stored pending user: ${pendingUser.fcid} for device: ${pendingUser.deviceId}`,
      );
    } catch (error) {
      this.logger.error(`Error storing pending user: ${pendingUser.fcid}`, error);
      throw new Error(`Failed to store pending user: ${error.message}`);
    }
  }

  /**
   * Retrieve a pending user by device ID
   */
  async findByDeviceId(deviceId: string): Promise<PendingUser | null> {
    try {
      const userKey = this.config.buildPendingUserKey(deviceId);
      const userData = await this.redisService.get(userKey);

      if (!userData) {
        return null;
      }

      return this.deserializePendingUser(userData);
    } catch (error) {
      this.logger.error(`Error finding pending user by device ID: ${deviceId}`, error);
      return null;
    }
  }

  /**
   * Retrieve a pending user by FCID
   */
  async findByFcid(fcid: string): Promise<PendingUser | null> {
    try {
      const fcidKey = this.config.buildFcidMappingKey(fcid);
      const deviceId = await this.redisService.get(fcidKey);

      if (!deviceId) {
        return null;
      }

      return this.findByDeviceId(deviceId);
    } catch (error) {
      this.logger.error(`Error finding pending user by FCID: ${fcid}`, error);
      return null;
    }
  }

  /**
   * Remove a pending user from Redis
   */
  async remove(deviceId: string): Promise<void> {
    try {
      // First get the user to find the FCID
      const pendingUser = await this.findByDeviceId(deviceId);

      if (!pendingUser) {
        return; // User doesn't exist, nothing to remove
      }

      const userKey = this.config.buildPendingUserKey(deviceId);
      const fcidKey = this.config.buildFcidMappingKey(pendingUser.fcid);

      // Use Redis pipeline for atomic deletion
      const redis = this.redisService.getClient();
      const pipeline = redis.pipeline();

      pipeline.del(userKey);
      pipeline.del(fcidKey);

      await pipeline.exec();

      this.logger.debug(`Removed pending user: ${pendingUser.fcid} for device: ${deviceId}`);
    } catch (error) {
      this.logger.error(`Error removing pending user for device ID: ${deviceId}`, error);
      throw new Error(`Failed to remove pending user: ${error.message}`);
    }
  }

  /**
   * Check if a pending user exists for the given device ID
   */
  async exists(deviceId: string): Promise<boolean> {
    try {
      const userKey = this.config.buildPendingUserKey(deviceId);
      const redis = this.redisService.getClient();
      const exists = await redis.exists(userKey);
      return exists === 1;
    } catch (error) {
      this.logger.error(`Error checking if pending user exists for device ID: ${deviceId}`, error);
      return false;
    }
  }

  /**
   * Acquire a distributed lock for atomic operations
   */
  async acquireLock(key: string, ttlMs: number): Promise<boolean> {
    try {
      return await this.redisService.acquireLock(key, ttlMs);
    } catch (error) {
      this.logger.error(`Error acquiring lock: ${key}`, error);
      return false;
    }
  }

  /**
   * Release a distributed lock
   */
  async releaseLock(key: string): Promise<void> {
    try {
      await this.redisService.releaseLock(key);
    } catch (error) {
      this.logger.error(`Error releasing lock: ${key}`, error);
      throw error;
    }
  }

  /**
   * Get all pending users (for debugging/monitoring)
   */
  async findAll(): Promise<PendingUser[]> {
    try {
      const redis = this.redisService.getClient();
      const pattern = `${this.config.pendingUserKeyPrefix}*`;
      const keys = await this.scanKeys(redis, pattern);

      if (keys.length === 0) {
        return [];
      }

      const pipeline = redis.pipeline();
      keys.forEach(key => pipeline.get(key));

      const results = await pipeline.exec();
      const pendingUsers: PendingUser[] = [];

      for (const result of results || []) {
        if (result && result[1]) {
          try {
            const pendingUser = this.deserializePendingUser(result[1] as string);
            pendingUsers.push(pendingUser);
          } catch (error) {
            this.logger.warn(`Error deserializing pending user data: ${result[1]}`, error);
          }
        }
      }

      return pendingUsers;
    } catch (error) {
      this.logger.error('Error finding all pending users', error);
      return [];
    }
  }

  /**
   * Clean up expired pending users (maintenance operation)
   */
  async cleanup(): Promise<number> {
    try {
      // Redis automatically handles TTL expiration, but we can scan for any orphaned data
      const redis = this.redisService.getClient();
      const fcidPattern = `${this.config.fcidMappingKeyPrefix}*`;

      // Use SCAN instead of KEYS to avoid blocking Redis server
      const fcidKeys = await this.scanKeys(redis, fcidPattern);

      let cleanedCount = 0;

      // Check for orphaned FCID mappings (FCID key exists but user key doesn't)
      for (const fcidKey of fcidKeys) {
        const deviceId = await redis.get(fcidKey);
        if (deviceId) {
          const userKey = this.config.buildPendingUserKey(deviceId);
          const userExists = await redis.exists(userKey);

          if (userExists === 0) {
            // Orphaned FCID mapping, remove it
            await redis.del(fcidKey);
            cleanedCount++;
            this.logger.debug(`Cleaned orphaned FCID mapping: ${fcidKey}`);
          }
        }
      }

      this.logger.debug(`Cleanup completed. Removed ${cleanedCount} orphaned entries.`);
      return cleanedCount;
    } catch (error) {
      this.logger.error('Error during cleanup operation', error);
      return 0;
    }
  }

  /**
   * Scan Redis keys using SCAN command to avoid blocking the server
   * @param redis Redis client
   * @param pattern Key pattern to match
   * @returns Array of matching keys
   */
  private async scanKeys(redis: Redis, pattern: string): Promise<string[]> {
    const keys: string[] = [];
    let cursor = 0;
    const batchSize = 100; // Process keys in batches

    do {
      const result = await redis.scan(cursor, 'MATCH', pattern, 'COUNT', batchSize);
      cursor = parseInt(result[0]);
      keys.push(...result[1]);
    } while (cursor !== 0);

    return keys;
  }

  /**
   * Deserialize pending user data from Redis
   */
  private deserializePendingUser(userData: string): PendingUser {
    try {
      const data = JSON.parse(userData);
      return {
        fcid: data.fcid,
        deviceId: data.deviceId,
        identifiers: data.identifiers,
        createdAt: new Date(data.createdAt),
        ttl: data.ttl,
      };
    } catch (error) {
      this.logger.error(`Error deserializing pending user data: ${userData}`, error);
      throw new Error(`Invalid pending user data format: ${error.message}`);
    }
  }
}
