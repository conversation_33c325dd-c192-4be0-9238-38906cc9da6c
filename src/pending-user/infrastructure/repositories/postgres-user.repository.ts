import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserForJwt, UserType } from '../../domain/entities/pending-user.entity';
import { IUserRepository } from '../../domain/repositories/user.repository.interface';
import { User } from '../../../users/entities/user.entity';
import { ErrorLoggerService } from '../../../common/services/error-logger.service';

/**
 * PostgreSQL-based implementation of the user repository.
 *
 * This repository handles all database operations for permanent users,
 * using TypeORM directly for database interactions.
 */
@Injectable()
export class PostgresUserRepository implements IUserRepository {
  private readonly logger = new Logger(PostgresUserRepository.name);

  constructor(
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    private readonly errorLogger: ErrorLoggerService,
  ) {}

  /**
   * Find a user by device ID, prioritizing REGISTERED users
   */
  async findByDeviceId(deviceId: string): Promise<UserForJwt | null> {
    try {
      const user = await this.userRepository
        .createQueryBuilder('user')
        .where('user.type != :type', { type: UserType.MERGED })
        .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
        .orderBy("CASE WHEN user.type = 'REGISTERED' THEN 0 ELSE 1 END", 'ASC')
        .addOrderBy('user.created_at', 'ASC')
        .getOne();

      return user ? this.mapToUserForJwt(user) : null;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding user by device ID',
        context: 'PostgresUserRepository.findByDeviceId',
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId },
      });
      return null;
    }
  }

  /**
   * Find a user by Firebase Client ID
   */
  async findByFcid(fcid: string): Promise<UserForJwt | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { fcid },
      });

      return user ? this.mapToUserForJwt(user) : null;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding user by FCID',
        context: 'PostgresUserRepository.findByFcid',
        includeStack: true,
        includeRequest: false,
        metadata: { fcid },
      });
      return null;
    }
  }

  /**
   * Find an ANONYMOUS user by device ID
   */
  async findAnonymousUser(deviceId: string): Promise<UserForJwt | null> {
    try {
      const user = await this.userRepository
        .createQueryBuilder('user')
        .where("user.type = 'ANONYMOUS'")
        .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
        .getOne();

      return user ? this.mapToUserForJwt(user) : null;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding anonymous user by device ID',
        context: 'PostgresUserRepository.findAnonymousUser',
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId },
      });
      return null;
    }
  }

  /**
   * Find a PENDING user by device ID (for database-stored pending users)
   */
  async findPendingUser(deviceId: string): Promise<UserForJwt | null> {
    try {
      const user = await this.userRepository
        .createQueryBuilder('user')
        .where("user.type = 'PENDING'")
        .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
        .getOne();

      return user ? this.mapToUserForJwt(user) : null;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding pending user by device ID',
        context: 'PostgresUserRepository.findPendingUser',
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId },
      });
      return null;
    }
  }

  /**
   * Check if a user with the given FCID exists and has the specified device ID
   */
  async findUniqueUser(fcid: string, deviceId: string): Promise<UserForJwt | null> {
    try {
      const user = await this.findByFcid(fcid);

      if (!user || !user.device_ids.includes(deviceId)) {
        return null;
      }

      return user;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding unique user',
        context: 'PostgresUserRepository.findUniqueUser',
        includeStack: true,
        includeRequest: false,
        metadata: { fcid, deviceId },
      });
      return null;
    }
  }

  /**
   * Get all users with the specified device ID (for constraint validation)
   */
  async findAllByDeviceId(deviceId: string): Promise<UserForJwt[]> {
    try {
      const users = await this.userRepository
        .createQueryBuilder('user')
        .where('user.type != :type', { type: UserType.MERGED })
        .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
        .getMany();

      return users.map(user => this.mapToUserForJwt(user));
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding all users by device ID',
        context: 'PostgresUserRepository.findAllByDeviceId',
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId },
      });
      return [];
    }
  }

  /**
   * Count users by type for a specific device ID
   */
  async countByDeviceIdAndType(deviceId: string, userType: UserType): Promise<number> {
    try {
      const count = await this.userRepository
        .createQueryBuilder('user')
        .where('user.type = :userType', { userType })
        .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
        .getCount();

      return count;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error counting users by device ID and type',
        context: 'PostgresUserRepository.countByDeviceIdAndType',
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId, userType },
      });
      return 0;
    }
  }

  /**
   * Validate that only one user (ANONYMOUS or PENDING) exists per device ID
   */
  async validateDeviceIdConstraint(deviceId: string): Promise<boolean> {
    try {
      const anonymousCount = await this.countByDeviceIdAndType(deviceId, UserType.ANONYMOUS);
      const pendingCount = await this.countByDeviceIdAndType(deviceId, UserType.PENDING);

      // Valid if: (0 or 1 ANONYMOUS) AND (0 or 1 PENDING) AND (not both)
      const hasAnonymous = anonymousCount > 0;
      const hasPending = pendingCount > 0;
      const hasMultipleAnonymous = anonymousCount > 1;
      const hasMultiplePending = pendingCount > 1;
      const hasBoth = hasAnonymous && hasPending;

      const isValid = !hasMultipleAnonymous && !hasMultiplePending && !hasBoth;

      if (!isValid) {
        this.logger.warn(
          `Device ID constraint violation for ${deviceId}: ` +
            `ANONYMOUS=${anonymousCount}, PENDING=${pendingCount}`,
        );
      }

      return isValid;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error validating device ID constraint',
        context: 'PostgresUserRepository.validateDeviceIdConstraint',
        includeStack: true,
        includeRequest: false,
        metadata: { deviceId },
      });
      return false;
    }
  }

  /**
   * Map database user entity to UserForJwt interface
   */
  private mapToUserForJwt(user: User): UserForJwt {
    return {
      fcid: user.fcid,
      type: user.type as UserType,
      identifiers: user.identifiers || {},
      device_ids: user.device_ids || [],
      created_at: user.created_at,
      updated_at: user.updated_at,
    };
  }
}
