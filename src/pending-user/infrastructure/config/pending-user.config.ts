import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Configuration class for pending user management.
 *
 * This class provides centralized configuration for Redis TTL settings,
 * lock timeouts, and other configurable parameters for the pending user system.
 */
@Injectable()
export class PendingUserConfig {
  constructor(private readonly configService: ConfigService) {}

  /**
   * TTL for pending users in Redis (in seconds)
   * Default: 24 hours (86400 seconds)
   */
  get pendingUserTtlSeconds(): number {
    return this.configService.get<number>('pendingUser.ttlSeconds', 86400);
  }

  /**
   * TTL for distributed locks (in milliseconds)
   * Default: 5 seconds (5000 ms)
   */
  get lockTtlMs(): number {
    return this.configService.get<number>('pendingUser.lockTtlMs', 5000);
  }

  /**
   * Redis key prefix for pending users
   * Default: 'pending:user:'
   */
  get pendingUserKeyPrefix(): string {
    return this.configService.get<string>('pendingUser.keyPrefix', 'pending:user:');
  }

  /**
   * Redis key prefix for FCID mappings
   * Default: 'pending:fcid:'
   */
  get fcidMappingKeyPrefix(): string {
    return this.configService.get<string>('pendingUser.fcidKeyPrefix', 'pending:fcid:');
  }

  /**
   * Redis key prefix for locks
   * Default: 'pending:lock:'
   */
  get lockKeyPrefix(): string {
    return this.configService.get<string>('pendingUser.lockKeyPrefix', 'pending:lock:');
  }

  /**
   * Whether to enable fallback to database when Redis is unavailable
   * Default: true
   */
  get enableRedisFallback(): boolean {
    return this.configService.get<boolean>('pendingUser.enableRedisFallback', true);
  }

  /**
   * Maximum number of retry attempts for Redis operations
   * Default: 3
   */
  get maxRetryAttempts(): number {
    return this.configService.get<number>('pendingUser.maxRetryAttempts', 3);
  }

  /**
   * Delay between retry attempts (in milliseconds)
   * Default: 1000 ms
   */
  get retryDelayMs(): number {
    return this.configService.get<number>('pendingUser.retryDelayMs', 1000);
  }

  /**
   * Whether to enable strict device ID constraint validation
   * Default: true
   */
  get enableStrictConstraints(): boolean {
    return this.configService.get<boolean>('pendingUser.enableStrictConstraints', true);
  }

  /**
   * Whether to enable cleanup of expired pending users
   * Default: true
   */
  get enableCleanup(): boolean {
    return this.configService.get<boolean>('pendingUser.enableCleanup', true);
  }

  /**
   * Interval for cleanup operations (in seconds)
   * Default: 1 hour (3600 seconds)
   */
  get cleanupIntervalSeconds(): number {
    return this.configService.get<number>('pendingUser.cleanupIntervalSeconds', 3600);
  }

  /**
   * Build a Redis key for pending user storage
   * @param deviceId The device ID
   * @returns Full Redis key
   */
  buildPendingUserKey(deviceId: string): string {
    return `${this.pendingUserKeyPrefix}${deviceId}`;
  }

  /**
   * Build a Redis key for FCID mapping
   * @param fcid The Firebase Client ID
   * @returns Full Redis key
   */
  buildFcidMappingKey(fcid: string): string {
    return `${this.fcidMappingKeyPrefix}${fcid}`;
  }

  /**
   * Build a Redis key for distributed locks
   * @param deviceId The device ID
   * @returns Full Redis key
   */
  buildLockKey(deviceId: string): string {
    return `${this.lockKeyPrefix}${deviceId}`;
  }
}
