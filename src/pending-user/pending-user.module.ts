import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Application Layer
import { PendingUserService } from './application/services/pending-user.service';

// Domain Layer - Use Cases
import { FindOrCreateUserForJwtUseCase } from './domain/use-cases/find-or-create-user-for-jwt.use-case';
import { CreatePendingUserUseCase } from './domain/use-cases/create-pending-user.use-case';

// Infrastructure Layer - Repositories
import { RedisPendingUserRepository } from './infrastructure/repositories/redis-pending-user.repository';
import { PostgresUserRepository } from './infrastructure/repositories/postgres-user.repository';

// Infrastructure Layer - Configuration
import { PendingUserConfig } from './infrastructure/config/pending-user.config';

// Repository Interfaces (for dependency injection)
import { IPendingUserRepository } from './domain/repositories/pending-user.repository.interface';
import { IUserRepository } from './domain/repositories/user.repository.interface';

// External Dependencies
import { RedisModule } from '../common/redis.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { DeviceId } from '../users/entities/device-id.entity';
import { ErrorLoggerService } from '../common/services/error-logger.service';

/**
 * Pending User Management Module
 *
 * This module provides a clean, modular architecture for pending user management
 * following Clean Architecture principles with clear separation of concerns.
 *
 * The module is designed to be reusable across projects and can be easily
 * extracted as a standalone library.
 */
@Module({
  imports: [ConfigModule, RedisModule, TypeOrmModule.forFeature([User, DeviceId])],
  providers: [
    // Configuration
    PendingUserConfig,

    // Application Services
    PendingUserService,

    // Domain Use Cases
    FindOrCreateUserForJwtUseCase,
    CreatePendingUserUseCase,

    // Infrastructure Repositories
    {
      provide: 'IPendingUserRepository',
      useClass: RedisPendingUserRepository,
    },
    {
      provide: 'IUserRepository',
      useClass: PostgresUserRepository,
    },

    // External Dependencies
    ErrorLoggerService,
  ],
  exports: [
    // Export the main service for use in other modules
    PendingUserService,

    // Export configuration for external use
    PendingUserConfig,

    // Export repository interfaces for testing
    'IPendingUserRepository',
    'IUserRepository',
  ],
})
export class PendingUserModule {}
