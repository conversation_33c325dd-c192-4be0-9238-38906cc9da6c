import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  DiskHealthIndicator,
  HealthCheckService,
  MemoryHealthIndicator,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';
import { Throttle } from '@nestjs/throttler';
import { throttleConfig } from '../config/throttle.config';
import { JwtAuthGuard } from '../guards/auth.guard';
import {
  DatabaseMetricsService,
  DatabasePerformanceReportWithConnectionPool,
} from '../common/services/database-metrics.service';
import { MaterializedViewRefreshService } from '../common/services/materialized-view-refresh.service';

/** The HealthController class checks the health of various components including the database, memory,
 and disk. */
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private databaseMetrics: DatabaseMetricsService,
    private materializedViewService: MaterializedViewRefreshService,
  ) {}

  /**
   * The function checks the health of various components including the database, memory, and storage.
   * @returns The `check()` function is returning the result of calling the `health.check()` method
   * with an array of functions as arguments. Each function in the array is a check for a different
   * aspect of the system's health, including the status of the database, memory usage, and disk
   * storage. The `health.check()` method will return a Promise that resolves with an array of objects
   * representing the results of each check
   */
  @Get()
  @Throttle(throttleConfig.health) // Use throttle configuration
  async check() {
    try {
      const result = await this.health.check([
        /* istanbul ignore next */
        () => this.db.pingCheck('database', { timeout: 10000 }),
        /* istanbul ignore next */
        () => this.memory.checkHeap('memory_heap', 7.5 * 1024 * 1024 * 1024),
        /* istanbul ignore next */
        () => this.memory.checkRSS('memory_rss', 7.5 * 1024 * 1024 * 1024),
        /* istanbul ignore next */
        () => this.disk.checkStorage('storage', { thresholdPercent: 0.9, path: '/' }),
      ]);
      return result;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }

  /**
   * Database performance metrics endpoint
   */
  @Get('database-metrics')
  @UseGuards(JwtAuthGuard)
  @Throttle(throttleConfig.health)
  async getDatabaseMetrics(): Promise<any> {
    try {
      return await this.databaseMetrics.generatePerformanceReport();
    } catch (error) {
      console.error('Database metrics check failed:', error);
      throw error;
    }
  }

  /**
   * Analyze database tables to update statistics
   */
  @Get('analyze-database')
  @UseGuards(JwtAuthGuard)
  @Throttle(throttleConfig.health)
  async analyzeDatabase() {
    try {
      await this.databaseMetrics.analyzeDatabase();
      return { message: 'Database analysis completed successfully' };
    } catch (error) {
      console.error('Database analysis failed:', error);
      throw error;
    }
  }

  /**
   * Connection pool performance metrics endpoint
   */
  @Get('connection-pool-metrics')
  @UseGuards(JwtAuthGuard)
  @Throttle(throttleConfig.health)
  async getConnectionPoolMetrics(): Promise<any> {
    try {
      const [poolStats, performanceMetrics, healthCheck] = await Promise.all([
        this.databaseMetrics.getConnectionPoolStats(),
        this.databaseMetrics.getConnectionPerformanceMetrics(),
        this.databaseMetrics.checkConnectionPoolHealth(),
      ]);

      return {
        poolStats,
        performanceMetrics,
        healthCheck,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Connection pool metrics check failed:', error);
      throw error;
    }
  }

  /**
   * Comprehensive database performance report including connection pool analysis
   */
  @Get('database-performance-report')
  @UseGuards(JwtAuthGuard)
  @Throttle(throttleConfig.health)
  async getDatabasePerformanceReport(): Promise<DatabasePerformanceReportWithConnectionPool> {
    try {
      const [performanceReport, poolStats, healthCheck] = await Promise.all([
        this.databaseMetrics.generatePerformanceReport(),
        this.databaseMetrics.getConnectionPoolStats(),
        this.databaseMetrics.checkConnectionPoolHealth(),
      ]);

      return {
        ...performanceReport,
        connectionPool: {
          stats: poolStats,
          health: healthCheck,
        },
        recommendations: this.generatePerformanceRecommendations(
          performanceReport,
          poolStats,
          healthCheck,
        ),
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Database performance report failed:', error);
      throw error;
    }
  }

  /**
   * Generate performance recommendations based on current metrics
   */
  private generatePerformanceRecommendations(
    performanceReport: any,
    poolStats: any,
    healthCheck: any,
  ): string[] {
    const recommendations: string[] = [];

    // Connection pool recommendations
    if (!healthCheck.isHealthy) {
      recommendations.push(...healthCheck.recommendations);
    }

    // Performance recommendations
    if (performanceReport.cacheHitRatio < 95) {
      recommendations.push(
        'Cache hit ratio is low. Consider increasing cache TTL or optimizing cache invalidation.',
      );
    }

    if (performanceReport.slowQueries.length > 0) {
      recommendations.push(
        `Found ${performanceReport.slowQueries.length} slow queries. Review and optimize query patterns.`,
      );
    }

    if (poolStats.activeConnections > poolStats.maxConnections * 0.8) {
      recommendations.push(
        'High connection pool utilization. Consider increasing max connections or optimizing query patterns.',
      );
    }

    // Connection latency recommendations
    if (performanceReport.slowQueries.some((q: any) => q.query.includes('pg.connect'))) {
      recommendations.push(
        'High pg.connect latency detected. Consider connection pool optimizations or network improvements.',
      );
    }

    return recommendations;
  }
}
