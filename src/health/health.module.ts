import { Modu<PERSON> } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HealthController } from './health.controller';
import { DatabaseMetricsService } from '../common/services/database-metrics.service';
import { User } from '../users/entities/user.entity';
import { WebhooksModule } from '../webhooks/webhooks.module';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    TerminusModule,
    WebhooksModule,
    TypeOrmModule.forFeature([User]),
    AuthModule,
    UsersModule,
  ],
  controllers: [HealthController],
  providers: [DatabaseMetricsService],
  exports: [DatabaseMetricsService],
})
export class HealthModule {}
