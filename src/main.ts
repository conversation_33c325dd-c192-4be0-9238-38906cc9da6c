import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import * as dotenv from 'dotenv';
import { json } from 'express';

import {
  ResponseInterceptor,
  GlobalExceptionFilter,
} from './common/filters/global-exception.filter';
import { WinstonLoggerService } from './common/services/winston-logger.service';
import { Config } from './config/interfaces/config.interface';
import { AppModule } from './app.module';
import { ErrorLoggerService } from './common/services/error-logger.service';
import { registerGlobalErrorHandlers } from './common/services/global-error-handlers';

dotenv.config();

if (process.env.ENABLE_SIGNOZ === 'true') {
  require('./tracing');
}

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { logger: false });

  const configService = app.get(ConfigService<Config>);
  const logger = app.get(WinstonLoggerService);
  const errorLogger = app.get(ErrorLoggerService);

  app.useLogger(logger);

  app.use(json({ limit: '2mb' }));

  // Register global error handlers (process-level and Express errors)
  registerGlobalErrorHandlers(app, errorLogger);

  app.enableCors();

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      enableDebugMessages: true,
    }),
  );

  app.useGlobalFilters(new GlobalExceptionFilter(errorLogger, configService));
  app.useGlobalInterceptors(new ResponseInterceptor());

  const port = configService.get('port');
  await app.listen(port);
  logger.log(`Roshi ENV ${configService.get('environment')} started on port ${port}`);
}
bootstrap();
