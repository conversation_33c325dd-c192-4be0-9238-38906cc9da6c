import { Injectable, NestMiddleware, ForbiddenException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { FORBIDDEN_USER_AGENT_MESSAGE } from '../filters/exception-message.constants';
import { parseUserAgent } from '../utils/user-agent.utils';

/**
 * Middleware to allow only requests with specific user-agent headers.
 * Allowed: iOS, Android, Vegeta (substring match).
 * Extracts and formats user agent information for use in the application.
 */
@Injectable()
export class UserAgentMiddleware implements NestMiddleware {
  private readonly allowedUserAgents: string[] = ['iOS', 'Android', 'Vegeta'];

  use(req: Request, res: Response, next: NextFunction): void {
    const userAgent = req.headers['user-agent'] || '';

    // Check if user agent matches allowed patterns
    const isAllowed = this.isValidUserAgent(userAgent);

    if (!isAllowed) {
      throw new ForbiddenException(FORBIDDEN_USER_AGENT_MESSAGE);
    }

    // Parse and attach user agent information to the request
    const parsedUserAgent = parseUserAgent(userAgent);
    req.userAgentInfo = parsedUserAgent;

    next();
  }

  /**
   * Validates if the user agent string matches allowed patterns.
   * @param userAgent - The user agent string to validate
   * @returns True if the user agent is valid, false otherwise
   */
  private isValidUserAgent(userAgent: string): boolean {
    // Start line, any number of spaces, followed by allowed user agents,
    // iOS and Android have a / after (version number split)
    return userAgent.match(/^(\s+)?(Android\/|iOS\/|Vegeta)/i) !== null;
  }
}
