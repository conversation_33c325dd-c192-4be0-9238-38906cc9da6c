import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { redisConfig } from '../../config/redis.config';
import { Redis } from 'ioredis';
import { ErrorLoggerService } from './error-logger.service';

@Injectable()
export class RedisService implements OnModuleInit {
  private readonly logger = new Logger(RedisService.name);
  private readonly redis: Redis;

  constructor(
    @InjectQueue('webhooks') private readonly webhookQueue: Queue,
    @InjectQueue('webhooks-dlq') private readonly dlqQueue: Queue,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    // Create Redis client with detailed configuration
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      connectTimeout: redisConfig.connectTimeout,
      commandTimeout: redisConfig.commandTimeout,
      retryStrategy: redisConfig.retryStrategy,
      enableAutoPipelining: redisConfig.enableAutoPipelining,
      maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
      enableOfflineQueue: redisConfig.enableOfflineQueue,
      enableReadyCheck: redisConfig.enableReadyCheck,
      lazyConnect: redisConfig.lazyConnect,
      showFriendlyErrorStack: redisConfig.showFriendlyErrorStack,
      ...(redisConfig.tls && {
        tls: {
          rejectUnauthorized: false,
          servername: redisConfig.host,
        },
      }),
    });

    this.redis.on('connect', () => {
      this.logger.log('Connected to Redis');
    });

    this.redis.on('error', error => {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis connection error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
    });

    this.redis.on('ready', () => {
      this.logger.log('Redis client ready');
    });

    this.redis.on('reconnecting', () => {
      this.logger.debug('Reconnecting to Redis...');
    });
  }

  async onModuleInit() {
    try {
      // Test the connection
      await this.redis.ping();
      this.logger.log('Successfully connected to Redis');

      // Log Redis connection details (without sensitive info)
      this.logger.log(
        `Redis connection details: host=${redisConfig.host}, port=${
          redisConfig.port
        }, tls=${!!redisConfig.tls}`,
      );

      if (redisConfig.tls) {
        this.logger.log('Redis TLS is enabled with SNI');
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis connection error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      this.errorLogger.logError(
        new Error(
          `Redis connection details: host=${redisConfig.host}, port=${
            redisConfig.port
          }, tls=${!!redisConfig.tls}`,
        ),
        undefined,
        {
          errorName: 'Redis connection error',
          context: 'RedisService',
          includeStack: true,
          includeRequest: false,
        },
      );

      // Don't throw the error to allow the application to start
      // but log detailed information for debugging
      if (error.message.includes('timeout')) {
        this.errorLogger.logError(
          new Error('Connection timed out. This could be due to:'),
          undefined,
          {
            errorName: 'Redis connection timeout',
            context: 'RedisService',
            includeStack: true,
            includeRequest: false,
          },
        );
      }

      // throw error; // Uncomment to prevent app from starting if Redis connection fails
    }
  }

  /**
   * Get the underlying ioredis client for advanced Redis operations
   */
  public getClient(): Redis {
    return this.redis;
  }

  // Cache operations
  async get(key: string): Promise<string | null> {
    try {
      return await this.redis.get(key);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis get error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.redis.setex(key, ttl, value);
      } else {
        await this.redis.set(key, value);
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis set error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis delete error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  // Queue operations
  async addToQueue(data: any, options?: any) {
    try {
      const job = await this.webhookQueue.add('process-webhook', data, {
        ...redisConfig.queue.defaultJobOptions,
        ...options,
      });
      this.logger.debug(`Added job ${job.id} to webhook queue`);
      return job;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis add to queue error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async getQueueStats() {
    try {
      const [active, completed, failed, delayed, waiting] = await Promise.all([
        this.webhookQueue.getActiveCount(),
        this.webhookQueue.getCompletedCount(),
        this.webhookQueue.getFailedCount(),
        this.webhookQueue.getDelayedCount(),
        this.webhookQueue.getWaitingCount(),
      ]);

      return {
        active,
        completed,
        failed,
        delayed,
        waiting,
      };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis get queue stats error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  // DLQ operations
  async addToDlq(data: any, options?: any) {
    try {
      const job = await this.dlqQueue.add('process-dlq', data, {
        ...options,
      });
      this.logger.debug(`Added job ${job.id} to DLQ`);
      return job;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis add to DLQ error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async getDlqStats() {
    try {
      const [active, completed, failed, delayed, waiting] = await Promise.all([
        this.dlqQueue.getActiveCount(),
        this.dlqQueue.getCompletedCount(),
        this.dlqQueue.getFailedCount(),
        this.dlqQueue.getDelayedCount(),
        this.dlqQueue.getWaitingCount(),
      ]);

      return {
        active,
        completed,
        failed,
        delayed,
        waiting,
      };
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis get DLQ stats error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async getDlqJobs(start = 0, end = 19) {
    try {
      const jobs = await this.dlqQueue.getJobs(['waiting', 'active', 'delayed', 'failed']);
      return jobs.slice(start, end + 1);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis get DLQ jobs error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { start, end },
      });
      throw error;
    }
  }

  async getDlqJob(jobId: string) {
    try {
      return await this.dlqQueue.getJob(jobId);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis get DLQ job error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId },
      });
      throw error;
    }
  }

  async retryDlqJob(jobId: string) {
    try {
      const job = await this.dlqQueue.getJob(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found in DLQ`);
      }

      // Add the job back to the main queue
      const originalData = job.data.originalData || job.data;
      const newJob = await this.webhookQueue.add('process-webhook', originalData, {
        ...redisConfig.queue.defaultJobOptions,
        jobId: `retry-${jobId}-${Date.now()}`,
      });

      // Mark the DLQ job as completed
      await job.moveToCompleted('Retried', true);

      this.logger.debug(`Retried job ${jobId} from DLQ, new job ID: ${newJob.id}`);
      return newJob;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis retry DLQ job error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId },
      });
      throw error;
    }
  }

  async removeDlqJob(jobId: string) {
    try {
      const job = await this.dlqQueue.getJob(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found in DLQ`);
      }

      await job.remove();
      this.logger.debug(`Removed job ${jobId} from DLQ`);
      return true;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis remove DLQ job error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { jobId },
      });
      throw error;
    }
  }

  async cleanupOldDlqJobs(maxAgeDays = 7) {
    try {
      const jobs = await this.dlqQueue.getJobs(['waiting', 'active', 'delayed', 'failed']);
      const now = new Date();
      const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
      let removedCount = 0;

      for (const job of jobs) {
        const jobAge = now.getTime() - job.timestamp;
        if (jobAge > maxAgeMs) {
          await job.remove();
          removedCount++;
        }
      }

      this.logger.debug(`Cleaned up ${removedCount} old jobs from DLQ`);
      return removedCount;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis clean up old DLQ jobs error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { maxAgeDays },
      });
      throw error;
    }
  }

  /**
   * Acquires an atomic lock for a given key with a TTL (in ms).
   * Returns true if the lock was acquired, false otherwise.
   */
  async acquireLock(key: string, ttlMs: number): Promise<boolean> {
    try {
      // Use positional arguments for NX and PX as per ioredis v5 signature
      this.logger.log(`Trying to acquire lock: ${key} for ${ttlMs}ms`);

      const result = await this.redis.set(key, 'locked', 'PX', ttlMs, 'NX');
      this.logger.log(`Lock result for ${key}: ${result}`);

      return result === 'OK';
    } catch (error) {
      this.logger.log(`Releasing lock: ${key}`);
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis acquire lock error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { key, ttlMs },
      });
      throw error;
    }
  }

  /**
   * Releases a lock for a given key.
   */
  async releaseLock(key: string): Promise<void> {
    try {
      this.logger.log(`[${Date.now()}] Releasing lock: ${key}`);

      await this.redis.del(key);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Redis release lock error',
        context: 'RedisService',
        includeStack: true,
        includeRequest: false,
        metadata: { key },
      });
      throw error;
    }
  }
}
