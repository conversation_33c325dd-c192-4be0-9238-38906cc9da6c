import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PostgresUserService } from './postgres-user.service';
import { ConfigService } from '@nestjs/config';
import { Config } from '../../config/interfaces/config.interface';

/**
 * Service responsible for maintaining and refreshing materialized views
 * for optimal database query performance
 */
@Injectable()
export class MaterializedViewRefreshService {
  private readonly logger = new Logger(MaterializedViewRefreshService.name);
  private readonly cronEnabled: boolean;

  constructor(
    private readonly postgresUserService: PostgresUserService,
    private readonly configService: ConfigService<Config>,
  ) {
    const cronConfig = this.configService.get<{ enabled: boolean }>('cron');
    this.cronEnabled = cronConfig?.enabled ?? false;
  }

  /**
   * Scheduled task to refresh merged users materialized view every 5 minutes
   * This ensures optimal performance for findMergedUser queries
   */
  @Cron(CronExpression.EVERY_5_MINUTES, { name: 'refreshMergedUsersMaterializedView' })
  async refreshMergedUsersMaterializedView(): Promise<void> {
    if (!this.cronEnabled) return;
    try {
      this.logger.log('🔄 Scheduled: Refreshing merged users materialized view...');
      await this.postgresUserService.refreshMergedUsersMaterializedView();
      this.logger.log('✅ Successfully refreshed merged users materialized view');
    } catch (error) {
      this.logger.error('Failed to refresh merged users materialized view', {
        error: error.message,
        stack: error.stack,
      });
    }
  }

  /**
   * Manual refresh method for immediate materialized view updates
   * Useful for testing or immediate performance improvements
   */
  async manualRefreshMergedUsersView(): Promise<void> {
    try {
      this.logger.log('🔄 Manual: Refreshing merged users materialized view...');
      await this.postgresUserService.refreshMergedUsersMaterializedView();
      this.logger.log('✅ Successfully refreshed merged users materialized view');
    } catch (error) {
      this.logger.error('Failed to manually refresh merged users materialized view', {
        error: error.message,
        stack: error.stack,
      });
      throw error; // Re-throw for manual operations to handle errors
    }
  }

  /**
   * Scheduled task to refresh all materialized views daily at 2 AM
   * This ensures all materialized views are up to date with minimal impact
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM, { name: 'refreshAllMaterializedViews' })
  async refreshAllMaterializedViews(): Promise<void> {
    if (!this.cronEnabled) return;
    try {
      this.logger.log('🔄 Scheduled: Refreshing all materialized views...');

      // Refresh merged users materialized view
      await this.postgresUserService.refreshMergedUsersMaterializedView();

      // Add other materialized view refreshes here as needed
      // await this.refreshOtherMaterializedViews();

      this.logger.log('✅ Successfully refreshed all materialized views');
    } catch (error) {
      this.logger.error('Failed to refresh all materialized views', {
        error: error.message,
        stack: error.stack,
      });
    }
  }

  /**
   * Health check method to verify materialized view status
   * Returns true if materialized view exists and is accessible
   */
  async checkMaterializedViewHealth(): Promise<boolean> {
    try {
      // Perform a simple, fast query to check if the materialized view is accessible
      await this.postgresUserService.query('SELECT 1 FROM mv_merged_users_lookup LIMIT 1;');
      return true;
    } catch (error) {
      this.logger.warn('Materialized view health check failed', {
        error: error.message,
      });
      return false;
    }
  }
}
