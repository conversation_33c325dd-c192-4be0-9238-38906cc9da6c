import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Not, Repository } from 'typeorm';

import { User, UserType } from '../../users/entities/user.entity';
import {
  CreateUserInputDto,
  DeviceIdentifiers,
  UsersEndpointInputDTO,
  UserResponseDto,
} from '../../users/user.dto';
import { UserNotFoundException } from '../../users/exceptions/user-not-found.exception';
import { DeviceId, DeviceType } from '../../users/entities/device-id.entity';
import { ErrorLoggerService } from './error-logger.service';
import { RedisService } from './redis.service';
import { FcidGenerator } from '../utils/fcid-generator.util';

interface UserUpdate {
  fcid: string;
  device_ids?: string[];
  merged_fcids?: string[];
  properties?: Record<string, any>;
  updated_at?: Date;
}

@Injectable()
export class PostgresUserService {
  private readonly CACHE_TTL = 1800; // 30 minutes in seconds
  private readonly NEGATIVE_CACHE_TTL = 60; // 1 minute for negative cache
  private readonly CACHE_PREFIX = 'user:';
  private readonly BATCH_SIZE = 100; // Batch size for bulk operations
  private readonly UPDATE_QUEUE_KEY = 'user-update-queue';
  private readonly logger: Logger;

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly redisService: RedisService,
    @InjectRepository(DeviceId)
    private readonly deviceIdRepository: Repository<DeviceId>,
    private readonly errorLogger: ErrorLoggerService,
  ) {
    this.logger = new Logger(PostgresUserService.name);
  }

  private removeNullValues<T extends object>(obj: T): Partial<T> {
    return Object.fromEntries(
      Object.entries(obj).filter(([_, value]) => value != null),
    ) as Partial<T>;
  }

  private createDeviceIdEntries(identifiers?: Partial<DeviceIdentifiers>): string[] {
    const entries: string[] = [];
    if (!identifiers) return entries;

    if (identifiers.idfa && Array.isArray(identifiers.idfa)) {
      entries.push(...identifiers.idfa);
    }
    if (identifiers.idfv && Array.isArray(identifiers.idfv)) {
      entries.push(...identifiers.idfv);
    }
    if (identifiers.gaid && Array.isArray(identifiers.gaid)) {
      entries.push(...identifiers.gaid);
    }
    if (identifiers.adid && Array.isArray(identifiers.adid)) {
      entries.push(...identifiers.adid);
    }
    return entries;
  }

  /**
   * Merges user identifiers while preserving immutable fields like installed_date
   * @param existingUser The existing user record
   * @param newUser The new user data to merge
   * @returns Merged user object with preserved immutable fields
   */
  private mergeUserIdentifiers(
    existingUser: User,
    newUser: UsersEndpointInputDTO | UserResponseDto,
  ): User {
    const mergedUser = { ...existingUser };

    // Keep existing fcid and installed_date as they're immutable
    // Only update fcaid if provided
    if (newUser.fcaid) {
      mergedUser.fcaid = newUser.fcaid;
      // If existing user does not have fcaid should change type
      if (!existingUser.fcaid) mergedUser.type = UserType.REGISTERED;
    }

    // Merge device identifiers
    const existingIds = new Set(existingUser.device_ids || []);
    const newIds = this.createDeviceIdEntries(newUser.identifiers);
    newIds.forEach(id => existingIds.add(id));
    mergedUser.device_ids = Array.from(existingIds);

    // Ensure identifiers are initialized
    if (!mergedUser.identifiers) {
      mergedUser.identifiers = {
        adid: [],
        gaid: [],
        idfa: [],
        idfv: [],
      };
    }

    // Merge specific identifiers
    mergedUser.identifiers = {
      adid: newUser.identifiers?.adid?.length
        ? Array.from(
            new Set([...(mergedUser.identifiers.adid || []), ...(newUser.identifiers?.adid || [])]),
          )
        : mergedUser.identifiers.adid || [],
      gaid: newUser.identifiers?.gaid?.length
        ? Array.from(
            new Set([...(mergedUser.identifiers.gaid || []), ...(newUser.identifiers?.gaid || [])]),
          )
        : mergedUser.identifiers.gaid || [],
      idfa: newUser.identifiers?.idfa?.length
        ? Array.from(
            new Set([...(mergedUser.identifiers.idfa || []), ...(newUser.identifiers?.idfa || [])]),
          )
        : mergedUser.identifiers.idfa || [],
      idfv: newUser.identifiers?.idfv?.length
        ? Array.from(
            new Set([...(mergedUser.identifiers.idfv || []), ...(newUser.identifiers?.idfv || [])]),
          )
        : mergedUser.identifiers.idfv || [],
    };

    return mergedUser;
  }

  private async checkExistingUserByIdentifiers(
    createUserDto: CreateUserInputDto,
  ): Promise<User | null> {
    // First check fcid as primary identifier
    if (createUserDto.fcid) {
      const user = await this.findByAttribute('fcid', createUserDto.fcid);
      if (user) return user;
    }

    // Then check fcaid
    if (createUserDto.fcaid) {
      const user = await this.findByAttribute('fcaid', createUserDto.fcaid);
      if (user) return user;
    }

    return null;
  }

  private getCacheKey(attribute: string, value: string): string {
    return `${this.CACHE_PREFIX}${attribute}:${value}`;
  }

  /**
   * Finds a user that has the given fcid in their merged_fcids array
   * Uses optimized materialized view for fast lookups with fallback to GIN index
   *
   * @param fcid - The fcid to search for in merged_fcids arrays
   * @returns User that contains the fcid in their merged_fcids, or null if not found
   */
  async findMergedUser(fcid: string): Promise<User | null> {
    return this.trackQuery('findMergedUser', async () => {
      // First try using the materialized view for fastest lookup
      try {
        const mergedUser = await this.userRepository
          .createQueryBuilder('user')
          .innerJoin(
            'mv_merged_users_lookup',
            'mv',
            'mv.fcid = user.fcid AND mv.merged_fcid = :fcid',
            { fcid },
          )
          .where('user.type != :type', { type: UserType.MERGED })
          .getOne();

        if (mergedUser) {
          return mergedUser;
        }
      } catch (error) {
        // If materialized view doesn't exist or fails, fall back to GIN index
        this.logger.debug('Materialized view lookup failed, falling back to GIN index', {
          fcid,
          error: error.message,
        });
      }

      // Fallback: Use optimized GIN index query with type filtering
      const mergedUser = await this.userRepository
        .createQueryBuilder('user')
        .where('user.type = :type', { type: UserType.REGISTERED })
        .andWhere('user.merged_fcids IS NOT NULL')
        .andWhere('array_length(user.merged_fcids, 1) > 0')
        .andWhere('user.merged_fcids @> ARRAY[:fcid]', { fcid })
        .getOne();

      return mergedUser;
    });
  }

  /**
   * Finds an anonymous user by device ID
   * Uses Redis caching for improved performance
   *
   * @param value - The device ID to search for
   * @returns Anonymous user with the given device ID, or null if not found
   */
  async findAnonUser(value: string): Promise<User | null> {
    return this.trackQuery('findAnonUser', async () => {
      const cacheKey = this.getCacheKey('device_ids', value);

      try {
        // Check Redis cache first
        const cachedUser = await this.getUserFromCache(cacheKey);
        if (cachedUser) {
          this.logger.debug(`Cache hit for ${cacheKey}`);
          // If cached user is anonymous, return it
          if (cachedUser.type === UserType.ANONYMOUS) {
            return cachedUser as User;
          }
          // If cached user is not anonymous (REGISTERED, MERGED, etc.),
          // there is no anonymous user for this device ID
          this.logger.debug(
            `Cached user is not anonymous (type: ${cachedUser.type}), returning null`,
          );
          return null;
        } else {
          this.logger.debug(`Cache miss or negative cache hit for ${cacheKey}`);
        }

        // Use GIN-optimized array contains operator for better performance
        // Specifically filter for ANONYMOUS users only
        const user = await this.userRepository
          .createQueryBuilder('user')
          .where("user.type = 'ANONYMOUS'")
          .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId: value })
          .getOne();

        if (user) {
          await this.cacheUser(user);
        } else {
          // Negative cache for not found
          await this.setNegativeCache(cacheKey);
        }
        return user;
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Error finding anonymous user',
          context: 'PostgresUserService.findAnonUser',
          includeStack: true,
          includeRequest: false,
          metadata: { deviceId: value },
        });
        return null;
      }
    });
  }

  /**
   * Finds a user with the given fcid and device_id combination
   * Uses optimized GIN index for fast lookups with proper filtering
   *
   * @param fcid - The fcid to search for
   * @param device_id - The device_id to search for in the user's device_ids array
   * @returns User that matches both fcid and device_id, or null if not found
   */
  async findUniqueUser(fcid: string, device_id: string): Promise<User | null> {
    return this.trackQuery('findUniqueUser', async () => {
      // Create a composite cache key using both fcid and device_id
      const cacheKey = this.getCacheKey('unique_user', `${fcid}:${device_id}`);

      try {
        // Check Redis cache first
        const cachedUser = await this.getUserFromCache(cacheKey);
        if (cachedUser) {
          this.logger.debug(`Cache hit for ${cacheKey}`);
          // If cached user matches both fcid and device_id, return it
          if (cachedUser.fcid === fcid && cachedUser.device_ids?.includes(device_id)) {
            return cachedUser as User;
          }
        } else {
          this.logger.debug(`Cache miss or negative cache hit for ${cacheKey}`);
        }

        // Use GIN-optimized array contains operator for better performance
        const user = await this.userRepository
          .createQueryBuilder('user')
          .where('user.fcid = :fcid', { fcid })
          .andWhere('user.device_ids @> ARRAY[:device_id]', { device_id })
          .getOne();

        if (user) {
          await this.cacheUser(user);
        } else {
          // Negative cache for not found
          await this.setNegativeCache(cacheKey);
        }
        return user;
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Error finding unique user',
          context: 'PostgresUserService.findUniqueUser',
          includeStack: true,
          includeRequest: false,
          metadata: { fcid, device_id },
        });
        return null;
      }
    });
  }

  async findByAttribute(attribute: keyof User, value: string): Promise<User | null> {
    const cacheKey = this.getCacheKey(attribute, value);

    try {
      const cachedUser = await this.getUserFromCache(cacheKey);
      if (cachedUser === null) {
        this.logger.debug(`Cache miss or negative cache hit for ${cacheKey}`);
        // Proceed to DB lookup
      } else {
        this.logger.debug(`Cache hit for ${cacheKey}`);
        if (typeof cachedUser === 'string') {
          return this.findByFcid(cachedUser);
        }
        return cachedUser as User;
      }

      // Handle device identifier array query, ignore MERGED users
      if (attribute === 'device_ids') {
        const user = await this.userRepository
          .createQueryBuilder('user')
          .select([
            'user.fcid',
            'user.fcaid',
            'user.type',
            'user.device_ids',
            'user.identifiers',
            'user.properties',
            'user.merged_fcids',
            'user.created_at',
            'user.updated_at',
            'user.installed_at',
            'user.event_control',
          ])
          .where('user.type != :type', { type: UserType.MERGED })
          .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId: value })
          .orderBy("CASE WHEN user.type = 'REGISTERED' THEN 0 ELSE 1 END", 'ASC')
          .addOrderBy('user.created_at', 'ASC')
          .getOne();

        if (user) {
          await this.cacheUser(user);
        } else {
          // Negative cache for not found
          await this.setNegativeCache(cacheKey);
        }
        return user;
      }

      // fcaid search should ignore MERGED users
      if (attribute === 'fcaid') {
        const user = await this.userRepository.findOne({
          where: { [attribute]: value, type: Not(UserType.MERGED) },
        });
        if (user) {
          await this.cacheUser(user);
        } else {
          // Negative cache for not found
          await this.setNegativeCache(cacheKey);
        }
        return user;
      }

      // Standard attribute query
      const user = await this.userRepository.findOne({
        where: { [attribute]: value },
      });

      if (user) {
        await this.cacheUser(user);
      } else {
        // Negative cache for not found
        await this.setNegativeCache(cacheKey);
      }
      return user;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding user by attribute',
        context: 'PostgresUserService.findByAttribute',
        fcid: value,
        includeStack: true,
        includeRequest: false,
        metadata: { attribute, value },
      });
      return null;
    }
  }

  /**
   * Generates a fcid if it's not provided
   * @param existingFcid The existing fcid
   * @param identifiers Optional device identifiers for deterministic generation
   * @returns The generated fcid using deterministic or random generation
   */
  private generateFcid(existingFcid?: string | null, identifiers?: DeviceIdentifiers): string {
    if (existingFcid) {
      return existingFcid;
    }

    // Use deterministic generation if device identifiers are available
    if (identifiers && this.hasDeviceIdentifiers(identifiers)) {
      return FcidGenerator.generateDeterministicFcid({
        adid: identifiers.adid?.[0],
        idfv: identifiers.idfv?.[0],
        idfa: identifiers.idfa?.[0],
        gaid: identifiers.gaid?.[0],
      });
    }

    // Fallback to random generation for backward compatibility
    return FcidGenerator.generateRandomFcid();
  }

  /**
   * Check if device identifiers contain any valid device IDs
   * @param identifiers Device identifiers object
   * @returns True if any device identifiers are present
   */
  private hasDeviceIdentifiers(identifiers: DeviceIdentifiers): boolean {
    return !!(
      identifiers.adid?.length ||
      identifiers.idfv?.length ||
      identifiers.idfa?.length ||
      identifiers.gaid?.length
    );
  }

  /**
   * Double-save device IDs to the new device_ids table
   */
  async doubleSaveDeviceIds(fcid: string, identifiers?: Partial<DeviceIdentifiers>): Promise<void> {
    if (!fcid || !identifiers) {
      this.logger.debug(
        `doubleSaveDeviceIds called with missing fcid or identifiers. fcid: ${fcid}, identifiers: ${JSON.stringify(
          identifiers,
        )}`,
      );
      return;
    }
    const now = new Date();
    const deviceTypes: { type: DeviceType; values?: string[] }[] = [
      { type: DeviceType.IDFA, values: identifiers.idfa },
      { type: DeviceType.IDFV, values: identifiers.idfv },
      { type: DeviceType.GAID, values: identifiers.gaid },
      { type: DeviceType.ADID, values: identifiers.adid },
    ];
    for (const { type, values } of deviceTypes) {
      if (!values) continue;
      for (const device_id of values) {
        if (!device_id) continue;
        // Check if already exists
        let device = await this.deviceIdRepository.findOne({
          where: { fcid, device_id, id_type: type },
        });
        try {
          device = await this.deviceIdRepository.findOne({
            where: { fcid, device_id, id_type: type },
          });
        } catch (error) {
          this.errorLogger.logError(error, undefined, {
            errorName: 'Error finding device in doubleSaveDeviceIds',
            context: 'PostgresUserService.doubleSaveDeviceIds',
            fcid,
            includeStack: true,
            includeRequest: false,
            metadata: { device_id, type },
          });
          continue; // Or handle the error as appropriate
        }
        if (!device) {
          device = this.deviceIdRepository.create({
            fcid,
            device_id,
            id_type: type,
            created_at: now,
            last_seen: now,
          });
        } else {
          await this.deviceIdRepository.update(
            { fcid, device_id, id_type: type },
            { last_seen: now },
          );
          continue;
        }
        await this.deviceIdRepository.save(device);
      }
    }
  }

  /**
   * Creates a new user or merges with existing user if identifiers match
   * @param createUserDto The user data for creation
   * @throws BadRequestException if installed_date is missing for new users
   * @returns Created or merged user
   */
  async createUser(dto: UsersEndpointInputDTO): Promise<User> {
    try {
      const createUserDto: CreateUserInputDto = dto as CreateUserInputDto;
      const existingUser = await this.checkExistingUserByIdentifiers(createUserDto);

      if (createUserDto.installed_at && !(createUserDto.installed_at instanceof Date)) {
        createUserDto.installed_at = new Date(createUserDto.installed_at);
      }

      if (existingUser) {
        // Merge identifiers and update existing user
        const mergedUser = this.mergeUserIdentifiers(existingUser, createUserDto);

        // Properly merge properties instead of overwriting
        if (createUserDto.properties) {
          mergedUser.properties = {
            ...mergedUser.properties,
            ...createUserDto.properties,
          };
        }

        // Keep original installed_at
        mergedUser.installed_at = existingUser.installed_at;

        const updatedUser = await this.userRepository.save(mergedUser);
        await this.cacheUser(updatedUser);
        try {
          await this.doubleSaveDeviceIds(updatedUser.fcid, createUserDto.identifiers);
        } catch (error) {
          this.errorLogger.logError(error, undefined, {
            errorName: 'Error in doubleSaveDeviceIds after user update',
            context: 'PostgresUserService.createUser',
            fcid: updatedUser.fcid,
            includeStack: true,
            includeRequest: false,
            metadata: { identifiers: createUserDto.identifiers },
          });
        }

        return updatedUser;
      }

      // Validate installed_date for new users
      if (!createUserDto.installed_at) {
        throw new BadRequestException('installed_at is required for new users');
      }

      // Create new user
      const cleanDto = this.removeNullValues(createUserDto) as CreateUserInputDto;
      const deviceIds = cleanDto.identifiers
        ? this.createDeviceIdEntries(cleanDto.identifiers)
        : [];

      // Generate fcid if not provided - use deterministic generation if identifiers are available
      const fcid = this.generateFcid(cleanDto.fcid, cleanDto.identifiers);

      const user = this.userRepository.create({
        ...cleanDto,
        fcid,
        fcaid: cleanDto.fcaid || undefined,
        device_ids: deviceIds,
        // created_at and updated_at will be handled by @CreateDateColumn and @UpdateDateColumn
      });

      const savedUser = await this.userRepository.save(user);
      await this.cacheUser(savedUser);
      try {
        await this.doubleSaveDeviceIds(savedUser.fcid, cleanDto.identifiers);
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Error in doubleSaveDeviceIds after user creation',
          context: 'PostgresUserService.createUser',
          fcid: savedUser.fcid,
          includeStack: true,
          includeRequest: false,
          metadata: { identifiers: cleanDto.identifiers },
        });
      }

      return savedUser;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error creating user',
        context: 'PostgresUserService.createUser',
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async updateUser(fcid: string, updateUserDto: UserResponseDto): Promise<User> {
    try {
      const user = await this.userRepository.findOne({
        where: { fcid },
      });

      if (!user) {
        // Instead of throwing, create a new user
        // Ensure installed_at is present, fallback to now if missing
        const installedAt = updateUserDto.installed_at
          ? new Date(updateUserDto.installed_at)
          : new Date();
        const createUserDto: CreateUserInputDto = {
          fcid: fcid,
          fcaid: updateUserDto.fcaid,
          identifiers: updateUserDto.identifiers,
          installed_at: installedAt,
          properties: updateUserDto.properties,
          type: updateUserDto.type,
        };
        return this.createUser(createUserDto);
      }

      // Clean update data and merge identifiers
      const cleanDto = this.removeNullValues(updateUserDto) as UserResponseDto;

      const mergedUser = this.mergeUserIdentifiers(user, {
        fcaid: cleanDto.fcaid || undefined,
        identifiers: cleanDto.identifiers,
        installed_at: user.installed_at, // Preserve original installed_date
      });

      // Properly merge properties instead of overwriting
      if (cleanDto.properties) {
        mergedUser.properties = {
          ...mergedUser.properties,
          ...cleanDto.properties,
        };
      }

      // Update user with remaining fields, ensuring merged_to is properly handled
      Object.assign(mergedUser, {
        ...cleanDto,
        properties: mergedUser.properties, // Preserve merged properties
        // Explicitly handle merged_to field to ensure it's not lost
        merged_to: cleanDto.merged_to !== undefined ? cleanDto.merged_to : mergedUser.merged_to,
      });

      const updatedUser = await this.userRepository.save(mergedUser);
      await this.invalidateUserCache(user);
      await this.cacheUser(updatedUser);
      try {
        await this.doubleSaveDeviceIds(updatedUser.fcid, cleanDto.identifiers);
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Error in doubleSaveDeviceIds after user update',
          context: 'PostgresUserService.updateUser',
          fcid: updatedUser.fcid,
          deviceId:
            cleanDto.identifiers?.idfa?.[0] ||
            cleanDto.identifiers?.idfv?.[0] ||
            cleanDto.identifiers?.gaid?.[0] ||
            cleanDto.identifiers?.adid?.[0],
          includeStack: true,
          includeRequest: false,
          metadata: { identifiers: cleanDto.identifiers },
        });
      }

      return updatedUser;
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error updating user',
        context: 'PostgresUserService.updateUser',
        fcid: fcid,
        includeStack: true,
        deviceId:
          updateUserDto.identifiers?.idfa?.[0] ||
          updateUserDto.identifiers?.idfv?.[0] ||
          updateUserDto.identifiers?.gaid?.[0] ||
          updateUserDto.identifiers?.adid?.[0],
        includeRequest: false,
      });
      throw error;
    }
  }

  async deleteUser(identifier: string): Promise<void> {
    try {
      const user = await this.userRepository.findOne({
        where: [{ fcid: identifier }, { fcaid: identifier }],
      });

      if (!user) {
        throw new UserNotFoundException(identifier);
      }

      // Delete user
      await this.userRepository.remove(user);
      // Clean up cache
      await this.invalidateUserCache(user);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error deleting user',
        context: 'PostgresUserService.deleteUser',
        fcid: identifier,
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  async deleteMergedUsers(fcid: string): Promise<void> {
    try {
      const users = await this.userRepository.find({
        where: [{ merged_to: fcid }],
      });
      for (const user of users) {
        // Delete user
        await this.userRepository.remove(user);
        // Clean up cache
        await this.invalidateUserCache(user);
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error finding merged users',
        context: 'PostgresUserService.deleteMergedUsers',
        fcid: fcid,
        includeStack: true,
        includeRequest: false,
      });
      throw error;
    }
  }

  private async invalidateUserCache(user: User): Promise<void> {
    const cacheKeys = [
      user.fcid && this.getCacheKey('fcid', user.fcid),
      (user.fcaid && this.getCacheKey('fcaid', user.fcaid)) || '',
      ...(user.device_ids || []).map(deviceId => this.getCacheKey('device_ids', deviceId)),
      // Invalidate merged_fcids cache keys for all FCIDs in the mergedFcids array
      ...(user.mergedFcids || []).map(mergedFcid => this.getCacheKey('merged_fcids', mergedFcid)),
      // Invalidate unique_user cache keys for all fcid:device_id combinations
      ...(user.device_ids || []).map(deviceId =>
        this.getCacheKey('unique_user', `${user.fcid}:${deviceId}`),
      ),
    ].filter(Boolean); // Remove undefined entries

    await Promise.all(cacheKeys.map(key => this.redisService.del(key)));
  }

  /**
   * Finds a user by their FCID
   * Uses Redis caching for improved performance
   *
   * @param fcid - The FCID to search for
   * @returns User with the given FCID, or null if not found
   */
  async findByFcid(fcid: string): Promise<User | null> {
    return this.trackQuery('findByFcid', async () => {
      const cacheKey = this.getCacheKey('fcid', fcid);

      try {
        // Check Redis cache first
        const cachedUser = await this.getUserFromCache(cacheKey);
        if (cachedUser) {
          this.logger.debug(`Cache hit for ${cacheKey}`);
          return cachedUser as User;
        } else {
          this.logger.debug(`Cache miss or negative cache hit for ${cacheKey}`);
        }

        // Query database
        const user = await this.userRepository.findOne({
          where: { fcid },
        });

        if (user) {
          await this.cacheUser(user);
        } else {
          // Negative cache for not found
          await this.setNegativeCache(cacheKey);
        }
        return user;
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Error finding user by FCID',
          context: 'PostgresUserService.findByFcid',
          includeStack: true,
          includeRequest: false,
          metadata: { fcid },
        });
        return null;
      }
    });
  }

  /**
   * Finds a user by device ID
   * Uses Redis caching for improved performance
   *
   * @param deviceId - The device ID to search for
   * @returns User with the given device ID, or null if not found
   */
  async findByDeviceId(deviceId: string): Promise<User | null> {
    return this.trackQuery('findByDeviceId', async () => {
      const cacheKey = this.getCacheKey('device_ids', deviceId);

      try {
        // Check Redis cache first
        const cachedUser = await this.getUserFromCache(cacheKey);
        if (cachedUser) {
          this.logger.debug(`Cache hit for ${cacheKey}`);
          // If cached user has the device_id, return it
          if (cachedUser.device_ids?.includes(deviceId)) {
            return cachedUser as User;
          }
        } else {
          this.logger.debug(`Cache miss or negative cache hit for ${cacheKey}`);
        }

        // Use GIN-optimized array contains operator for better performance
        // Ignore MERGED users as per existing logic
        const user = await this.userRepository
          .createQueryBuilder('user')
          .select([
            'user.fcid',
            'user.fcaid',
            'user.type',
            'user.device_ids',
            'user.identifiers',
            'user.properties',
            'user.merged_fcids',
            'user.created_at',
            'user.updated_at',
            'user.installed_at',
            'user.event_control',
          ])
          .where('user.type != :type', { type: UserType.MERGED })
          .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
          .orderBy("CASE WHEN user.type = 'REGISTERED' THEN 0 ELSE 1 END", 'ASC')
          .addOrderBy('user.created_at', 'ASC')
          .getOne();

        if (user) {
          await this.cacheUser(user);
        } else {
          // Negative cache for not found
          await this.setNegativeCache(cacheKey);
        }
        return user;
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Error finding user by device ID',
          context: 'PostgresUserService.findByDeviceId',
          includeStack: true,
          includeRequest: false,
          metadata: { deviceId },
        });
        return null;
      }
    });
  }

  async cacheUser(user: User): Promise<void> {
    const cachePromises: Promise<void>[] = [];

    // Serialize user data with error handling
    let serializedUser: string;
    try {
      serializedUser = JSON.stringify(user);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to serialize user for caching',
        context: 'PostgresUserService.cacheUser',
        includeStack: true,
        includeRequest: false,
      });
      // Return early without caching if serialization fails
      return;
    }

    if (user.fcid) {
      try {
        cachePromises.push(
          this.redisService.set(
            this.getCacheKey('fcid', user.fcid),
            serializedUser,
            this.CACHE_TTL,
          ),
        );
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Failed to cache user by fcid',
          context: 'PostgresUserService.cacheUser',
          fcid: user.fcid,
          includeStack: true,
          includeRequest: false,
        });
        // Continue with other cache operations
      }
    }

    if (user.fcaid) {
      try {
        cachePromises.push(
          this.redisService.set(
            this.getCacheKey('fcaid', user.fcaid),
            serializedUser,
            this.CACHE_TTL,
          ),
        );
      } catch (error) {
        this.errorLogger.logError(error, undefined, {
          errorName: 'Failed to cache user by fcaid',
          context: 'PostgresUserService.cacheUser',
          fcid: user.fcid,
          includeStack: true,
          includeRequest: false,
          metadata: { fcaid: user.fcaid },
        });
        // Continue with other cache operations
      }
    }

    if (user.device_ids) {
      for (const deviceId of user.device_ids) {
        try {
          cachePromises.push(
            this.redisService.set(
              this.getCacheKey('device_ids', deviceId),
              serializedUser,
              this.CACHE_TTL,
            ),
          );
        } catch (error) {
          this.errorLogger.logError(error, undefined, {
            errorName: 'Failed to cache user by device_id',
            context: 'PostgresUserService.cacheUser',
            fcid: user.fcid,
            includeStack: true,
            includeRequest: false,
            metadata: { deviceId },
          });
          // Continue with other cache operations
        }
      }
    }

    // Cache by mergedFcids for findMergedUser lookups
    if (user.mergedFcids) {
      for (const mergedFcid of user.mergedFcids) {
        cachePromises.push(
          this.redisService
            .set(this.getCacheKey('merged_fcids', mergedFcid), serializedUser, this.CACHE_TTL)
            .catch(error => {
              this.errorLogger.logError(error, undefined, {
                errorName: 'Failed to cache user by merged_fcid',
                context: 'PostgresUserService.cacheUser',
                fcid: user.fcid,
                includeStack: true,
                includeRequest: false,
                metadata: { mergedFcid },
              });
            }),
        );
      }
    }

    // Cache by unique_user combinations for findUniqueUser lookups
    if (user.fcid && user.device_ids) {
      for (const deviceId of user.device_ids) {
        cachePromises.push(
          this.redisService
            .set(
              this.getCacheKey('unique_user', `${user.fcid}:${deviceId}`),
              serializedUser,
              this.CACHE_TTL,
            )
            .catch(error => {
              this.errorLogger.logError(error, undefined, {
                errorName: 'Failed to cache user by unique_user combination',
                context: 'PostgresUserService.cacheUser',
                fcid: user.fcid,
                includeStack: true,
                includeRequest: false,
                metadata: { deviceId },
              });
            }),
        );
      }
    }

    try {
      await Promise.all(cachePromises);
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Failed to complete cache operations',
        context: 'PostgresUserService.cacheUser',
        includeStack: true,
        includeRequest: false,
      });
      // The operation continues even if caching fails
    }
  }

  async getUserFromCache(key: string): Promise<User | null> {
    try {
      const cachedUser = await this.redisService.get(key);
      if (!cachedUser || cachedUser === 'null') {
        return null;
      }
      try {
        return JSON.parse(cachedUser);
      } catch (parseError) {
        this.errorLogger.logError(parseError, undefined, {
          errorName: 'Failed to parse cached user data',
          context: 'PostgresUserService.getUserFromCache',
          includeStack: true,
          includeRequest: false,
          metadata: { key: key },
        });
        // If we can't parse the cached data, invalidate it
        await this.redisService.del(key);
        return null;
      }
    } catch (error) {
      this.errorLogger.logError(error, undefined, {
        errorName: 'Error retrieving user from cache',
        context: 'PostgresUserService.getUserFromCache',
        includeStack: true,
        includeRequest: false,
        metadata: { key: key },
      });
      return null;
    }
  }

  /**
   * Performance monitoring helper
   */
  private async trackQuery<T>(queryName: string, queryFn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      // Log slow queries (>100ms)
      if (duration > 100) {
        this.logger.debug(`Slow query detected: ${queryName} took ${duration}ms`);
      }
      // Log debug info for all queries
      this.logger.debug(`Query ${queryName} completed in ${duration}ms`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.errorLogger.logError(error, undefined, {
        errorName: 'Query failed',
        context: 'PostgresUserService.trackQuery',
        includeStack: true,
        includeRequest: false,
        metadata: { queryName, duration },
      });
      throw error;
    }
  }

  /**
   * Queue a user update in Redis for distributed batching
   * @param update UserUpdate object
   */
  async queueUserUpdate(update: UserUpdate): Promise<void> {
    const redis = this.redisService.getClient();
    await redis.lpush(this.UPDATE_QUEUE_KEY, JSON.stringify(update));
    const queueLength = await redis.llen(this.UPDATE_QUEUE_KEY);
    if (queueLength >= this.BATCH_SIZE) {
      await this.flushUpdateQueue();
    }
  }

  /**
   * Flush the user update queue from Redis and perform batch updates
   */
  async flushUpdateQueue(): Promise<void> {
    const redis = this.redisService.getClient();
    const updates: UserUpdate[] = [];
    for (let i = 0; i < this.BATCH_SIZE; i++) {
      const item = await redis.rpop(this.UPDATE_QUEUE_KEY);
      if (!item) break;
      updates.push(JSON.parse(item));
    }
    if (updates.length === 0) return;
    await this.trackQuery('batch_user_updates', async () => {
      const deviceIdUpdates = updates.filter(u => u.device_ids);
      const mergedFcidUpdates = updates.filter(u => u.merged_fcids);
      if (deviceIdUpdates.length > 0) {
        await this.batchUpdateDeviceIds(deviceIdUpdates);
      }
      if (mergedFcidUpdates.length > 0) {
        await this.batchUpdateMergedFcids(mergedFcidUpdates);
      }
    });
  }

  /**
   * Batch update device_ids for users
   * @param updates Array of UserUpdate objects with fcid and device_ids
   */
  private async batchUpdateDeviceIds(updates: UserUpdate[]): Promise<void> {
    const queryRunner = this.userRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      for (const update of updates) {
        await queryRunner.query(
          `UPDATE users SET device_ids = $1, updated_at = NOW() WHERE fcid = $2`,
          [update.device_ids, update.fcid],
        );
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Refreshes the merged users materialized view for optimal query performance
   * This should be called periodically or after significant user updates
   */
  async refreshMergedUsersMaterializedView(): Promise<void> {
    try {
      await this.userRepository.query('SELECT refresh_merged_users_lookup();');
    } catch (error) {
      this.logger.warn('Failed to refresh merged users materialized view', {
        error: error.message,
      });
      throw error; // Re-throw so callers can handle the failure
    }
  }

  /**
   * Batch update merged_fcids for users
   * @param updates Array of UserUpdate objects with fcid and merged_fcids
   */
  private async batchUpdateMergedFcids(updates: UserUpdate[]): Promise<void> {
    const queryRunner = this.userRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      for (const update of updates) {
        await queryRunner.query(
          `UPDATE users SET merged_fcids = $1, updated_at = NOW() WHERE fcid = $2`,
          [update.merged_fcids, update.fcid],
        );
      }
      await this.refreshMergedUsersMaterializedView();

      // Refresh materialized view after batch updates for optimal performance
      await this.refreshMergedUsersMaterializedView();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Returns the current size of the user update queue in Redis
   */
  async getUpdateQueueSize(): Promise<number> {
    const redis = this.redisService.getClient();
    return redis.llen(this.UPDATE_QUEUE_KEY);
  }

  private async setNegativeCache(cacheKey: string): Promise<void> {
    await this.redisService.set(cacheKey, 'null', this.NEGATIVE_CACHE_TTL);
  }

  /**
   * Finds all users (not MERGED) with a given device ID
   */
  async findAllByDeviceId(deviceId: string): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .where('user.type != :type', { type: UserType.MERGED })
      .andWhere('user.device_ids @> ARRAY[:deviceId]', { deviceId })
      .getMany();
  }

  /**
   * Executes a raw SQL query using the userRepository.
   * @param sql The SQL query string to execute
   * @returns The result of the query
   */
  public async query(sql: string): Promise<unknown> {
    return this.userRepository.query(sql);
  }
}
