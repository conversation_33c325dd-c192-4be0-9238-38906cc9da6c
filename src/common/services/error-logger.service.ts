import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { WinstonLoggerService } from './winston-logger.service';
import { parseUserAgent, ParsedUserAgent } from '../utils/user-agent.utils';

const SENSITIVE_FIELDS = ['token', 'auth', 'authorization', 'password', 'secret', 'x-api-key'];
/**
 * Options for customizing error logging behavior
 */
export interface ErrorLogOptions {
  /** Whether to include the error stack trace in the log */
  includeStack?: boolean;
  /** Whether to include request details in the log */
  includeRequest?: boolean;
  /** Custom context for the log entry */
  context?: string;
  /** Custom fcid to use instead of extracting from request */
  fcid?: string;
  /** Custom deviceId to use instead of extracting from request */
  deviceId?: string;
  /** Additional metadata to include in the log */
  metadata?: Record<string, unknown>;
  /** Custom error name/type to log */
  errorName?: string;
  /** Whether to include user agent information in the log */
  includeUserAgent?: boolean;
}

/**
 * Structured error log data
 */
export interface ErrorLogData {
  message: string;
  context?: string;
  fcid?: string;
  deviceId?: string;
  stacktrace?: string;
  request?: {
    method: string;
    url: string;
    headers: Record<string, unknown>;
    query: Record<string, unknown>;
    body: Record<string, unknown>;
  };
  userAgent?: ParsedUserAgent;
  exceptionType?: string;
  metadata?: Record<string, unknown>;
  errorName?: string;
}

/**
 * Custom error logger service that provides unified error logging format
 * with flexible options for including stack traces, request details, and fcid extraction.
 *
 * Note: This service is specifically for error logging. For regular application logs
 * (warn, info, debug), use the WinstonLoggerService directly to ensure proper
 * log level filtering and Signoz integration.
 */
@Injectable()
export class ErrorLoggerService {
  constructor(private readonly winstonLogger: WinstonLoggerService) {}

  /**
   * Logs an error with unified format and flexible options
   *
   * @param error - The error or exception to log
   * @param request - Optional Express request object for context extraction
   * @param options - Configuration options for what to include in the log
   */
  logError(error: Error | unknown, request?: Request, options: ErrorLogOptions = {}): void {
    const {
      includeStack = true,
      includeRequest = false,
      context = 'ErrorLogger',
      fcid,
      deviceId,
      metadata,
      errorName,
      includeUserAgent = true,
    } = options;

    const errorData: ErrorLogData = {
      message: this.extractErrorMessage(error),
      context,
      exceptionType: errorName || (error instanceof Error ? error.name : 'Error'),
    };

    // Add fcid - use provided fcid or extract from request
    errorData.fcid = fcid || (request ? this.extractFcidFromRequest(request) : undefined);

    // Add stack trace if requested
    if (includeStack && error instanceof Error && error.stack) {
      errorData.stacktrace = error.stack;
    }

    // Add request details if requested
    if (includeRequest && request) {
      errorData.request = this.buildRequestDetails(request);
    }

    // Add user agent information if requested and available
    if (includeUserAgent && request) {
      if (request.userAgentInfo) {
        errorData.userAgent = request.userAgentInfo;
      } else {
        const userAgent = request.headers['user-agent'];
        if (userAgent) {
          errorData.userAgent = parseUserAgent(userAgent);
        }
      }
    }

    if (metadata) {
      errorData.metadata = metadata;
    }

    if (deviceId) {
      errorData.deviceId = deviceId;
    }

    // Log using Winston logger
    this.winstonLogger.error(errorData);
  }

  /**
   * Convenience method for logging errors with stack trace and request details
   *
   * @param error - The error or exception to log
   * @param request - Express request object
   * @param options - Additional options
   */
  logErrorWithContext(
    error: Error | unknown,
    request: Request,
    options: Omit<ErrorLogOptions, 'includeStack' | 'includeRequest'> = {},
  ): void {
    this.logError(error, request, {
      ...options,
      includeStack: true,
      includeRequest: true,
    });
  }

  /**
   * Convenience method for logging errors with only the error message and fcid
   *
   * @param error - The error or exception to log
   * @param fcid - Optional fcid to include
   * @param options - Additional options
   */
  logSimpleError(
    error: Error | unknown,
    fcid?: string,
    options: Omit<ErrorLogOptions, 'includeStack' | 'includeRequest'> = {},
  ): void {
    this.logError(error, undefined, {
      ...options,
      fcid,
      includeStack: false,
      includeRequest: false,
    });
  }

  /**
   * Convenience method for logging errors with user agent information
   *
   * @param error - The error or exception to log
   * @param request - Express request object
   * @param options - Additional options
   */
  logErrorWithUserAgent(
    error: Error | unknown,
    request: Request,
    options: Omit<ErrorLogOptions, 'includeUserAgent'> = {},
  ): void {
    this.logError(error, request, {
      ...options,
      includeUserAgent: true,
    });
  }

  /**
   * Extracts fcid from various parts of the Express request
   *
   * @param request - Express request object
   * @returns The extracted fcid or undefined if not found
   */
  private extractFcidFromRequest(request: Request): string | undefined {
    // Check body
    if (request.body && typeof request.body === 'object' && 'fcid' in request.body) {
      return request.body.fcid;
    }

    // Helper to extract string from string | string[] | undefined
    const getString = (val: unknown): string | undefined => {
      if (typeof val === 'string') return val;
      if (Array.isArray(val) && val.length > 0 && typeof val[0] === 'string') return val[0];
      return undefined;
    };

    // Check query parameters
    if (request.query && typeof request.query === 'object' && 'fcid' in request.query) {
      return getString(request.query.fcid);
    }

    // Check headers
    if (request.headers && typeof request.headers === 'object' && 'fcid' in request.headers) {
      return getString(request.headers['fcid']);
    }

    // Check route parameters
    if (request.params && typeof request.params === 'object' && 'fcid' in request.params) {
      return getString(request.params.fcid);
    }

    return undefined;
  }

  /**
   * Builds request details object with sensitive data masked
   *
   * @param request - Express request object
   * @returns Sanitized request details
   */
  private buildRequestDetails(request: Request) {
    return {
      method: request.method,
      url: request.url,
      headers: this.maskSensitiveData(request.headers) as Record<string, unknown>,
      query: this.maskSensitiveData(request.query) as Record<string, unknown>,
      body: this.maskSensitiveData(request.body) as Record<string, unknown>,
    };
  }

  /**
   * Masks sensitive fields in objects to prevent logging sensitive information
   *
   * @param obj - Object to mask
   * @returns Object with sensitive fields masked
   */
  private maskSensitiveData(
    obj: Record<string, unknown> | unknown[] | unknown,
  ): Record<string, unknown> | unknown[] | unknown {
    if (Array.isArray(obj)) {
      return obj.map(item => this.maskSensitiveData(item));
    }
    if (!obj || typeof obj !== 'object') return obj;

    const masked: Record<string, unknown> = { ...obj };

    for (const key of Object.keys(masked)) {
      const value = masked[key];
      if (SENSITIVE_FIELDS.some(field => key.toLowerCase().includes(field))) {
        masked[key] = '[MASKED]';
      } else if (typeof value === 'object' && value !== null) {
        masked[key] = this.maskSensitiveData(value);
      }
    }

    return masked;
  }

  /**
   * Extracts error message from various error types
   *
   * @param error - Error or unknown object
   * @returns Error message string
   */
  private extractErrorMessage(error: Error | unknown): string {
    if (error instanceof Error) {
      return error.message;
    }

    if (typeof error === 'string') {
      return error;
    }

    if (error && typeof error === 'object' && 'message' in error) {
      return String((error as Error).message);
    }

    return 'Unknown error';
  }
}
