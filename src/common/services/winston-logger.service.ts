import { Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import { Config } from 'src/config/interfaces/config.interface';
import { OpenTelemetryTransportV3 } from '@opentelemetry/winston-transport';
import { getUnifiedLoggingConfig } from './logger-config.util';

@Injectable()
export class WinstonLoggerService implements LoggerService {
  private readonly logger: winston.Logger;

  /**
   * Common printf format for log messages.
   */
  private static readonly logPrintfFormat = (prefix?: string) =>
    winston.format.printf((info: winston.Logform.TransformableInfo) => {
      const ts = info.timestamp ? `[${info.timestamp}] ` : '';
      const pre = prefix ? `[${prefix}] ` : '';
      return `${ts}${pre}${info.level}: ${info.message}`;
    });

  /**
   * Common timestamp format for log messages.
   */
  private static readonly logTimestampFormat = winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  });

  constructor(private readonly configService: ConfigService<Config>) {
    const loggingConfig = this.configService.get('logging');
    const { level, prefix, colors, timestamp } = getUnifiedLoggingConfig(loggingConfig);

    // Format for console (with color)
    const consoleFormats = [];
    if (colors) consoleFormats.push(winston.format.colorize({ all: true }));
    if (timestamp) consoleFormats.push(WinstonLoggerService.logTimestampFormat);
    consoleFormats.push(WinstonLoggerService.logPrintfFormat(prefix));

    // Format for Signoz (no color)
    const signozFormats = [];
    if (timestamp) signozFormats.push(WinstonLoggerService.logTimestampFormat);
    signozFormats.push(WinstonLoggerService.logPrintfFormat(prefix));

    this.logger = winston.createLogger({
      level,
      transports: [
        /**
         * OpenTelemetry transport for Signoz observability
         * Configured with level 'warn' to ensure only error and warn level logs
         * are sent to Signoz for monitoring and alerting purposes
         */
        new OpenTelemetryTransportV3({
          level: 'warn', // Only send error and warn logs to Signoz
          format: winston.format.combine(...signozFormats),
        }),
        /**
         * Console transport for local development and debugging
         * Uses the configured log level from the application config
         */
        new winston.transports.Console({
          format: winston.format.combine(...consoleFormats),
        }),
      ],
    });

    winston.addColors({
      error: 'bold red',
      warn: 'yellow',
      info: 'green dim',
      debug: 'magenta',
      verbose: 'cyan',
    });
  }

  log(message: any, context?: string) {
    this.logger.info(message, { context });
  }

  error(message: any, trace?: string, context?: string) {
    this.logger.error(message, { trace, context });
  }

  warn(message: any, context?: string) {
    this.logger.warn(message, { context });
  }

  debug(message: any, context?: string) {
    this.logger.debug(message, { context });
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context });
  }
}
