import { Config } from '../../config/interfaces/config.interface';

export const NEST_ALLOWED_LEVELS = [
  'error',
  'warn',
  'log',
  'debug',
  'verbose',
  'fatal',
  'info',
] as const;

export function getUnifiedLoggingConfig(loggingConfig: Config['logging']): {
  level: string;
  logLevels: ('error' | 'warn' | 'log' | 'debug' | 'verbose' | 'fatal')[];
  prefix: string;
  colors: boolean;
  timestamp: boolean;
} {
  const level = loggingConfig?.levels?.[0] || 'info';
  const logLevels = (loggingConfig?.levels || []).filter(lvl =>
    NEST_ALLOWED_LEVELS.includes(lvl as any),
  ) as ('error' | 'warn' | 'log' | 'debug' | 'verbose' | 'fatal')[];
  const prefix = loggingConfig?.prefix || '';
  const colors = loggingConfig?.colors ?? true;
  const timestamp = loggingConfig?.timestamp ?? true;
  return { level, logLevels, prefix, colors, timestamp };
}
