import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';

interface QueryStats {
  query: string;
  calls: number;
  total_exec_time: number;
  mean_exec_time: number;
  rows: number;
}

interface IndexUsageStats {
  schemaname: string;
  tablename: string;
  indexname: string;
  idx_tup_read: number;
  idx_tup_fetch: number;
}

interface DatabasePerformanceReport {
  slowQueries: QueryStats[];
  indexUsage: IndexUsageStats[];
  cacheHitRatio: number;
  connectionCount: number;
  tableStats: {
    users: {
      rowCount: number;
      size: string;
    };
    webhooks: {
      rowCount: number;
      size: string;
    };
  };
}

export interface DatabasePerformanceReportWithConnectionPool extends DatabasePerformanceReport {
  connectionPool: {
    stats: any;
    health: any;
  };
  recommendations: string[];
  timestamp: string;
}

@Injectable()
export class DatabaseMetricsService {
  private readonly logger = new Logger(DatabaseMetricsService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Scheduled task to reset pg_stat_statements every 24 hours
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async scheduledResetQueryStats(): Promise<void> {
    this.logger.log('Scheduled: Resetting pg_stat_statements statistics');
    await this.resetQueryStats();
  }

  /**
   * Get slow queries from pg_stat_statements
   */
  async getSlowQueries(minDuration = 100): Promise<QueryStats[]> {
    try {
      // First check if pg_stat_statements extension is available
      const extensionCheck = await this.userRepository.query(`
        SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements';
      `);

      if (extensionCheck.length === 0) {
        this.logger.debug(
          'pg_stat_statements extension not available, skipping slow query analysis',
        );
        return [];
      }

      const result = await this.userRepository.query(
        `
        SELECT
          query,
          calls,
          total_exec_time,
          mean_exec_time,
          rows
        FROM pg_stat_statements
        WHERE mean_exec_time > $1
        ORDER BY total_exec_time DESC
        LIMIT 20;
      `,
        [minDuration],
      );

      return result;
    } catch (error) {
      this.logger.debug('pg_stat_statements not available, skipping slow query analysis');
      return [];
    }
  }

  /**
   * Get index usage statistics
   */
  async getIndexUsageStats(): Promise<IndexUsageStats[]> {
    try {
      const result = await this.userRepository.query(`
        SELECT
          schemaname,
          relname as tablename,
          indexrelname as indexname,
          idx_tup_read,
          idx_tup_fetch
        FROM pg_stat_user_indexes
        WHERE schemaname = 'public'
        AND relname IN ('users', 'webhooks')
        ORDER BY idx_tup_read DESC;
      `);

      return result;
    } catch (error) {
      this.logger.debug('Failed to get index usage stats, using fallback approach');
      // Fallback: just return basic index information
      try {
        const fallbackResult = await this.userRepository.query(`
          SELECT
            'public' as schemaname,
            t.relname as tablename,
            i.relname as indexname,
            0 as idx_tup_read,
            0 as idx_tup_fetch
          FROM pg_class t, pg_class i, pg_index ix
          WHERE t.oid = ix.indrelid
          AND i.oid = ix.indexrelid
          AND t.relname IN ('users', 'webhooks')
          ORDER BY t.relname, i.relname;
        `);
        return fallbackResult;
      } catch (fallbackError) {
        this.logger.debug('Fallback index stats also failed, returning empty array');
        return [];
      }
    }
  }

  /**
   * Get cache hit ratio
   */
  async getCacheHitRatio(): Promise<number> {
    try {
      const result = await this.userRepository.query(`
        SELECT
          ROUND(
            (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100,
            2
          ) as cache_hit_ratio
        FROM pg_statio_user_tables
        WHERE schemaname = 'public';
      `);

      return result[0]?.cache_hit_ratio || 0;
    } catch (error) {
      this.logger.error('Failed to get cache hit ratio', error.stack);
      return 0;
    }
  }

  /**
   * Get current connection count
   */
  async getConnectionCount(): Promise<number> {
    try {
      const result = await this.userRepository.query(`
        SELECT count(*) as connection_count
        FROM pg_stat_activity
        WHERE state = 'active';
      `);

      return parseInt(result[0]?.connection_count || '0');
    } catch (error) {
      this.logger.error('Failed to get connection count', error.stack);
      return 0;
    }
  }

  /**
   * Get table statistics
   */
  async getTableStats(): Promise<DatabasePerformanceReport['tableStats']> {
    try {
      const usersStats = await this.userRepository.query(`
        SELECT
          n_tup_ins + n_tup_upd + n_tup_del as row_count,
          pg_size_pretty(pg_total_relation_size('users')) as size
        FROM pg_stat_user_tables
        WHERE relname = 'users';
      `);

      const webhooksStats = await this.userRepository.query(`
        SELECT
          n_tup_ins + n_tup_upd + n_tup_del as row_count,
          pg_size_pretty(pg_total_relation_size('webhooks')) as size
        FROM pg_stat_user_tables
        WHERE relname = 'webhooks';
      `);

      return {
        users: {
          rowCount: parseInt(usersStats[0]?.row_count || '0'),
          size: usersStats[0]?.size || '0 bytes',
        },
        webhooks: {
          rowCount: parseInt(webhooksStats[0]?.row_count || '0'),
          size: webhooksStats[0]?.size || '0 bytes',
        },
      };
    } catch (error) {
      this.logger.error('Failed to get table stats', error.stack);
      return {
        users: { rowCount: 0, size: '0 bytes' },
        webhooks: { rowCount: 0, size: '0 bytes' },
      };
    }
  }

  /**
   * Generate comprehensive performance report
   */
  async generatePerformanceReport(): Promise<DatabasePerformanceReport> {
    const [slowQueries, indexUsage, cacheHitRatio, connectionCount, tableStats] = await Promise.all(
      [
        this.getSlowQueries(),
        this.getIndexUsageStats(),
        this.getCacheHitRatio(),
        this.getConnectionCount(),
        this.getTableStats(),
      ],
    );

    const report: DatabasePerformanceReport = {
      slowQueries,
      indexUsage,
      cacheHitRatio,
      connectionCount,
      tableStats,
    };

    // Log warnings for performance issues
    if (cacheHitRatio < 95) {
      this.logger.warn(`Low cache hit ratio: ${cacheHitRatio}%`);
    }

    if (slowQueries.length > 0) {
      this.logger.warn(`Found ${slowQueries.length} slow queries`);
    }

    if (connectionCount > 50) {
      this.logger.warn(`High connection count: ${connectionCount}`);
    }

    return report;
  }

  /**
   * Reset pg_stat_statements statistics
   */
  async resetQueryStats(): Promise<void> {
    try {
      // First check if pg_stat_statements extension is available
      const extensionCheck = await this.userRepository.query(`
        SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements';
      `);

      if (extensionCheck.length === 0) {
        this.logger.debug('pg_stat_statements extension not available, skipping reset');
        return;
      }

      await this.userRepository.query(`SELECT pg_stat_statements_reset();`);
      this.logger.log('Query statistics reset successfully');
    } catch (error) {
      this.logger.debug('pg_stat_statements not available, skipping reset');
    }
  }

  /**
   * Analyze tables to update statistics
   */
  async analyzeDatabase(): Promise<void> {
    try {
      await this.userRepository.query(`ANALYZE users;`);
      await this.userRepository.query(`ANALYZE webhooks;`);
      this.logger.log('Database analysis completed successfully');
    } catch (error) {
      this.logger.error('Failed to analyze database', error.stack);
    }
  }

  /**
   * Get connection pool statistics
   */
  async getConnectionPoolStats(): Promise<{
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    waitingConnections: number;
    maxConnections: number;
    minConnections: number;
  }> {
    try {
      const result = await this.userRepository.query(`
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections,
          count(*) FILTER (WHERE state = 'idle') as idle_connections,
          count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction
        FROM pg_stat_activity 
        WHERE datname = current_database();
      `);

      // Get pool configuration from TypeORM
      const dataSource = this.userRepository.manager.connection;
      const pool = (dataSource as any).driver?.master?._clients || [];
      const extra = this.userRepository.manager.connection.options.extra as {
        max: number;
        min: number;
      };

      return {
        totalConnections: parseInt(result[0]?.total_connections || '0'),
        activeConnections: parseInt(result[0]?.active_connections || '0'),
        idleConnections: parseInt(result[0]?.idle_connections || '0'),
        waitingConnections: parseInt(result[0]?.idle_in_transaction || '0'),
        maxConnections: extra.max,
        minConnections: extra.min,
      };
    } catch (error) {
      this.logger.error('Failed to get connection pool stats', error.stack);
      return {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        waitingConnections: 0,
        maxConnections: 30,
        minConnections: 10,
      };
    }
  }

  /**
   * Get real connection performance metrics from pg_stat_activity
   * - avgConnectionTime: average age of active connections (ms)
   * - maxConnectionTime: max age of active connections (ms)
   * - connectionErrors: count of connections in error state (if available)
   * - connectionTimeouts: count of connections terminated due to timeout (if available)
   */
  async getConnectionPerformanceMetrics(): Promise<{
    avgConnectionTime: number;
    maxConnectionTime: number;
    connectionErrors: number;
    connectionTimeouts: number;
  }> {
    try {
      // Get age of active connections (in ms)
      const result = await this.userRepository.query(`
        SELECT
          EXTRACT(EPOCH FROM (now() - backend_start)) * 1000 AS connection_age_ms
        FROM pg_stat_activity
        WHERE datname = current_database() AND state = 'active' AND backend_start IS NOT NULL;
      `);
      const ages = result.map((row: any) => parseFloat(row.connection_age_ms));
      const avgConnectionTime =
        ages.length > 0 ? ages.reduce((a: number, b: number) => a + b, 0) / ages.length : 0;
      const maxConnectionTime = ages.length > 0 ? Math.max(...ages) : 0;

      // Count connections in error/timeout states (Postgres doesn't expose this directly, so we use state_change and state)
      // For timeouts, count connections that are idle in transaction for a long time (e.g., > 5 min)
      const errorResult = await this.userRepository.query(`
        SELECT
          count(*) FILTER (WHERE state = 'idle in transaction' AND now() - state_change > interval '5 minutes') AS timeout_count
        FROM pg_stat_activity
        WHERE datname = current_database();
      `);
      const connectionTimeouts = parseInt(errorResult[0]?.timeout_count || '0');
      // Postgres does not track connection errors in pg_stat_activity, so we return 0
      const connectionErrors = 0;

      return {
        avgConnectionTime,
        maxConnectionTime,
        connectionErrors,
        connectionTimeouts,
      };
    } catch (error) {
      this.logger.error('Failed to get connection performance metrics', error.stack);
      return {
        avgConnectionTime: 0,
        maxConnectionTime: 0,
        connectionErrors: 0,
        connectionTimeouts: 0,
      };
    }
  }

  /**
   * Check for connection pool health issues
   */
  async checkConnectionPoolHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const poolStats = await this.getConnectionPoolStats();
    const issues: string[] = [];
    const recommendations: string[] = [];

    const exhaustionThreshold =
      this.configService.get<number>('database.connectionPool.exhaustionThreshold') || 0.9;
    const idleThreshold =
      this.configService.get<number>('database.connectionPool.idleThreshold') || 0.7;
    const utilizationThreshold =
      this.configService.get<number>('database.connectionPool.utilizationThreshold') || 0.8;

    // Check for connection pool exhaustion
    if (poolStats.activeConnections >= poolStats.maxConnections * exhaustionThreshold) {
      issues.push('Connection pool near capacity');
      recommendations.push('Consider increasing max connections or optimizing query patterns');
    }

    // Check for too many idle connections
    if (poolStats.idleConnections > poolStats.maxConnections * idleThreshold) {
      issues.push('Too many idle connections');
      recommendations.push('Consider reducing min connections or idle timeout');
    }

    // Check connection utilization
    const utilization = poolStats.activeConnections / poolStats.maxConnections;
    if (utilization > utilizationThreshold) {
      issues.push('High connection pool utilization');
      recommendations.push('Monitor for connection leaks and optimize query performance');
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations,
    };
  }
}
