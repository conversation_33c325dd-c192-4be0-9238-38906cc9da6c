import { INestApplication } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ErrorLoggerService } from './error-logger.service';

const ENTITY_TOO_LARGE_TYPE = 'entity.too.large';
const INVALID_JSON_PAYLOAD_MESSAGE = 'Invalid JSON payload';
const PAYLOAD_TOO_LARGE_MESSAGE = 'Payload too large';
const INTERNAL_SERVER_ERROR_MESSAGE = 'Internal server error';

/**
 * Registers global error handlers for process-level and Express errors.
 * Logs all error-level events using ErrorLoggerService.
 *
 * @param app - The NestJS application instance
 * @param errorLogger - The ErrorLoggerService instance
 */
export function registerGlobalErrorHandlers(
  app: INestApplication,
  errorLogger: ErrorLoggerService,
): void {
  // Process-level: Unhandled Promise Rejection
  process.on('unhandledRejection', async (reason: unknown) => {
    errorLogger.logError(reason, undefined, { context: 'UnhandledPromiseRejection' });
    try {
      await app.close();
    } finally {
      process.exit(1);
    }
  });
  // Process-level: Uncaught Exception
  process.on('uncaughtException', (err: Error) => {
    errorLogger.logError(err, undefined, { context: 'UncaughtException' });
    // It's unsafe to perform async operations in an 'uncaughtException' handler
    process.exit(1);
  });

  // Express error-handling middleware
  app.use((err: Error & { type?: string }, req: Request, res: Response, next: NextFunction) => {
    if (err instanceof SyntaxError && 'body' in err) {
      errorLogger.logError(err, req, { context: 'BodyParserError', includeRequest: true });
      res.status(400).json({ message: INVALID_JSON_PAYLOAD_MESSAGE });
    } else if (err.type === ENTITY_TOO_LARGE_TYPE) {
      errorLogger.logError(err, req, { context: 'PayloadTooLargeError', includeRequest: true });
      res.status(413).json({ message: PAYLOAD_TOO_LARGE_MESSAGE });
    } else if (err) {
      errorLogger.logError(err, req, { context: 'ExpressError', includeRequest: true });
      res.status(500).json({ message: INTERNAL_SERVER_ERROR_MESSAGE });
    } else {
      next();
    }
  });
}
