import { ConsoleLogger, ConsoleLoggerOptions } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Config } from '../../config/interfaces/config.interface';
import { getUnifiedLoggingConfig } from './logger-config.util';

export class ConsoleLoggerService extends ConsoleLogger {
  constructor(private readonly configService: ConfigService<Config>) {
    const loggingConfig = configService.get<Config['logging']>('logging') || {
      levels: [],
      prefix: '',
      colors: true,
      timestamp: true,
    };
    const { logLevels, colors, timestamp, prefix } = getUnifiedLoggingConfig(loggingConfig);
    const options: ConsoleLoggerOptions = {
      logLevels,
      colors,
      timestamp,
      prefix,
    };
    super(options);
  }

  error(message: any, stack?: string, context?: string) {
    const disableStacktrace = this.configService.get<boolean>('disableStacktraceLogging', false);
    // Only log the stack if not disabled
    super.error(message, disableStacktrace ? undefined : stack, context);
  }
}
