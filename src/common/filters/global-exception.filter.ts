import {
  Injectable,
  Catch,
  ArgumentsHost,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  NestInterceptor,
  ExecutionContext,
  CallHand<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { Config } from '../../config/interfaces/config.interface';
import {
  TOKEN_EXPIRED_MESSAGE,
  CANNOT_POST_REFRESH_MESSAGE,
  FORBIDDEN_USER_AGENT_MESSAGE,
  INVALID_FIREBASE_AUTH_MESSAGE_ANDROID,
  FIREBASE_ERROR_MESSAGE_PREFIX,
  INVALID_FIREBASE_AUTH_MESSAGE_IOS_SUBSTRING,
  JWT_EXPIRED_STRING,
} from './exception-message.constants';
import { ErrorLoggerService } from '../services/error-logger.service';

const SUPPRESSED_EXCEPTIONS = [
  { status: HttpStatus.UNAUTHORIZED, message: TOKEN_EXPIRED_MESSAGE },
  { status: HttpStatus.OK, message: CANNOT_POST_REFRESH_MESSAGE },
  { status: HttpStatus.FORBIDDEN, message: FORBIDDEN_USER_AGENT_MESSAGE },
  { status: HttpStatus.UNAUTHORIZED, message: INVALID_FIREBASE_AUTH_MESSAGE_ANDROID },
];

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => ({
        message: 'Success',
        statusCode: context.switchToHttp().getResponse().statusCode,
        data,
        timestamp: new Date().toISOString(),
      })),
    );
  }
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly disableStacktraceLogging: boolean;

  constructor(
    private readonly errorLogger: ErrorLoggerService,
    private readonly configService: ConfigService<Config>,
  ) {
    this.disableStacktraceLogging = this.configService.get<boolean>(
      'disableStacktraceLogging',
      false,
    );
  }

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status =
      exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException ? exception.getResponse() : 'Internal server error';

    const shouldSuppress =
      SUPPRESSED_EXCEPTIONS.some(config =>
        isSuppressedException(exception, status, message, config.status, config.message),
      ) ||
      isFirebaseError(message) ||
      isIosFirebaseAuthError(message) ||
      isJwtExpiredException(exception);

    if (!shouldSuppress) {
      // Use the new ErrorLoggerService for comprehensive error logging
      this.errorLogger.logError(exception, request, {
        context: 'GlobalExceptionFilter',
        includeStack: !this.disableStacktraceLogging,
        includeRequest: true,
        metadata: {
          status,
          exceptionType: exception?.constructor?.name,
        },
      });
    }

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message,
    });
  }
}

function isSuppressedException(
  exception: unknown,
  status: number,
  message: unknown,
  expectedStatus: number,
  expectedMessage: string,
): boolean {
  const isMessageMatch =
    (typeof message === 'string' && message === expectedMessage) ||
    (typeof message === 'object' &&
      message !== null &&
      'message' in message &&
      (message as { message: string }).message === expectedMessage);

  if (expectedStatus === HttpStatus.OK) {
    // Special case: CANNOT_POST_REFRESH_MESSAGE is only checked by message, not exception/status
    return isMessageMatch;
  }
  return exception instanceof HttpException && status === expectedStatus && isMessageMatch;
}

function isFirebaseError(message: unknown): boolean {
  if (typeof message === 'string') {
    return message.startsWith(FIREBASE_ERROR_MESSAGE_PREFIX);
  }

  if (typeof message === 'object' && message !== null && 'message' in message) {
    const messageStr = (message as { message: string }).message;
    return typeof messageStr === 'string' && messageStr.startsWith(FIREBASE_ERROR_MESSAGE_PREFIX);
  }

  return false;
}

function isIosFirebaseAuthError(message: unknown): boolean {
  if (typeof message === 'string') {
    return message.includes(INVALID_FIREBASE_AUTH_MESSAGE_IOS_SUBSTRING);
  }
  if (typeof message === 'object' && message !== null && 'message' in message) {
    const messageStr = (message as { message: string }).message;
    return (
      typeof messageStr === 'string' &&
      messageStr.includes(INVALID_FIREBASE_AUTH_MESSAGE_IOS_SUBSTRING)
    );
  }
  return false;
}

function isJwtExpiredException(exception: unknown): boolean {
  if (!exception || typeof exception !== 'object') {
    return false;
  }
  const { name, message } = exception as { name?: unknown; message?: unknown };
  if (typeof name === 'string' && name.toLowerCase() === JWT_EXPIRED_STRING) {
    return true;
  }
  if (typeof message === 'string' && message.toLowerCase().includes(JWT_EXPIRED_STRING)) {
    return true;
  }
  return false;
}
