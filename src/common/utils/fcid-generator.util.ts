import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

/**
 * Device identifiers interface for FCID generation
 */
export interface DeviceIdentifiersForFcid {
  adid?: string;
  idfv?: string;
  idfa?: string;
  gaid?: string;
  deviceId?: string;
}

/**
 * Utility class for generating Firebase Client IDs (FCIDs)
 *
 * This class provides both deterministic and random FCID generation methods
 * to ensure consistency between ANONYMOUS and PENDING users.
 */
export class FcidGenerator {
  /**
   * Generate a deterministic FCID based on device identifiers.
   * This ensures that the same device identifiers always produce the same FCID,
   * allowing ANONYMOUS and PENDING users to share the same unique FCID.
   *
   * @param identifiers Device identifiers object
   * @returns Deterministic FCID in format: uuid-XX (where XX is 2-digit suffix)
   */
  static generateDeterministicFcid(identifiers: DeviceIdentifiersForFcid): string {
    const bestId = this.getBestIdentifier(identifiers);

    // Hash the ID to get a deterministic but well-distributed seed
    const hash = crypto.createHash('sha256').update(bestId).digest('hex');

    // Use the hash to create a deterministic UUID (v4-like)
    const uuid = [
      hash.slice(0, 8),
      hash.slice(8, 12),
      '4' + hash.slice(13, 16), // Version 4
      ((parseInt(hash.slice(16, 17), 16) & 0x3) | 0x8).toString(16) + hash.slice(17, 20), // Variant: Set bits to 10xx for RFC 4122
      hash.slice(20, 32),
    ].join('-');

    // Make suffix deterministic too
    const suffixHash = crypto
      .createHash('sha256')
      .update(bestId + '-suffix')
      .digest('hex');
    const suffix = (parseInt(suffixHash.slice(0, 8), 16) % 100).toString().padStart(2, '0');

    return `${uuid}-${suffix}`;
  }

  /**
   * Generate a random FCID for cases where deterministic generation is not needed.
   * This maintains compatibility with the existing PostgresUserService.generateFcid method.
   *
   * @param existingFcid Optional existing FCID to return if provided
   * @returns Random FCID in format: uuid-XX (where XX is 2-digit suffix)
   */
  static generateRandomFcid(existingFcid?: string | null): string {
    return (
      existingFcid ??
      `${uuidv4()}-${Math.floor(Math.random() * 100)
        .toString()
        .padStart(2, '0')}`
    );
  }

  /**
   * Get the best available identifier for deterministic FCID generation.
   * Priority order: adid > idfv > idfa > gaid > deviceId
   *
   * @param identifiers Device identifiers object
   * @returns Best identifier string
   */
  private static getBestIdentifier(identifiers: DeviceIdentifiersForFcid): string {
    return (
      identifiers.adid ||
      identifiers.idfv ||
      identifiers.idfa ||
      identifiers.gaid ||
      identifiers.deviceId ||
      ''
    );
  }

  /**
   * Check if two sets of device identifiers would generate the same FCID.
   * This is useful for validation and testing purposes.
   *
   * @param identifiers1 First set of device identifiers
   * @param identifiers2 Second set of device identifiers
   * @returns True if both would generate the same FCID
   */
  static wouldGenerateSameFcid(
    identifiers1: DeviceIdentifiersForFcid,
    identifiers2: DeviceIdentifiersForFcid,
  ): boolean {
    const bestId1 = this.getBestIdentifier(identifiers1);
    const bestId2 = this.getBestIdentifier(identifiers2);
    return bestId1 === bestId2 && bestId1 !== '';
  }

  /**
   * Validate FCID format.
   * Expected format: xxxxxxxx-xxxx-4xxx-xxxx-xxxxxxxxxxxx-XX
   *
   * @param fcid FCID to validate
   * @returns True if FCID has valid format
   */
  static isValidFcidFormat(fcid: string): boolean {
    // UUID v4 pattern + 2-digit suffix
    const fcidPattern =
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}-\d{2}$/i;
    return fcidPattern.test(fcid);
  }
}
