const MILLIS_PER_YEAR = 1000 * 60 * 60 * 24 * 365.2425;
/**
 * Calculates the current age of the user based on install date and declared age.
 * Returns null if not enough data is present.
 */
export function calculateUserCurrentAge(
  installedAt: Date | string | undefined,
  userDeclaredAge: number | undefined,
  birthDate?: string,
): number | null {
  // If birthdate is available, calculate age based on birthdate
  if (birthDate) {
    try {
      const birth = new Date(birthDate);
      if (isNaN(birth.getTime())) return null;
      const now = new Date();
      let age = now.getUTCFullYear() - birth.getUTCFullYear();
      const monthDiff = now.getUTCMonth() - birth.getUTCMonth();
      // Adjust age if birthday hasn't occurred yet this year
      if (monthDiff < 0 || (monthDiff === 0 && now.getUTCDate() < birth.getUTCDate())) {
        age--;
      }
      return age;
    } catch (error) {
      return null;
    }
  }

  // Fallback to original calculation using install date and declared age
  if (!installedAt || userDeclaredAge == null) return null;
  const installedDate = typeof installedAt === 'string' ? new Date(installedAt) : installedAt;
  if (isNaN(installedDate.getTime())) return null;
  const timeDiff = Math.abs(new Date().getTime() - installedDate.getTime());
  const yearsDiff = Math.floor(timeDiff / MILLIS_PER_YEAR);
  return userDeclaredAge + yearsDiff;
}
