/**
 * Utility functions for detecting and handling Redis-related errors.
 *
 * This module provides robust error detection that relies on stable error codes
 * and type information rather than potentially changing error messages.
 */

/**
 * Check if an error is related to Redis connectivity
 *
 * This function uses a multi-layered approach to detect Redis-related errors:
 * 1. Error codes: Checks for specific Node.js network error codes
 * 2. Error types: Checks for Redis-specific error constructor names
 * 3. Fallback: Checks error messages for Redis-related keywords
 *
 * This approach is more robust than string matching alone and provides
 * better long-term maintainability as it relies on stable error codes
 * and type information rather than potentially changing error messages.
 *
 * Supported error codes:
 * - ECONNREFUSED: Connection refused
 * - ENOTFOUND: DNS resolution failed
 * - ETIMEDOUT: Connection timeout
 * - ECONNRESET: Connection reset by peer
 * - EHOSTUNREACH: Host unreachable
 * - ENETUNREACH: Network unreachable
 * - EADDRNOTAVAIL: Address not available
 *
 * Supported error types:
 * - ReplyError: Redis server replied with an error
 * - AbortError: Command was aborted
 * - RedisError: Generic Redis error
 *
 * @param error The error to check
 * @returns True if it's a Redis-related error
 */
export function isRedisError(error: any): boolean {
  if (!error) return false;

  // Check for specific error codes that indicate connectivity issues
  if (error.code) {
    const connectivityErrorCodes = [
      'ECONNREFUSED', // Connection refused
      'ENOTFOUND', // DNS resolution failed
      'ETIMEDOUT', // Connection timeout
      'ECONNRESET', // Connection reset by peer
      'EHOSTUNREACH', // Host unreachable
      'ENETUNREACH', // Network unreachable
      'EADDRNOTAVAIL', // Address not available
    ];

    if (connectivityErrorCodes.includes(error.code)) {
      return true;
    }
  }

  // Check for Redis-specific error types by constructor name
  if (error.constructor && error.constructor.name) {
    const redisErrorTypes = [
      'ReplyError', // Redis server replied with an error
      'AbortError', // Command was aborted
      'RedisError', // Generic Redis error
    ];

    if (redisErrorTypes.includes(error.constructor.name)) {
      return true;
    }
  }

  // Fallback: Check error message for Redis-related keywords
  // This is kept as a fallback for edge cases not covered by the above checks
  const errorMessage = error.message?.toLowerCase() || '';
  const redisErrorIndicators = ['redis', 'connection', 'timeout', 'network'];

  return redisErrorIndicators.some(indicator => errorMessage.includes(indicator));
}

/**
 * Check if an error is specifically a Redis connection error
 *
 * This is a more specific check that focuses on connection-related issues
 * rather than all Redis errors.
 *
 * @param error The error to check
 * @returns True if it's a Redis connection error
 */
export function isRedisConnectionError(error: any): boolean {
  if (!error) return false;

  // Check for connection-specific error codes
  if (error.code) {
    const connectionErrorCodes = [
      'ECONNREFUSED', // Connection refused
      'ENOTFOUND', // DNS resolution failed
      'ETIMEDOUT', // Connection timeout
      'ECONNRESET', // Connection reset by peer
      'EHOSTUNREACH', // Host unreachable
      'ENETUNREACH', // Network unreachable
      'EADDRNOTAVAIL', // Address not available
    ];

    if (connectionErrorCodes.includes(error.code)) {
      return true;
    }
  }

  // Check for connection-related error messages
  const errorMessage = error.message?.toLowerCase() || '';
  const connectionErrorIndicators = ['connection', 'timeout', 'network', 'refused', 'unreachable'];

  return connectionErrorIndicators.some(indicator => errorMessage.includes(indicator));
}

/**
 * Check if an error is a Redis timeout error
 *
 * @param error The error to check
 * @returns True if it's a Redis timeout error
 */
export function isRedisTimeoutError(error: any): boolean {
  if (!error) return false;

  // Check for timeout error code
  if (error.code === 'ETIMEDOUT') {
    return true;
  }

  // Check for timeout in error message
  const errorMessage = error.message?.toLowerCase() || '';
  return errorMessage.includes('timeout');
}
