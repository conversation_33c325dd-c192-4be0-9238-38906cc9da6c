export interface ModifiedPropertyDTO {
  affected_at: string;
  affected_property: string;
  affected_value: string;
}

export function toModifiedProperties(
  properties: Record<string, unknown>,
  timestamp: string,
): ModifiedPropertyDTO[] {
  const modified: ModifiedPropertyDTO[] = [];
  for (const key in properties) {
    modified.push({
      affected_at: timestamp,
      affected_property: key,
      affected_value: properties[key] as string,
    });
  }
  return modified;
}
