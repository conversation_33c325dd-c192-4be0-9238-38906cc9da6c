import { FcidGenerator } from './fcid-generator.util';

describe('FcidGenerator', () => {
  describe('generateDeterministicFcid', () => {
    it('should generate the same FCID for the same device identifiers', () => {
      const identifiers1 = {
        deviceId: 'test-device-123',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const identifiers2 = {
        deviceId: 'test-device-123',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const fcid1 = FcidGenerator.generateDeterministicFcid(identifiers1);
      const fcid2 = FcidGenerator.generateDeterministicFcid(identifiers2);

      expect(fcid1).toBe(fcid2);
      expect(FcidGenerator.isValidFcidFormat(fcid1)).toBe(true);
    });

    it('should generate different FCIDs for different device identifiers', () => {
      const identifiers1 = {
        deviceId: 'test-device-123',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const identifiers2 = {
        deviceId: 'test-device-456',
        idfv: '*************-4321-4321-************',
      };

      const fcid1 = FcidGenerator.generateDeterministicFcid(identifiers1);
      const fcid2 = FcidGenerator.generateDeterministicFcid(identifiers2);

      expect(fcid1).not.toBe(fcid2);
      expect(FcidGenerator.isValidFcidFormat(fcid1)).toBe(true);
      expect(FcidGenerator.isValidFcidFormat(fcid2)).toBe(true);
    });

    it('should prioritize identifiers correctly (adid > idfv > idfa > gaid > deviceId)', () => {
      const identifiersWithAdid = {
        deviceId: 'device-123',
        adid: '1234567890abcdef',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const identifiersWithOnlyIdfv = {
        deviceId: 'device-123',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const fcidWithAdid = FcidGenerator.generateDeterministicFcid(identifiersWithAdid);
      const fcidWithIdfv = FcidGenerator.generateDeterministicFcid(identifiersWithOnlyIdfv);

      // Should be different because adid takes priority over idfv
      expect(fcidWithAdid).not.toBe(fcidWithIdfv);
    });

    it('should generate FCID in correct format (UUID-XX)', () => {
      const identifiers = {
        deviceId: 'test-device',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const fcid = FcidGenerator.generateDeterministicFcid(identifiers);

      // Should match UUID v4 format + 2-digit suffix
      const fcidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}-\d{2}$/i;
      expect(fcid).toMatch(fcidPattern);
      expect(FcidGenerator.isValidFcidFormat(fcid)).toBe(true);
    });
  });

  describe('generateRandomFcid', () => {
    it('should generate different FCIDs on each call', () => {
      const fcid1 = FcidGenerator.generateRandomFcid();
      const fcid2 = FcidGenerator.generateRandomFcid();

      expect(fcid1).not.toBe(fcid2);
      expect(FcidGenerator.isValidFcidFormat(fcid1)).toBe(true);
      expect(FcidGenerator.isValidFcidFormat(fcid2)).toBe(true);
    });

    it('should return existing FCID if provided', () => {
      const existingFcid = 'existing-fcid-12345-67';
      const result = FcidGenerator.generateRandomFcid(existingFcid);

      expect(result).toBe(existingFcid);
    });

    it('should generate FCID in correct format', () => {
      const fcid = FcidGenerator.generateRandomFcid();

      // Should match UUID v4 format + 2-digit suffix
      const fcidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}-\d{2}$/i;
      expect(fcid).toMatch(fcidPattern);
    });
  });

  describe('wouldGenerateSameFcid', () => {
    it('should return true for identical identifiers', () => {
      const identifiers1 = {
        deviceId: 'test-device',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const identifiers2 = {
        deviceId: 'test-device',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      expect(FcidGenerator.wouldGenerateSameFcid(identifiers1, identifiers2)).toBe(true);
    });

    it('should return false for different identifiers', () => {
      const identifiers1 = {
        deviceId: 'test-device-1',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const identifiers2 = {
        deviceId: 'test-device-2',
        idfv: '*************-4321-4321-************',
      };

      expect(FcidGenerator.wouldGenerateSameFcid(identifiers1, identifiers2)).toBe(false);
    });

    it('should consider priority when comparing identifiers', () => {
      const identifiers1 = {
        deviceId: 'device-123',
        adid: '1234567890abcdef',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      const identifiers2 = {
        deviceId: 'device-123',
        idfv: '12345678-1234-1234-1234-123456789012',
      };

      // Should be false because adid takes priority in identifiers1
      expect(FcidGenerator.wouldGenerateSameFcid(identifiers1, identifiers2)).toBe(false);
    });
  });

  describe('isValidFcidFormat', () => {
    it('should validate correct FCID format', () => {
      const validFcids = [
        '12345678-1234-4567-8901-123456789012-00',
        'abcdef12-3456-4789-abcd-ef1234567890-99',
        '00000000-0000-4000-8000-000000000000-50',
      ];

      validFcids.forEach(fcid => {
        expect(FcidGenerator.isValidFcidFormat(fcid)).toBe(true);
      });
    });

    it('should reject invalid FCID formats', () => {
      const invalidFcids = [
        '12345678-1234-1567-8901-123456789012-00', // Wrong version (should be 4)
        '12345678-1234-4567-1901-123456789012-00', // Wrong variant (should be 8-b)
        '12345678-1234-4567-8901-123456789012-0', // Wrong suffix length
        '12345678-1234-4567-8901-123456789012', // Missing suffix
        'not-a-uuid-at-all', // Not a UUID
        '', // Empty string
      ];

      invalidFcids.forEach(fcid => {
        expect(FcidGenerator.isValidFcidFormat(fcid)).toBe(false);
      });
    });
  });

  describe('Integration test: ANON and PENDING users should have same FCID', () => {
    it('should generate the same FCID for ANON and PENDING users with same device identifiers', () => {
      // Simulate device identifiers that would be used for both ANON and PENDING users
      const deviceIdentifiers = {
        deviceId: 'shared-device-123',
        idfv: '12345678-1234-1234-1234-123456789012',
        adid: '1234567890abcdef',
      };

      // Generate FCID as if creating a PENDING user in Redis
      const pendingUserFcid = FcidGenerator.generateDeterministicFcid(deviceIdentifiers);

      // Generate FCID as if creating an ANON user in database
      const anonUserFcid = FcidGenerator.generateDeterministicFcid(deviceIdentifiers);

      // They should be identical
      expect(pendingUserFcid).toBe(anonUserFcid);
      expect(FcidGenerator.isValidFcidFormat(pendingUserFcid)).toBe(true);
    });
  });
});
