/**
 * User agent parsing utilities for extracting family and app version information.
 */

/**
 * Interface for parsed user agent information.
 */
export interface ParsedUserAgent {
  family: 'Android' | 'iOS' | 'Vegeta' | 'Unknown';
  appVersion: string | null;
  buildNumber: string | null;
}

/**
 * Parses a user agent string to extract family and app version information.
 * Supports formats like "Android/7.2.3 (757)" or "iOS/4.2.12 (757)".
 *
 * @param userAgent - The user agent string to parse
 * @returns Parsed user agent information with family, app version, and build number
 */
export function parseUserAgent(userAgent: string): ParsedUserAgent {
  if (!userAgent || typeof userAgent !== 'string') {
    return {
      family: 'Unknown',
      appVersion: null,
      buildNumber: null,
    };
  }

  // Handle Vegeta user agent (special case)
  if (userAgent === 'Vegeta') {
    return {
      family: 'Vegeta',
      appVersion: null,
      buildNumber: null,
    };
  }

  // Regex to match patterns like "Android/7.2.3 (757)" or "iOS/4.2.12 (757)" (case-insensitive)
  const userAgentRegex = /^(Android|iOS)\/([0-9]+\.[0-9]+\.[0-9]+)\s*\(([0-9]+)\)$/i;
  const match = userAgent.match(userAgentRegex);

  if (match) {
    const [, family, appVersion, buildNumber] = match;
    // Normalize family name to proper case
    const normalizedFamily = family.toLowerCase() === 'ios' ? 'iOS' : 'Android';
    return {
      family: normalizedFamily as 'Android' | 'iOS',
      appVersion,
      buildNumber,
    };
  }

  // Fallback: try to extract just the family if the format doesn't match exactly (case-insensitive)
  if (userAgent.toLowerCase().includes('android')) {
    return {
      family: 'Android',
      appVersion: null,
      buildNumber: null,
    };
  }

  if (userAgent.toLowerCase().includes('ios')) {
    return {
      family: 'iOS',
      appVersion: null,
      buildNumber: null,
    };
  }

  return {
    family: 'Unknown',
    appVersion: null,
    buildNumber: null,
  };
}

/**
 * Formats parsed user agent information into a standardized object.
 *
 * @param userAgent - The user agent string to parse and format
 * @returns Formatted user agent object with family and app_ver properties
 */
export function formatUserAgent(userAgent: string): { family: string; app_ver: string | null } {
  const parsed = parseUserAgent(userAgent);

  return {
    family: parsed.family,
    app_ver: parsed.appVersion,
  };
}

/**
 * Helper function to get user agent information from an Express request.
 * This function assumes the UserAgentMiddleware has been applied to the request.
 *
 * @param req - Express request object
 * @returns Parsed user agent information or null if not available
 */
export function getUserAgentInfo(req: any): ParsedUserAgent | null {
  return req.userAgentInfo || null;
}

/**
 * Helper function to get formatted user agent information from an Express request.
 * This function assumes the UserAgentMiddleware has been applied to the request.
 *
 * @param req - Express request object
 * @returns Formatted user agent object with family and app_ver properties
 */
export function getFormattedUserAgent(req: any): { family: string; app_ver: string | null } {
  const userAgentInfo = getUserAgentInfo(req);
  if (!userAgentInfo) {
    return { family: 'Unknown', app_ver: null };
  }

  return {
    family: userAgentInfo.family,
    app_ver: userAgentInfo.appVersion,
  };
}
