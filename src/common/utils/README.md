# Common Utilities

This directory contains shared utility functions that can be used across the application.

## Redis Error Utilities (`redis-error.util.ts`)

Provides robust error detection for Redis-related errors that relies on stable error codes and type information rather than potentially changing error messages.

### Functions

#### `isRedisError(error: any): boolean`

Checks if an error is related to Redis connectivity using a multi-layered approach:

1. **Error codes**: Checks for specific Node.js network error codes
2. **Error types**: Checks for Redis-specific error constructor names
3. **Fallback**: Checks error messages for Redis-related keywords

**Supported error codes:**

- `ECONNREFUSED`: Connection refused
- `ENOTFOUND`: DNS resolution failed
- `ETIMEDOUT`: Connection timeout
- `ECONNRESET`: Connection reset by peer
- `EHOSTUNREACH`: Host unreachable
- `ENETUNREACH`: Network unreachable
- `EADDRNOTAVAIL`: Address not available

**Supported error types:**

- `ReplyError`: Redis server replied with an error
- `AbortError`: Command was aborted
- `RedisError`: Generic Redis error

#### `isRedisConnectionError(error: any): boolean`

More specific check that focuses on connection-related issues rather than all Redis errors.

#### `isRedisTimeoutError(error: any): boolean`

Checks specifically for timeout-related errors.

### Usage Example

```typescript
import { isRedisError, isRedisConnectionError } from '../common/utils/redis-error.util';

try {
  await redis.get('key');
} catch (error) {
  if (isRedisError(error)) {
    // Handle Redis-specific error
    console.log('Redis error detected:', error.message);
  }

  if (isRedisConnectionError(error)) {
    // Handle connection-specific error
    console.log('Redis connection error:', error.message);
  }
}
```

### Benefits

- **More reliable**: Error codes and constructor names are more stable than error messages
- **Better performance**: Error code checking is faster than string matching
- **Future-proof**: Less likely to break when underlying libraries change error messages
- **Comprehensive**: Covers both network-level errors and Redis-specific errors
- **Maintainable**: Clear documentation makes it easy to understand and modify
