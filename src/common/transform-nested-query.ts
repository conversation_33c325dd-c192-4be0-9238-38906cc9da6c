import { Injectable, PipeTransform } from '@nestjs/common';

@Injectable()
export class TransformNestedQuery implements PipeTransform {
  transform(value: any) {
    const parsed: any = {};
    Object.keys(value).forEach(k => {
      if (k.includes('.')) {
        const [parent, child] = k.split('.');
        if (!parsed[parent]) parsed[parent] = {};
        parsed[parent][child] = [value[k]]; // should be an array only for identifiers
      } else {
        parsed[k] = value[k];
      }
    });
    return parsed;
  }
}
