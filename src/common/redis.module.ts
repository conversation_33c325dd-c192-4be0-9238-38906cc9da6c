import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { redisConfig } from '../config/redis.config';
import { RedisService } from './services/redis.service';

@Module({
  imports: [
    BullModule.forRoot({
      redis: {
        host: redisConfig.host,
        port: redisConfig.port,
        password: redisConfig.password,
        tls: redisConfig.tls
          ? {
              rejectUnauthorized: false,
              servername: redisConfig.host,
            }
          : undefined,
        retryStrategy: (times: number) => {
          const delay = Math.min(times * 50, 2000);
          return delay;
        },
      },
    }),
    BullModule.registerQueue(
      {
        name: 'webhooks',
        defaultJobOptions: {
          ...redisConfig.queue.defaultJobOptions,
          removeOnComplete: true,
          removeOnFail: false,
        },
      },
      {
        name: 'webhooks-dlq',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
        },
      },
      {
        name: 'geolocation',
        defaultJobOptions: {
          ...redisConfig.queue.defaultJobOptions,
          removeOnComplete: true,
          removeOnFail: false,
        },
      },
      {
        name: 'geolocation-dlq',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
        },
      },
    ),
  ],
  providers: [RedisService],
  exports: [RedisService, BullModule],
})
export class RedisModule {}
