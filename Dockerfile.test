# Multi-stage build that reuses the production stages from main Dockerfile
FROM node:20-alpine AS base

# Install system dependencies once
RUN apk --no-cache add curl tzdata postgresql-client

# Set working directory
WORKDIR /app

# Add build argument for GitHub token
ARG GH_AUTH_PACKAGE_TOKEN

# Copy package files for dependency caching
COPY package*.json ./
COPY .npmrc .npmrc

# Replace the token placeholder in .npmrc with the actual token
RUN sed -i "s/\${GH_AUTH_PACKAGE_TOKEN}/${GH_AUTH_PACKAGE_TOKEN}/g" .npmrc

# Install dependencies with npm cache mount
RUN --mount=type=cache,target=/root/.npm \
    npm ci --prefer-offline --no-audit

# Build stage
FROM base AS builder

# Copy source code and configuration files
COPY . .

# Build the application with cache mount
RUN --mount=type=cache,target=/app/.nest \
    npm run build

# Test stage - lightweight, just for running tests
FROM base AS test

# Copy necessary files for testing
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src
COPY --from=builder /app/test ./test
COPY --from=builder /app/jest.config.js ./
COPY --from=builder /app/tsconfig*.json ./

# Set environment variables
ENV NODE_ENV=test
ENV TZ=UTC

# Create test-results directory
RUN mkdir -p /app/test-results

# Default command runs tests only (app service handles migrations)
CMD ["sh", "-c", "echo 'Starting test container...' && echo 'Environment: NODE_ENV='$NODE_ENV && echo 'Working directory:' && pwd && echo 'Jest config exists:' && ls -la jest.config.js && echo 'Test directory exists:' && ls -la test/ && echo 'Waiting for app service to be ready...' && timeout 120 sh -c 'until curl -f http://app-test:3000/health; do sleep 2; done' && echo 'Running tests...' && NODE_ENV=test npm run test -- --json --outputFile=/app/test-results/jest_results.json"]