# Webhook Processing Overview

The system employs a batch processing strategy for handling incoming webhooks, primarily from FlipaClip. This approach offers several advantages, including improved efficiency, reduced load on downstream services, and better management of high event volumes.

## Key Features

- **Batching:** Webhook events are grouped into batches to minimize overhead and optimize processing. The maximum batch size is configurable (default: 100).
- **Sequential Processing:** Within each batch, events are processed sequentially to maintain the order of events and prevent race conditions.
- **Validation:** Each event undergoes thorough validation before processing to ensure data integrity and adherence to expected formats. This includes schema validation, timestamp checks, and provider-specific rules.
- **Retry Mechanism:** A robust retry mechanism with exponential backoff is implemented to handle transient errors and ensure reliable processing of events.
- **Error Handling:** Comprehensive error handling is in place to identify, log, and manage both validation and processing errors.
- **Observability:** The system is designed for observability, with features such as correlation IDs for request tracing, structured logging, and Prometheus metrics for monitoring performance and health.

## Processing Flow

1.  **Reception and Queuing:** Incoming webhooks are received and added to a processing queue (using Bull/Redis for persistence and reliability).
2.  **Job Dequeuing and Validation:** The `WebhookProcessor` dequeues webhook jobs from the queue. It then validates the data of each job against a predefined schema and performs provider-specific checks.
3.  **Sequential Processing:** Validated jobs are processed one at a time to maintain event order. The `WebhookProcessor` delegates the core processing logic to the `BatchWebhookService`.
4.  **Event Handling:** The `BatchWebhookService` determines the appropriate handler for each event type (e.g., `AdHandler`, `PaywallHandler`) and invokes it to process the event payload.
5.  **Data Persistence and Updates:** Handlers extract relevant information from the event and interact with repositories and other services (e.g., `UsersService`) to persist data and update user information as needed.
6.  **Error Management:** If errors occur during processing, the system attempts retries for transient failures. Non-retryable errors or failures after maximum retries are handled appropriately and logged.
7.  **Metrics Collection:** Throughout the processing pipeline, metrics are collected to monitor performance, identify bottlenecks, and track errors.

## Configuration

The batch processing behavior is controlled by the configuration in `config/batch.config.ts`, which includes settings for:

- Maximum batch size
- Processing timeout
- Retry attempts and backoff strategy

## Observability

The system provides several mechanisms for observability:

- **Correlation IDs:** Each webhook event is assigned a unique correlation ID for tracking across different services.
- **Structured Logging:** All log messages include consistent context, such as correlation ID, user identifier, event name, and provider.
- **Prometheus Metrics:** A range of metrics are collected and exposed via Prometheus, including processing duration, processed events, errors, retries, and revenue tracking.
- **Health Checks:** Health and metrics endpoints are available for monitoring system health and performance.
