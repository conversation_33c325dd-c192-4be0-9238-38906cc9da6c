# BatchWebhookService Documentation

The `BatchWebhookService` is responsible for handling webhooks in batches, specifically for FlipaClip events. **As of the latest update, it processes webhooks synchronously, immediately updating user properties and returning the actual modified_properties in the API response.**

## Key Responsibilities

1.  **Receiving and Processing Webhooks Synchronously:** Accepts incoming webhooks, validates them, and processes them immediately using the appropriate handler.
2.  **Immediate User Property Updates:** Updates user properties and persists webhook data in the database as part of the synchronous request.
3.  **Error Handling:** Manages errors during processing and returns detailed error information in the API response.
4.  **Interaction with Other Services:** Collaborates with handlers, repositories, and user services to process events, persist data, and update user information.
5.  **Metrics Collection:** Records metrics related to webhook processing, including duration, success/failure rates, and errors.

## Core Methods

### `processWebhook(webhook: Webhook, batchId?: string): Promise<Partial<Webhook> | null>`

This method is responsible for receiving a webhook and processing it synchronously.

**Parameters:**

- `webhook`: The webhook data to be processed. This should conform to the `Webhook` entity structure.
- `batchId` (optional): An identifier for a batch of webhooks, if applicable.

**Functionality:**

1.  Checks if the webhook is from Purchasely. If so, it skips it, as batch processing is not supported for Purchasely events.
2.  For FlipaClip events:
    - Determines the appropriate handler (AdHandler or PaywallHandler) based on the event name.
    - Processes the event synchronously using the handler, which updates user properties and returns the modified properties.
    - Saves the processed webhook data to the database.
    - Updates the user's properties, including `last_amplitude_device_id`.
    - Returns the processed webhook data, including the actual `modified_properties`.
3.  Logs information about the processing and records relevant metrics.
4.  If processing fails, logs the error and throws it to be handled by the controller.

### `processWebhookJob(webhook: WebhookJobData): Promise<void>`

> **Note:** This method is now deprecated in favor of synchronous processing. Previously, it was used by a worker to process jobs from the queue.

## Helper Methods

### `getHandlerForEvent(eventName: string): AdHandler | PaywallHandler`

Determines the correct handler based on the event name. Only handles FlipaClip events.

### `getWebhookPriority(webhook: Webhook): number`

Assigns a priority to the webhook job based on its event name. (This is now only relevant if you re-enable queue-based processing.)

### Logging Methods (`logInfo`, `logError`, `logWarning`)

These methods provide structured logging with context information, including:

- `correlationId`: A unique identifier for tracking the webhook.
- `fcid`: The FlipaClip user ID.
- `eventName`: The name of the webhook event.
- `provider`: The event provider (e.g., "FlipaClip").
- `batchId`: The ID of the batch, if applicable.

## Error Handling

The service manages errors during synchronous processing and returns detailed error information in the API response. If an error occurs, the batch response will indicate which events failed and why.

## Interaction with Other Services

The `BatchWebhookService` interacts with several other services:

- **Handlers (`AdHandler`, `PaywallHandler`):** These handlers are responsible for processing the event payload and extracting relevant information.
- **`webhookRepository`:** Used to persist webhook data in the database.
- **`UsersService`:** Used to retrieve and update user properties.
- **`MetricsService`:** Used to record metrics related to webhook processing.

## Metrics

The service uses the `MetricsService` to collect metrics about webhook processing. Key metrics include:

- **Processing Duration:** Tracks the time taken to process webhooks.
- **Processed Webhooks:** Counts the total number of webhooks processed, categorized by status (success, error).
- **Errors:** Counts the total number of errors encountered during processing, categorized by error type.
- **Revenue:** Tracks revenue generated from ad events.

These metrics can be used to monitor the health and performance of the webhook processing system.

## Important Considerations

- **Purchasely Events:** This service specifically handles FlipaClip events. Purchasely events are skipped and should be processed by a separate mechanism (likely the `SingleWebhookService`).
- **Event Ordering:** Within a batch, events are processed sequentially to maintain order and prevent race conditions.
- **Synchronous Processing:** The service now processes all events synchronously, returning the actual updated user properties in the API response. This is suitable for small to moderate batch sizes. For very large batches, consider the performance implications.
- **Error Handling:** The synchronous approach allows for immediate feedback on which events succeeded or failed, with detailed error information in the response.

This documentation provides a comprehensive overview of the `BatchWebhookService` as it now operates synchronously. For more detailed information, refer to the source code and related documentation for the handlers and other interacting services.
