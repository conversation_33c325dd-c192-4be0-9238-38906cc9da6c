# Webhook Dead Letter Queue (DLQ)

## Overview

The Dead Letter Queue (DLQ) is a mechanism for handling failed webhook batch processing events. When a webhook event fails to process after multiple retry attempts, it is moved to the DLQ for later analysis and potential reprocessing. This prevents data loss and provides a way to debug and recover from processing failures.

## Architecture

The DLQ implementation uses Redis and Bull queues:

1. **Main Queue (`webhooks`)**: Processes incoming webhook events
2. **Dead Letter Queue (`webhooks-dlq`)**: Stores failed events with error context

### Components

- **RedisService**: Provides methods to interact with Redis and Bull queues
- **WebhookProcessor**: Processes webhook events and moves failed jobs to the DLQ
- **DlqProcessor**: Logs DLQ entries but doesn't attempt to process them
- **DlqService**: Manages DLQ operations (view, retry, delete)

## How It Works

### Normal Flow

1. Webhook events are received via the API
2. Events are validated and added to the main queue
3. The WebhookProcessor processes events from the queue
4. Successfully processed events are removed from the queue

### Failure Handling

1. If processing fails, the job is retried based on configuration
2. For retryable errors, <PERSON> handles the retry logic
3. After max retries or for non-retryable errors, the job is moved to the DLQ
4. The original job data and error context are preserved in the DLQ

## Using the DLQ

### Viewing DLQ Messages

You can view messages in the DLQ using the DlqService. Here are some examples:

#### Using the API

The application provides REST API endpoints to manage the DLQ:

```bash
# Get all DLQ messages (paginated)
curl -X GET http://localhost:3000/webhooks/dlq?page=0&limit=20

# Get a specific DLQ message by ID
curl -X GET http://localhost:3000/webhooks/dlq/123

# Get DLQ statistics
curl -X GET http://localhost:3000/webhooks/dlq/stats
```

Note: These endpoints require authentication using the JWT Auth Guard.

#### Using the Redis CLI

```bash
# Connect to Redis
redis-cli-aws

# List all keys in the DLQ
KEYS bull:webhooks-dlq:*

# Get a specific job (replace JOB_ID with the actual job ID)
HGETALL bull:webhooks-dlq:JOB_ID
```

### Retrying DLQ Messages

To retry a message from the DLQ:

#### Using the API

```bash
# Retry a specific message
curl -X POST http://localhost:3000/webhooks/dlq/123/retry
```

#### Using the DlqService Programmatically

```typescript
// Inject the DlqService
constructor(private readonly dlqService: DlqService) {}

// Retry a job
async retryJob(jobId: string) {
  return await this.dlqService.retryJob(jobId);
}
```

### Cleaning the DLQ

To remove messages from the DLQ:

#### Using the API

```bash
# Delete a specific message
curl -X DELETE http://localhost:3000/webhooks/dlq/123
```

#### Using the Redis CLI

```bash
# Delete a specific job (replace JOB_ID with the actual job ID)
redis-cli-aws DEL bull:webhooks-dlq:JOB_ID

# Clear the entire DLQ (CAUTION: This will delete all DLQ messages)
redis-cli-aws KEYS "bull:webhooks-dlq:*" | xargs redis-cli-aws DEL
```

#### Implementing Bulk Delete

This would allow you to clear the entire DLQ with:

```bash
curl -X DELETE http://localhost:3000/webhooks/dlq
```

#### Using the DlqService Programmatically

```typescript
// Delete a specific job
async deleteJob(jobId: string) {
  return await this.dlqService.deleteJob(jobId);
}
```

## Monitoring

### DLQ Statistics

You can get statistics about the DLQ using the DlqService:

```typescript
// Get DLQ stats
async getStats() {
  return await this.dlqService.getStats();
}
```

This returns information about:

- Active jobs
- Completed jobs
- Failed jobs
- Delayed jobs
- Waiting jobs

### Logging

The DLQ implementation includes extensive logging:

- When a job is moved to the DLQ
- When a job is retried from the DLQ
- When a job is removed from the DLQ
- Error details for failed jobs

## Configuration

The DLQ uses the same Redis configuration as the main queue. The retry logic is configured in `batchConfig`:

```typescript
// src/config/batch.config.ts
export const batchConfig = {
  // ...
  retry: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      initialDelay: 5000, // 5 seconds
    },
  },
};
```

## Error Analysis

The DLQ stores detailed error information to help with debugging:

- Error message
- Error stack trace
- Error name
- Original job data
- Number of retry attempts
- Timestamp when moved to DLQ

## Monitoring and Alerting

### Automated Tasks

The DLQ system includes two automated tasks that help maintain and monitor the queue:

#### DLQ Cleanup Task

The `DlqCleanupTask` runs daily at midnight to prevent the DLQ from growing indefinitely:

```typescript
@Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
async cleanupOldJobs() {
  // Removes jobs older than 7 days
  // Logs cleanup statistics
}
```

Key features:

- Runs automatically every day at midnight
- Removes jobs that are older than 7 days
- Maintains a log of removed jobs and any errors
- Prevents Redis memory issues from old jobs accumulating

#### DLQ Monitor Task

The `DlqMonitorTask` provides hourly monitoring and alerting:

```typescript
@Cron(CronExpression.EVERY_HOUR)
async monitorDlqSize() {
  // Checks DLQ size against threshold
  // Sends alerts if threshold exceeded
}
```

Key features:

- Runs automatically every hour
- Monitors the total number of jobs in the DLQ
- Alerts when jobs exceed threshold (default: 50 jobs)
- Sends detailed Slack notifications including:
  - Current DLQ size
  - Details of 5 most recent failed jobs
  - Error messages and timestamps
  - Original job IDs for tracking

Example Slack notification:

```
🚨 ALERT: DLQ has 75 jobs, exceeding threshold of 50

Recent DLQ Jobs:
• ID: 123
  Error: Failed to process subscription
  Time: 2024-03-15T10:30:00Z
  Original Job: webhook-456

[... more jobs ...]

🕐 Alert triggered at 2024-03-15T11:00:00Z
```

### Configuration

Both tasks can be configured through environment variables:

```env
# DLQ Monitor configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# DLQ Cleanup configuration (optional, defaults shown)
DLQ_MAX_AGE_DAYS=7
```

### Metrics Collection

For more advanced monitoring, you can collect metrics about the DLQ:

1. **Job Age**: Track how long jobs have been in the DLQ
2. **Error Types**: Categorize errors to identify patterns
3. **Retry Success Rate**: Track how many retried jobs succeed
4. **Provider Distribution**: Monitor which webhook providers have the most failures

These metrics can be exported to monitoring systems like Prometheus or sent to logging platforms for visualization.

## Troubleshooting

### Common Issues

1. **Connection Issues**: If Redis connection fails, check your Redis configuration
2. **Serialization Errors**: Ensure webhook data can be properly serialized/deserialized
3. **Memory Issues**: If Redis is running out of memory, consider cleaning up old jobs

### Debugging Steps

1. Check the logs for detailed error messages
2. Examine the error context in the DLQ entry
3. Try to reproduce the issue with the original data
4. Check for any recent code changes that might affect processing

## Implementation Details

The DLQ implementation is based on Bull queues and Redis. Key files:

- `src/common/redis.module.ts`: Defines the queues
- `src/common/services/redis.service.ts`: Provides queue operations
- `src/webhooks/processors/webhook.processor.ts`: Handles job processing and failures
- `src/webhooks/processors/dlq.processor.ts`: Processes DLQ entries
- `src/webhooks/services/dlq.service.ts`: Manages DLQ operations
