# WebhookProcessor Documentation

The `WebhookProcessor` is a NestJS processor responsible for handling webhook jobs from the 'webhooks' queue. It validates incoming webhooks, processes them sequentially, and manages retries for transient errors.

## Key Responsibilities

1.  **Receiving and Validating Webhook Jobs:** Dequeues webhook jobs from the 'webhooks' queue and validates their data against a predefined schema.
2.  **Sequential Processing:** Processes valid webhook jobs one at a time to maintain event order.
3.  **Error Handling and Retries:** Manages errors during processing, implementing a retry mechanism for transient failures and handling non-retryable errors appropriately.
4.  **Interaction with BatchWebhookService:** Delegates the core processing logic to the `BatchWebhookService`.
5.  **Queue Event Handling:** Handles queue-level events such as errors and job failures, logging relevant information.

## Core Methods

### `processWebhook(job: Job<Webhook>): Promise<ProcessingResult>`

This method is the main entry point for processing a webhook job from the queue.

**Parameters:**

- `job`: A `Job` object from the Bull queue, containing the webhook data (`Webhook`) and metadata.

**Returns:**

A `Promise` that resolves to a `ProcessingResult` object, indicating the success or failure of the processing and providing additional information.

**Functionality:**

1.  **Initialization Check:** Ensures the `WebhookProcessor` is properly initialized.
2.  **Validation:**
    - Validates the webhook data using the `validateWebhook` method against the `baseEventSchema`.
    - If validation fails, logs a warning and returns a `ProcessingResult` with `success: false` and an error message.
3.  **Sequential Processing:**
    - Calls the `processWebhookJob` method of the `BatchWebhookService` to handle the core processing logic.
    - Updates the job progress to 100% upon successful completion.
    - Returns a `ProcessingResult` with `success: true` and the job ID and event ID.
4.  **Error Handling:**
    - Includes a nested `try...catch` block to handle errors during the processing phase.
    - **Retryable Errors:** If an error is considered retryable (based on `isRetryableError`) and the maximum number of attempts has not been reached, it logs a warning and re-throws the error, allowing Bull to handle the retry.
    - **Non-Retryable Errors:** If an error is non-retryable or the maximum attempts are reached, it logs an error, moves the job to the 'failed' state in the queue, and returns a `ProcessingResult` with `success: false` and the error message.
5.  **Unexpected Errors:** Includes a top-level `try...catch` to handle any unexpected errors during the entire process, logging the error and re-throwing it.

### `validateWebhook(webhook: Webhook): Promise<string | null>`

This method validates the webhook data against the `baseEventSchema` and performs additional provider-specific validations.

**Parameters:**

- `webhook`: The `Webhook` data to be validated.

**Returns:**

A `Promise` that resolves to:

- `null` if the webhook is valid.
- An error message string if validation fails.

**Functionality:**

1.  **Schema Validation:**
    - Converts the `Webhook` entity to a base event format suitable for schema validation.
    - Uses the `jsonschema` validator to validate the base event against the `baseEventSchema`.
    - If schema validation fails, returns an error message with details about the validation errors.
2.  **Provider Validation:**
    - Checks if the webhook provider is valid (allowed providers: 'FlipaClip', 'purchasely', 'Purchasely', 'paywall', 'ad').
    - If the provider is invalid, returns an error message.
3.  **Provider-Specific Validation:**
    - If the provider is 'purchasely', it checks if the `fcid` (FlipaClip ID) is present.
    - If `fcid` is missing for a 'purchasely' webhook, returns an error message.
4.  **Success:** If all validations pass, returns `null`.
5.  **Error Handling:** Includes a `try...catch` block to handle any errors during the validation process, logging the error and returning a generic validation error message.

### `isRetryableError(error: any): boolean`

This method determines whether an error is retryable based on its message or code.

**Parameters:**

- `error`: The error object to be checked.

**Returns:**

`true` if the error is considered retryable, `false` otherwise.

**Functionality:**

1.  Checks if the error message or code includes any of the following retryable error indicators:
    - Network-related errors: 'ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED', 'ENOTFOUND', 'EAI_AGAIN', 'ETIMEOUT', 'ECONNABORTED', 'ENETUNREACH', 'EHOSTUNREACH'
    - Database-related errors: 'deadlock', 'timeout', 'connection', 'transaction', 'lock' (case-insensitive)
2.  Returns `true` if any of the retryable error indicators are found, `false` otherwise.

## Queue Event Handlers

### `@OnQueueError() onError(error: Error)`

This method is called when an error occurs at the queue level (not specific to a job).

**Parameters:**

- `error`: The error object.

**Functionality:**

Logs the queue error with its message and stack trace.

### `@OnQueueFailed() onFailed(job: Job, error: Error)`

This method is called when a job in the queue fails (reaches the maximum number of retry attempts or encounters a non-retryable error).

**Parameters:**

- `job`: The failed `Job` object.
- `error`: The error object that caused the job to fail.

**Functionality:**

Logs the job failure, including the job ID, number of attempts, maximum attempts, error message, and stack trace.

## Initialization

The `WebhookProcessor` attempts to initialize itself in its constructor:

1.  Creates a new `Validator` instance from the `jsonschema` library.
2.  Logs a message indicating successful initialization.

If initialization fails (e.g., due to an error creating the validator), it logs an error message with the error details and re-throws the error, preventing the processor from being used.

## Important Considerations

- **Sequential Processing:** The `concurrency: 1` setting in the `@Process` decorator ensures that webhook jobs are processed one at a time, maintaining the order of events.
- **Validation:** The `validateWebhook` method is crucial for ensuring data integrity and preventing invalid data from being processed.
- **Retry Mechanism:** The `isRetryableError` method and the retry configuration in `batchConfig` provide a robust mechanism for handling transient errors and improving the reliability of webhook processing.
- **Error Handling:** Comprehensive error handling is implemented throughout the processor to log errors, manage retries, and prevent unexpected failures.
- **Dependency Injection:** The processor uses NestJS dependency injection to obtain instances of `BatchWebhookService` and other dependencies.
- **Logging:** The processor uses a logger to record important events, errors, and debugging information.

This documentation provides a detailed overview of the `WebhookProcessor`. For more specific information, refer to the source code and related documentation for the `BatchWebhookService`, `Webhook` entity, and `baseEventSchema`.
