# Paywall Webhook Events

This document describes the webhook events sent by the Paywall integration. These events are triggered when users interact with the subscription paywall in your application.

## Event Payload Example

```json
{
  "provider": "FlipaClip",
  "fcid": "12345",
  "event_name": "subscription_offer_shown",
  "store": "google_play",
  "session_id": 111123456789,
  "payload": {
    "paywall_id": "default_light_qa",
    "placement_id": "home_subscription_button",
    "trigger_action": "app_open",
    "plans": [
      {
        "id": "flipaclip_599_1m_7d0",
        "offers_free_trial": true,
        "period": "MONTH"
      },
      {
        "id": "flipaclip_2999_1y_7d0",
        "offers_free_trial": true,
        "period": "YEAR"
      }
    ],
    "ab_test_id": "test_001",
    "ab_test_variant": "variant_A"
  }
}
```

## Payload Fields

| Field           | Type   | Allowed Values                                                   | Description                                     |
| --------------- | ------ | ---------------------------------------------------------------- | ----------------------------------------------- |
| provider        | string | "FlipaClip" or "flipaclip"                                       | The provider sending the event                  |
| fcid            | string | Any string                                                       | Unique identifier for the user                  |
| event_name      | string | "subscription_offer_shown", "subscription_offer_aborted"         | The specific event triggered                    |
| store           | string | "google_play", "apple_app_store", "amazon_store", "huawei_store" | The store sending the event                     |
| session_id      | number | 123456789                                                        | The amplitude session_id                        |
| paywall_id      | string | Any string                                                       | The unique identifier for the paywall displayed |
| placement_id    | string | Any string                                                       | The placement identifier from the SDK           |
| annual_price    | number | Positive decimal                                                 | The annual price for the subscription offer     |
| trigger_action  | string | Any string                                                       | What caused the paywall to be displayed         |
| plans           | array  | Object array                                                     | The detailed plans offered                      |
| ab_test_id      | string | Any string (or null)                                             | Identifier for the A/B test if applicable       |
| ab_test_variant | string | Any string (or null)                                             | Identifier for the A/B test variant             |

## Subscription Offer Shown Event

Triggered when a paywall is shown to the user (corresponds to the PRESENTATION_VIEWED SDK event).

## Subscription Offer Aborted Event

Triggered when the user selects an offer, tries to purchase, and the operation is either aborted or fails.

### Additional Fields for `subscriptionOfferAborted`

| Field          | Type   | Allowed Values                     | Description                                          |
| -------------- | ------ | ---------------------------------- | ---------------------------------------------------- |
| offer_selected | string | "Annual", "Monthly"                | The type of offer the user selected and then aborted |
| abort_reason   | string | "User Cancelled", "Payment Failed" | The reason why the purchase was not completed        |

## Notes

- All webhook events from Paywall must include the standard webhook fields.
- Revenue values are always in USD currency.
- The `offers_free_trial` field will be `true` if the subscription offer includes a free trial.
- The `trigger_action` field describes what caused the paywall to be displayed.
- The `placement_id` field corresponds to the placement identifier from the SDK.
