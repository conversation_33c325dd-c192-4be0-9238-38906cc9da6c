# Webhook Validation Rules

This document outlines the validation rules applied to incoming webhook events. All events must pass both base validation rules and any provider-specific validation rules that apply.

## Base Event Validation

All webhook events must include the following required fields:

| Field       | Type   | Description                                    | Required |
| ----------- | ------ | ---------------------------------------------- | -------- |
| `eventType` | string | The type of event being processed              | Yes      |
| `timestamp` | number | Unix timestamp of when the event occurred      | Yes      |
| `provider`  | string | The source of the webhook (e.g., "ironsource") | Yes      |
| `payload`   | object | The event-specific data                        | Yes      |

### Type Validations

- `eventType`: Must be a non-empty string matching the pattern `^[a-zA-Z0-9_.-]+$`
- `timestamp`: Must be a positive integer representing a valid Unix timestamp
- `provider`: Must be a non-empty string containing only lowercase letters
- `payload`: Must be a non-null object

### Additional Base Rules

1. The event payload must not exceed 1MB in size
2. The timestamp must not be in the future
3. All string fields must be properly encoded UTF-8
4. No additional top-level fields are allowed beyond the required ones

## Provider-Specific Validation Rules

### IronSource Events

IronSource events must include these additional validations:

#### Required Fields in Payload

| Field            | Type   | Description                           |
| ---------------- | ------ | ------------------------------------- |
| `applicationKey` | string | The IronSource application identifier |
| `eventId`        | string | Unique identifier for the event       |
| `rewardAmount`   | number | Amount of virtual currency to reward  |
| `userId`         | string | The user identifier                   |

#### Type Validations

- `applicationKey`: Must be a non-empty string matching the pattern `^[A-Za-z0-9]+$`
- `eventId`: Must be a UUID v4 string
- `rewardAmount`: Must be a positive integer
- `userId`: Must be a non-empty string

#### Additional Rules

1. The `eventType` must be one of:
   - `ironsource.reward.credited`
   - `ironsource.reward.pending`
2. The `rewardAmount` must not exceed 1,000,000
3. The `userId` length must not exceed 128 characters

## Validation Response

When validation fails, the API will respond with:

- HTTP Status: 400 Bad Request
- Response body: JSON object containing:
  - `error`: String describing the validation error
  - `field`: The field that failed validation (if applicable)
  - `details`: Additional context about the validation failure

Example validation error response:

```json
{
  "error": "Invalid field value",
  "field": "rewardAmount",
  "details": "Value must be a positive integer"
}
```

## Rate Limiting

- Maximum of 1000 webhook events per minute per provider
- Exceeding this limit will result in HTTP 429 (Too Many Requests)
- Retry-After header will indicate when to resume sending events
