# Batch Webhook Processing Strategy

## Configuration

The batch processing behavior is controlled by the configuration in `config/batch.config.ts`:

```typescript
export const batchConfig = {
  maxBatchSize: 100, // Maximum events allowed in a single batch
  processingTimeout: 30000, // 30 seconds timeout for processing
  retry: {
    attempts: 3, // Number of retry attempts per event
    backoff: {
      type: 'exponential',
      initialDelay: 5000, // 5 seconds initial delay
    },
  },
};
```

## Usage

### Sending Batch Requests

To send webhook events in batch, make a POST request to the `/webhooks/batch` endpoint with the following structure:

```json
{
  "events": [
    {
      "event_name": "event_type",
      "provider": "provider_name",
      "store": "store_name",
      "event_control": {
        "device_id": "device_identifier",
        "timestamp": **********
      },
      "payload": {
        // Event-specific payload
      },
      "fcid": "user_fcid"
    }
    // ... more events
  ]
}
```

### Response Format

The batch endpoint returns a response with the following structure:

```json
{
  "status": "success" | "partial_success" | "error",
  "message": "Descriptive message about the processing result",
  "processed": number,    // Number of successfully processed events
  "failed": number,       // Number of failed events
  "validationErrors": [   // Array of validation errors (if any)
    {
      "index": number,
      "error": "error message",
      "event": {} // The invalid event object
    }
  ],
  "processingErrors": [   // Array of processing errors (if any)
    {
      "eventId": "event identifier",
      "error": "error message"
    }
  ]
}
```

## Processing Strategy

### 1. Validation Phase

Before processing, each event in the batch goes through validation:

- Checks for maximum batch size limit
- Validates each event against the appropriate schema (base, ad, paywall, or purchasely)
- Validates event timestamp and user existence
- Events that fail validation are collected but don't prevent processing of valid events

### 2. Sequential Processing

Events are processed sequentially to maintain order and prevent race conditions:

1. Each event is processed one at a time in the order received
2. Timestamp validation is performed before processing each event
3. Results are collected and aggregated
4. The response is sent only after all events are processed

This approach ensures:

- Event order is maintained
- No race conditions in timestamp validation
- Consistent state updates
- Accurate error reporting

## Observability and Monitoring

### 1. Correlation IDs

Each webhook event is assigned a unique correlation ID that is propagated through the entire processing pipeline:

- Generated using UUID v4
- Included in all log messages
- Helps track events across different services
- Enables request tracing and debugging

### 2. Structured Logging

All log messages include consistent context:

```typescript
{
  correlationId: string;  // Unique ID for request tracing
  fcid: string;          // User identifier
  eventName: string;     // Type of event
  provider: string;      // Event provider
  batchId?: string;      // Batch identifier (if applicable)
  data?: any;           // Additional context-specific data
}
```

### 3. Prometheus Metrics

The following metrics are collected:

#### Histograms

- `webhook_processing_duration_seconds`: Duration of webhook processing
  - Labels: provider, event_type, status
  - Buckets: [0.1, 0.5, 1, 2, 5] seconds

#### Counters

- `webhooks_processed_total`: Total number of processed webhooks
  - Labels: provider, event_type, status
- `webhook_errors_total`: Total number of webhook processing errors
  - Labels: provider, event_type, error_type
- `webhook_retries_total`: Total number of webhook processing retries
  - Labels: provider, event_type
- `revenue_tracked_total`: Total revenue tracked in USD
  - Labels: provider, revenue_type

### 4. Health Checks

Health and metrics endpoints:

- `/health`: Returns system health status
- `/health/metrics`: Returns Prometheus metrics

## Retry Mechanism

The retry mechanism uses an exponential backoff strategy for failed events:

### How Retries Work

1. **Per-Event Retries**: Each event gets its own retry attempts
2. **On Failure**:
   - First retry: Waits 5 seconds (initial delay)
   - Second retry: Waits 10 seconds (2x initial delay)
   - Third retry: Waits 20 seconds (4x initial delay)

### Retry Configuration

```typescript
retry: {
  attempts: 3,        // Maximum number of attempts per event
  backoff: {
    type: 'exponential',
    initialDelay: 5000, // 5 seconds
  }
}
```

The delay between retries is calculated as:

```
delay = initialDelay * (2 ^ (attemptNumber - 1))
```

### Error Handling

- If an event fails all retry attempts, it's marked as failed
- The error message and event ID are included in the response
- Processing continues with the next event
- The response includes both validation and processing errors
- Errors are tracked in Prometheus metrics

## Job Persistence and Recovery

The system uses Bull/Redis for robust job persistence and recovery:

### Reliability Guarantees

1. **Job Persistence**: All webhook events are persisted in Redis, ensuring:

   - No data loss if the processing server goes down
   - Events are preserved even during system restarts
   - Processing order is maintained after recovery

2. **Automatic Recovery**: If the processing server experiences downtime:

   - In-progress events are automatically reprocessed when the server recovers
   - Queued events maintain their original order
   - No manual intervention is required

3. **Processing Guarantees**:
   - Events are processed exactly once (unless retry is needed)
   - Event order is preserved
   - Failed events are retried automatically with exponential backoff

> Note: These guarantees mean clients don't need to implement additional retry logic for server downtime scenarios. The system will automatically handle recovery and ensure all events are processed.

## Best Practices

1. **Batch Size**: Keep batches under the maximum size (100 events)
2. **Event Order**: Events are processed in the order received
3. **Timestamp Validation**: Each event's timestamp is validated before processing
4. **Error Handling**: Implement proper error handling for failed events
5. **Timeouts**: Consider the processing timeout (30 seconds) when sending large batches
6. **Retries**: Each event gets its own retry attempts with exponential backoff
7. **Monitoring**: Use provided metrics to track system health and performance
8. **Tracing**: Include correlation IDs in client logs for request tracing
9. **Revenue Tracking**: Monitor revenue metrics for accuracy

## Rate Limiting

The batch endpoint is protected by rate limiting configured in the `throttleConfig`.
