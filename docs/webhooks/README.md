# Webhook Processing System Documentation

This documentation provides an overview of the webhook processing system, which handles incoming webhook events from various providers (primarily FlipaClip).

## Key Components

- **[Processing Overview](./ProcessingOverview.md):** A high-level description of the webhook processing strategy, including batching, validation, and error handling.
- **[Batch Processing](./BatchProcessing.md):** Detailed information about the batch processing mechanism, including configuration, usage, and the retry system.
- **[Webhook Processor](./WebhookProcessor.md):** Documentation for the `WebhookProcessor`, which manages the queue and processes individual webhook jobs.
- **Batch Webhook Service:** Documentation for the `BatchWebhookService`, which handles the core logic of processing individual webhook events.

## Processing Flow

1.  Incoming webhooks are received and added to a queue.
2.  The `WebhookProcessor` dequeues jobs and validates the webhook data.
3.  Valid jobs are passed to the `BatchWebhookService` for processing.
4.  The `BatchWebhookService` interacts with handlers, repositories, and other services to process the event and update data.
5.  Metrics are collected throughout the process for monitoring and observability.

For more detailed information, please refer to the documentation for each component.
