# IronSource Webhook Events

This document describes the webhook events sent by IronSource integration. These events are triggered when users interact with ads in your application.

## Event Payload Example

```json
{
  "revenue": 0.05,
  "adType": "rewarded",
  "abTestGroup": "A",
  "instanceName": "Bidding",
  "segmentName": "non_coppa",
  "loadTime": 1,
  "adUnitId": "DefaultRewardedVideo",
  "isRewardGranted": true,
  "publisherNetwork": "IronSource",
  "triggerAction": "adCompleted"
}
```

## Payload Fields

| Field            | Type    | Allowed Values                                      | Description                                                                   |
| ---------------- | ------- | --------------------------------------------------- | ----------------------------------------------------------------------------- |
| revenue          | number  | Positive decimal                                    | The revenue generated from the ad impression in USD                           |
| adType           | string  | "Rewarded", "Interstitial", "Banner"                | The type of advertisement that was shown                                      |
| abTestGroup      | string  | "A", "B"                                            | Mediation A/B test group                                                      |
| instanceName     | string  | "Applovin $3", "Bidding"                            | Mediation instance name                                                       |
| segmentName      | string  | "non_coppa", "coppa"                                | Mediation segment name                                                        |
| loadTime         | number  | Positive integer                                    | Time taken to load the ad in milliseconds                                     |
| adUnitId         | string  | Any string                                          | Unique identifier for the ad unit placement                                   |
| isRewardGranted  | boolean | true, false                                         | Indicates if the reward was granted to the user (applicable for rewarded ads) |
| publisherNetwork | string  | "Ironsource", "Applovin", "Unityads", "Vungle"      | The advertising network that served the ad                                    |
| triggerAction    | string  | "adCompleted", "adStarted", "adSkipped", "adFailed" | The action that triggered the webhook event                                   |

## Notes

- All webhook events from IronSource will include these standard fields
- The `isRewardGranted` field is only relevant for rewarded video ads
- Revenue values are always in USD currency
- Load time is measured from the initial ad request to when the ad is ready to be displayed
