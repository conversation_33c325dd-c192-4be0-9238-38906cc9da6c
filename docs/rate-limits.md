# Rate Limits for Controllers

This document outlines the rate limits configured for each controller in the application. Rate limiting is implemented using the `@nestjs/throttler` package to prevent abuse and ensure fair usage of the API.

## Controllers and Rate Limits

### Auth Controller

- **Endpoint**: `/auth`
- **Rate Limit**: 10 requests per minute
- **Configuration**: `throttleConfig.auth`

### Users Controller

- **Endpoint**: `/users`
- **Rate Limit**: 20 requests per minute
- **Configuration**: `throttleConfig.users`

### Webhooks Controller

- **Endpoint**: `/webhooks`
- **Rate Limit**: 30 requests per minute
- **Configuration**: `throttleConfig.webhooks`

### Health Controller

- **Endpoint**: `/health`
- **Rate Limit**: 40 requests per minute
- **Configuration**: `throttleConfig.health`

## Throttle Configuration

The throttle configuration is centralized in the `throttle.config.ts` file located in the `src/config` directory. This configuration is imported and applied in each controller using the `@Throttle` decorator.

## Purpose

Rate limiting helps to:

- Protect the API from excessive requests that could lead to denial of service.
- Ensure fair usage among all users.
- Maintain the performance and reliability of the service.

For more details on how rate limiting is implemented, refer to the `throttle.config.ts` file and the respective controller files.
