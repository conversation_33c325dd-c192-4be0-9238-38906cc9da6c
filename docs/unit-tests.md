# Jest Unit tests guide

### How tests are run locally?

Once you have the node app running locally, test can be run from CLI:

`npm run test`

This will run Jest against our local app instance.

Config vars:

- `NODE_ENV` is either **test** or **local**
- `ROSHI_URL` is **http://localhost:3000/**

### How tests are run on Github Workflow?

Unit tests over Github Workflow are always run against **QA** environment.
This is the command it runs on Workflow's CLI:

`npm run test:workflow` (`NODE_ENV=test npm test -- --json --outputFile=jest_results.json`)

Jest results are saved to a JSON file that can be picked up later.

Config vars:

- `NODE_ENV` is **test**
- `TEST_ENV` is **live**
`ROSHI_URL` is **https://roshi.dev.flipaclip.com/**

### Replicating tests environments

- To run tests against a local server instance, set `ROSHI_URL=http://localhost:3000/` in your `.env` file.
- If `ROSHI_URL` is not defined, tests will run against the **QA** environment by default.
- `NODE_ENV` should be **test** to replicate CI
