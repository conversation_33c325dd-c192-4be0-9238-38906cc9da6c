# Database Migrations Guide

## Overview

Database migrations are version control for your database schema. They ensure consistent database structure across all environments (development, staging, production) and team members. Migrations track every change to your database schema, allowing you to:

- Roll forward to apply new changes
- Roll back to undo changes if needed
- Maintain a history of all database modifications
- Collaborate effectively with team members

## Migration Commands

### Creating a New Migration

#### Automatic Generation

```bash
npm run migration:generate -- migration-name
```

This command analyzes your entity changes and generates the necessary migration file automatically.

#### Manual Creation

```bash
npm run migration:create -- migration-name
```

Use this when you need to write custom migration logic manually.

### Running Migrations

```bash
npm run migration:run
```

Applies all pending migrations in chronological order.

### Reverting Migrations

```bash
npm run migration:revert
```

Reverts the most recently applied migration.

## Best Practices

1. **Migration Naming**

   - Use descriptive, action-based names (e.g., `AddUserEmailIndex`, `CreateProductTable`)
   - Follow PascalCase convention
   - Include the affected table/feature in the name

2. **Implementation Guidelines**

   - Always implement both `up()` and `down()` methods
   - Test migrations in a development environment first
   - Never modify a migration that has been committed or applied
   - Keep migrations atomic - one logical change per migration

3. **Documentation**

   - Add comments for complex operations
   - Document any manual steps required
   - Include the reason for the migration if not obvious

4. **Testing**
   - Verify both `up()` and `down()` methods work as expected
   - Test migrations against a copy of production data
   - Ensure rollback procedures are tested

## Example Migration File Structure

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserEmailIndex1234567890123 implements MigrationInterface {
  name = 'AddUserEmailIndex1234567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Define changes to apply
    await queryRunner.query(`CREATE INDEX "IDX_USER_EMAIL" ON "user" ("email")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Define how to revert changes
    await queryRunner.query(`DROP INDEX "IDX_USER_EMAIL"`);
  }
}
```

## Troubleshooting

### Common Issues and Solutions

1. **Migration Failed to Apply**

   - Check the migration history table (`typeorm_migrations`)
   - Review database logs for specific error messages
   - Verify database user permissions

2. **Conflict Resolution**

   - Never modify existing migrations
   - Create a new migration to fix issues
   - Document the fix in the new migration's comments

3. **Best Practices for Recovery**
   - Always backup database before running migrations
   - Keep record of all applied migrations
   - Test rollback procedures regularly

### Prevention Tips

1. **Before Running Migrations**

   - Review migration content carefully
   - Test in development environment
   - Ensure database backups are current
   - Communicate with team members

2. **After Failed Migrations**
   - Do not manually modify database schema
   - Document the error and resolution
   - Share learnings with the team
