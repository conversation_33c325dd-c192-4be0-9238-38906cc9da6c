# HTTP Examples for User Management API

This document provides cURL examples for interacting with the User Management API.

## Prerequisites

- Replace `<BASE_URL>` with your server's base URL
- Replace `YOUR_TOKEN` with a valid authentication token
- All requests require proper authentication using the Authorization header

## Create/Update User Examples

### Create Anonymous User with <PERSON>ce Identifier

```bash
curl -X POST <BASE_URL>/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "identifiers": {
      "adid": ["new-adid"]
    },
    "installed_at": 1707194321000
  }'
```

#### Expected Response:

```json
{
  "fcid": "generated-fcid",
  "type": "ANONYMOUS",
  "identifiers": {
    "adid": ["new-adid"]
  },
  "properties": {},
  "created_at": 1631022245,
  "updated_at": 1631022245
}
```

### Update Registered User

```bash
curl -X POST <BASE_URL>/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "fcaid": "existing-fcaid",
    "identifiers": {
      "adid": ["new-adid"]
    },
    "properties": {
      "totalAdRevenue": 5.50,
      "totalSubscriptionRevenue": 5.49
    }
  }'
```

#### Expected Response:

```json
{
  "fcaid": "existing-fcaid",
  "type": "REGISTERED",
  "identifiers": {
    "adid": ["old-adid", "new-adid"]
  },
  "properties": {
    "totalAdRevenue": 5.5,
    "totalSubscriptionRevenue": 5.49
  },
  "created_at": 1631022245,
  "updated_at": 1631022300
}
```

## Get User Examples

### Get User by FCAID

```bash
curl -X GET '<BASE_URL>/users?fcaid=existing-fcaid' \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get User by Device Identifier

```bash
curl -X GET '<BASE_URL>/users?identifiers.adid=existing-adid' \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Expected Response:

```json
{
  "fcaid": "existing-fcaid",
  "type": "REGISTERED",
  "identifiers": {
    "adid": ["existing-adid"]
  },
  "properties": {
    "totalAdRevenue": 5.5,
    "totalSubscriptionRevenue": 5.49
  },
  "created_at": 1631022245,
  "updated_at": 1631022300
}
```

## Delete User Examples

### Delete User by FCID

```bash
curl -X DELETE <BASE_URL>/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "fcid": "existing-fcid",
    "token": "delete-confirmation-token"
  }'
```

> **Note**: The `token` field is required for all delete operations as an additional security measure.

#### Expected Response:

```json
{
  "message": "User successfully deleted"
}
```

## Error Responses

### User Not Found

```json
{
  "statusCode": 404,
  "message": "User not found"
}
```

### Invalid Request

```json
{
  "statusCode": 400,
  "message": "At least one valid identifier must be provided (fcid, fcaid, or device identifiers)"
}
```

### Unauthorized

```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```
