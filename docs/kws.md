# Kids Web Services (KWS) Integration

This document describes the integration with Kids Web Services (KWS) API for age verification and parental controls.

## Overview

The KWS integration provides endpoints to interact with the Kids Web Services API, specifically for age verification and parental controls. The integration is built using NestJS and follows the existing application architecture.

## Configuration

The following environment variables are required:

```env
KWS_PRODUCT_CLIENT_ID=your_client_id
KWS_API_KEY=your_api_key
KWS_AUTH_URL=https://auth.kidswebservices.com/auth/realms/kws/protocol/openid-connect/token
KWS_API_URL=https://api.kidswebservices.com/v1/age
```

The `KWS_AUTH_URL` and `KWS_API_URL` are optional and will default to the production KWS endpoints if not provided. These can be configured to point to different environments (e.g., staging) if needed.

## Usage

### Using the KWS Service in Other Services

The KWS service can be injected into other services to verify age programmatically:

```typescript
import { Injectable } from '@nestjs/common';
import { KwsService } from '../kws/kws.service';
import { KwsAgeGateResponse } from '../kws/interfaces/kws-response.interface';

@Injectable()
export class YourService {
  constructor(private readonly kwsService: KwsService) {}

  async someMethod() {
    try {
      const ageGateData: KwsAgeGateResponse = await this.kwsService.getAgeGateData({
        ip: '*************',
        age: 13,
      });

      // Use the age gate data
      if (ageGateData.underAgeOfDigitalConsent) {
        // Handle underage user
      }

      // Access other properties
      const { country, region, consentAge, userAge } = ageGateData;
    } catch (error) {
      // Handle error
    }
  }
}
```

The `getAgeGateData` method returns a `KwsAgeGateResponse` object with the following structure:

```typescript
interface KwsAgeGateResponse {
  country: string; // Country code (e.g., "US")
  region: string; // Region/State code (e.g., "FL")
  consentAge: number; // Age of consent for the region
  userAge: number; // User's age
  underAgeOfDigitalConsent: boolean; // Whether user is under age of consent
}
```

## Endpoints

### Age Gate Verification

```http
GET /kws/age-gate
```

Verifies the age of a user based on provided parameters.

#### Query Parameters

| Parameter | Type   | Required | Description                    |
| --------- | ------ | -------- | ------------------------------ |
| ip        | string | No       | IP address of the user         |
| location  | string | No       | Location of the user           |
| dob       | string | No       | Date of birth                  |
| age       | number | No       | Age of the user (integer only) |

#### Example Request

```http
GET /kws/age-gate?ip=*************&age=13
```

#### Example Response

```json
{
  "message": "Success",
  "statusCode": 200,
  "data": {
    "response": {
      "country": "US",
      "region": "FL",
      "consentAge": 13,
      "userAge": 13,
      "underAgeOfDigitalConsent": false
    },
    "meta": {
      "requestId": "b77aad10-364c-11f0-9159-a741e631dd24",
      "timestamp": "2025-05-21T14:05:57.217Z"
    }
  },
  "timestamp": "2025-05-21T14:05:57.166Z"
}
```

#### Error Responses

- `400 Bad Request`: Invalid parameters
- `500 Internal Server Error`: Server or KWS API error

## Implementation Details

### Token Management

The integration includes automatic token management with caching:

- Tokens are cached and reused until they expire
- Automatic token refresh when expired
- Token scope management for different KWS operations

### Security

- All KWS credentials are managed through environment variables
- Token-based authentication with KWS API
- Input validation for all parameters
- Error handling and logging

## Testing

To test the integration:

1. Set up the required environment variables
2. Start the server
3. Use Postman or similar tool to test the endpoints
4. Verify the responses and error handling

## Dependencies

- @nestjs/axios: For HTTP requests
- class-validator: For request validation
- class-transformer: For data transformation
