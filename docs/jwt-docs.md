# **JWT Authentication System Documentation**

## **Overview**

This documentation describes the implementation of **JWT-based authentication** for the server.  
It includes **access token generation, refresh tokens, route protection, and security measures**.

---

## **Authentication Flow**

1. **<PERSON>lient requests a JWT token** using the `auth/createJwt` endpoint.
2. **Server validates the request** and issues an **access token** (15 min) and a **refresh token** (7 days).
3. **<PERSON><PERSON> uses the access token** to access protected routes.
4. **When the access token expires**, the client sends the **refresh token** to `auth/refresh` to get a new access token.
5. **Refresh token expires after 7 days**—users must create a new token again.

---

## **JWT Structure**

### **JWT Claims**

The JWT contains the following claims:

| **Claim** | **Type** | **Required** | **Description**                 |
| --------- | -------- | ------------ | ------------------------------- |
| `idfv`    | `string` | Optional     | This or adid have to be present |
| `adid`    | `string` | Optional     | This or idfv have to be present |
| `fcid`    | `string` | Optional     | Unique roshi identifier (fcid)  |

### **User Types**

1. **Anonymous Users**

   - Must provide at least one device identifier
   - `idfv` or `adid` is required in claims
   - `fcid` present if logged in

2. **Registered Users**
   - Must provide at least one device identifier
   - `idfv` or `adid` is required in claims
   - `fcid` present if logged in

### **Device Identifiers**

The system supports the following device identifiers:

- `idfv`: iOS Vendor ID
- `adid`: Android Device ID

**Important Rules:**

- At least one device identifier is required for anonymous users

### **Token Expiration**

- **Access Token:** `15 minutes`
- **Refresh Token:** `7 days`

---

## **Endpoints**

### **Create JWT Token**

Generates an access token and refresh token.

#### **Request**

**POST** `/auth/createJwt`

#### **Headers**

```http
Content-Type: application/json
x-api-key: {{$dotenv API_AUTH_TOKEN}}
```

#### **Body Examples**

##### **Create users**

1. **Anonymous User**

```json
{
  "identifiers": {
    "idfa": ["test-idfa"],
    "idfv": ["test-idfv"],
    "gaid": ["test-gaid"],
    "adid": ["test-adid"]
  }
}
```

2. **Registered User**

```json
{
  "fcaid": "registered-user"
}
```

##### **Log in users (fcid present)**

1. **Anonymous User**

```json
{
  "fcid": "test-fcid",
  "identifiers": {
    "idfa": ["test-idfa"],
    "idfv": ["test-idfv"],
    "gaid": ["test-gaid"],
    "adid": ["test-adid"]
  }
}
```

2. **Registered User**

```json
{
  "fcid": "test-fcid",
  "fcaid": "registered-user"
}
```

#### **Response**

```json
{
  "message": "Success",
  "statusCode": 201,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "accessTokenExpiry": "2025-04-29T00:02:57.862Z",
    "refreshTokenExpiry": "2025-05-05T23:47:57.862Z"
  },
  "timestamp": "2025-04-28T23:47:57.862Z"
}
```

### **Refresh Access Token**

Renews an access token using the refresh token.

#### **Request**

**POST** `/auth/refresh`

#### **Headers**

- `x-api-key`: `your-api-key-here` (Required)
- `Content-Type`: `application/json` (Required)

#### **Body**

```json
{
  "refreshToken": "your-refresh-token-here"
}
```

#### **Response**

```json
{
  "message": "Success",
  "statusCode": 201,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "accessTokenExpiry": "2025-04-29T00:02:57.862Z",
    "refreshTokenExpiry": "2025-05-05T23:47:57.862Z"
  },
  "timestamp": "2025-04-28T23:47:57.862Z"
}
```

### **Access Protected Route**

Access a JWT-protected route.

#### **Request**

**POST** `/auth/protected`

#### **Headers**

- `Authorization`: `Bearer your-access-token-here` (Required)

#### **Response**

```json
{
  "message": "You have access!",
  "user": {
    "id": "user-123",
    "type": "ANONYMOUS",
    "key": "device-key-1",
    "deviceId": "hash-of-device-ids",
    "iat": 1707194321,
    "exp": 1707195221
  }
}
```

## **Error Handling**

The system will return appropriate error messages for:

1. **Invalid Device IDs**

   - Missing required device IDs for anonymous users
   - IDFA without IDFV
   - Duplicate device IDs

2. **Invalid User Type**

   - Registered users without FCAID
   - Invalid type transitions

3. **Authentication Errors**
   - Invalid API key
   - Invalid or expired tokens
   - Missing required claims
