# Dead Letter Queue (DLQ) System

## Overview

The Dead Letter Queue (DLQ) system in Roshi provides a robust mechanism for handling failed jobs. When a job fails after exhausting all retry attempts, it's moved to a DLQ for later analysis and potential reprocessing.

## DLQ Architecture

The application uses dedicated DLQ queues for different services:

1. **webhooks-dlq**: For failed webhook processing jobs
2. **geolocation-dlq**: For failed geolocation lookup jobs

Each DLQ is paired with a main processing queue:

```
Main Queue (processing) → Retries → DLQ (if all retries fail)
```

## DLQ Implementation

### Queue Registration

DLQs are registered in the `RedisModule`:

```typescript
BullModule.registerQueue(
  {
    name: 'webhooks',
    defaultJobOptions: {
      ...redisConfig.queue.defaultJobOptions,
      removeOnComplete: true,
      removeOnFail: false,
    },
  },
  {
    name: 'webhooks-dlq',
    defaultJobOptions: {
      removeOnComplete: false,
      removeOnFail: false,
    },
  },
);
```

Key configuration differences for DLQs:

- `removeOnComplete: false`: Keep completed jobs for analysis
- `removeOnFail: false`: Keep failed jobs for analysis

### Moving Jobs to DLQ

When a job fails after maximum retries, it's moved to the DLQ with additional context:

```typescript
// Example from WebhookProcessor
@OnQueueFailed()
async onFailed(job: Job, error: Error) {
  if (job.attemptsMade >= job.opts.attempts) {
    try {
      // Prepare data for DLQ
      const dlqData = {
        originalData: job.data,
        originalJobId: job.id,
        attemptsMade: job.attemptsMade,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        timestamp: new Date().toISOString(),
      };

      // Add to DLQ
      await this.redisService.addToDlq(dlqData, {
        jobId: `dlq-${job.id}-${Date.now()}`,
      });

      this.logger.log(`Moved failed job ${job.id} to DLQ after ${job.attemptsMade} attempts`);
    } catch (dlqError) {
      this.logger.error(
        `Failed to move job ${job.id} to DLQ: ${dlqError.message}`,
      );
    }
  }
}
```

### DLQ Processors

Each DLQ has a dedicated processor that logs the failed jobs:

```typescript
@Processor('geolocation-dlq')
export class GeolocationDlqProcessor {
  private readonly logger = new Logger(GeolocationDlqProcessor.name);

  @Process('process-dlq')
  async processDlqJob(job: Job) {
    this.logger.debug(`Received job ${job.id} in geolocation DLQ`);
    // Just log the job, no processing needed for DLQ
    return { processed: true };
  }

  @OnQueueError()
  onError(error: Error) {
    this.logger.error(`Geolocation DLQ queue error: ${error.message}`, error.stack);
  }
}
```

## DLQ Data Structure

Each DLQ entry contains:

1. **Original Job Data**: The complete data from the original job
2. **Job Metadata**: ID, timestamps, attempt count
3. **Error Context**: Error message, stack trace, error type
4. **Processing History**: Information about previous processing attempts

Example DLQ entry for geolocation:

```json
{
  "originalData": {
    "fcid": "user-123",
    "ip": "***********"
  },
  "originalJobId": "geo-job-456",
  "attemptsMade": 3,
  "error": {
    "message": "Request failed with status code 429",
    "stack": "Error: Request failed with status code 429\n    at createError...",
    "name": "AxiosError"
  },
  "timestamp": "2023-06-01T12:34:56.789Z"
}
```

## DLQ Management

### Monitoring

The DLQ system includes monitoring capabilities:

```typescript
// Example from DlqService
async getStats() {
  try {
    const [active, completed, failed, delayed, waiting] = await Promise.all([
      this.dlqQueue.getActiveCount(),
      this.dlqQueue.getCompletedCount(),
      this.dlqQueue.getFailedCount(),
      this.dlqQueue.getDelayedCount(),
      this.dlqQueue.getWaitingCount(),
    ]);

    return {
      active,
      completed,
      failed,
      delayed,
      waiting,
      total: active + completed + failed + delayed + waiting,
    };
  } catch (error) {
    this.logger.error(`Failed to get DLQ stats: ${error.message}`, error.stack);
    throw error;
  }
}
```

### Alerting

The system can send alerts when the DLQ size exceeds a threshold:

```typescript
@Cron(CronExpression.EVERY_HOUR)
async monitorDlqSize(): Promise<void> {
  try {
    const stats = await this.getStats();
    const totalJobs = stats.total;

    if (totalJobs > this.dlqAlertThreshold) {
      // Get 5 most recent jobs for the alert
      const recentJobs = await this.getJobs(0, 5);
      await this.sendSlackAlert(totalJobs, recentJobs);
    }
  } catch (error) {
    this.logger.error(`Failed to monitor DLQ size: ${error.message}`, error.stack);
  }
}
```

## Best Practices

1. **Regular Monitoring**: Check DLQ size and contents regularly
2. **Root Cause Analysis**: Analyze patterns in DLQ entries to identify systemic issues
3. **Cleanup Policy**: Implement a policy for cleaning up old DLQ entries
4. **Reprocessing Strategy**: Develop a strategy for reprocessing valid failed jobs
5. **Alerting**: Set up alerts for abnormal DLQ growth

## Troubleshooting

Common DLQ issues and solutions:

1. **Rapid Growth**: If the DLQ is growing rapidly, investigate the root cause of failures
2. **Memory Usage**: Monitor Redis memory usage as DLQs can consume significant memory
3. **Duplicate Entries**: Check for duplicate entries that might indicate retry issues
4. **Persistent Failures**: Look for patterns in errors that might indicate systemic problems
