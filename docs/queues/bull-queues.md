# Bull Queue System

## Overview

The Roshi service uses Bull, a Redis-based queue system, for handling asynchronous tasks. <PERSON> provides a robust framework for job processing with features like retries, priorities, and delayed execution.

## Queue Architecture

The application uses several Bull queues for different purposes:

1. **webhooks**: Processes webhook events from various providers
2. **webhooks-dlq**: Dead Letter Queue for failed webhook processing
3. **geolocation**: Processes IP geolocation requests
4. **geolocation-dlq**: Dead Letter Queue for failed geolocation requests

### Queue Configuration

All queues are configured in the `RedisModule` and share the same Redis connection:

```typescript
// src/common/redis.module.ts
BullModule.registerQueue(
  {
    name: 'webhooks',
    defaultJobOptions: {
      ...redisConfig.queue.defaultJobOptions,
      removeOnComplete: true,
      removeOnFail: false,
    },
  },
  {
    name: 'webhooks-dlq',
    defaultJobOptions: {
      removeOnComplete: false,
      removeOnFail: false,
    },
  },
  {
    name: 'geolocation',
    defaultJobOptions: {
      ...redisConfig.queue.defaultJobOptions,
      removeOnComplete: true,
      removeOnFail: false,
    },
  },
  {
    name: 'geolocation-dlq',
    defaultJobOptions: {
      removeOnComplete: false,
      removeOnFail: false,
    },
  },
);
```

## Job Processing

Each queue has a dedicated processor that handles the jobs:

1. **WebhookProcessor**: Processes webhook events
2. **DlqProcessor**: Logs DLQ entries for webhooks
3. **GeolocationProcessor**: Processes geolocation requests
4. **GeolocationDlqProcessor**: Logs DLQ entries for geolocation

### Job Lifecycle

1. **Creation**: Jobs are added to a queue with specific options
2. **Processing**: A processor picks up the job and executes it
3. **Completion/Failure**: The job is either completed or fails
4. **Retry**: Failed jobs are retried based on configuration
5. **DLQ**: After maximum retries, failed jobs are moved to a DLQ

## Dead Letter Queues (DLQ)

Dead Letter Queues store jobs that have failed after maximum retry attempts. They serve several purposes:

1. **Debugging**: Preserve the original job data and error context
2. **Analysis**: Identify patterns in failures
3. **Reprocessing**: Allow manual reprocessing of failed jobs
4. **Monitoring**: Track failure rates and types

### DLQ Implementation

When a job fails after maximum retries, it's moved to a DLQ with additional metadata:

```typescript
// Example from WebhookProcessor
@OnQueueFailed()
async onFailed(job: Job, error: Error) {
  if (job.attemptsMade >= job.opts.attempts) {
    try {
      // Prepare data for DLQ
      const dlqData = {
        originalData: job.data,
        originalJobId: job.id,
        attemptsMade: job.attemptsMade,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        timestamp: new Date().toISOString(),
      };

      // Add to DLQ
      await this.redisService.addToDlq(dlqData, {
        jobId: `dlq-${job.id}-${Date.now()}`,
      });

      this.logger.log(`Moved failed job ${job.id} to DLQ after ${job.attemptsMade} attempts`);
    } catch (dlqError) {
      this.logger.error(
        `Failed to move job ${job.id} to DLQ: ${dlqError.message}`,
      );
    }
  }
}
```

## Queue Configuration Options

The default queue configuration is defined in `src/config/redis.config.ts`:

```typescript
queue: {
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000,
    },
    removeOnComplete: true,
    removeOnFail: false,
  },
}
```

Key options:

- **attempts**: Number of retry attempts (default: 3)
- **backoff**: Retry strategy (exponential with 5s initial delay)
- **removeOnComplete**: Remove successful jobs (true)
- **removeOnFail**: Keep failed jobs for analysis (false)

## Monitoring and Management

### Queue Statistics

You can get queue statistics using the RedisService:

```typescript
// Example for DLQ stats
async getDlqStats() {
  try {
    const [active, completed, failed, delayed, waiting] = await Promise.all([
      this.dlqQueue.getActiveCount(),
      this.dlqQueue.getCompletedCount(),
      this.dlqQueue.getFailedCount(),
      this.dlqQueue.getDelayedCount(),
      this.dlqQueue.getWaitingCount(),
    ]);

    return {
      active,
      completed,
      failed,
      delayed,
      waiting,
    };
  } catch (error) {
    this.logger.error(`Failed to get DLQ stats: ${error.message}`);
    throw error;
  }
}
```

### Queue Monitoring

For production environments, consider using:

- **Bull Board**: A UI dashboard for Bull queues
- **Prometheus**: For metrics collection
- **Grafana**: For visualization and alerting

## Best Practices

1. **Job Idempotency**: Ensure jobs can be safely retried without side effects
2. **Concurrency Control**: Set appropriate concurrency limits for processors
3. **Error Handling**: Properly categorize errors as retryable or non-retryable
4. **Monitoring**: Set up alerts for queue size and processing delays
5. **DLQ Management**: Regularly review and clean up DLQ entries

## Troubleshooting

Common issues and solutions:

1. **Redis Connection Issues**: Check Redis connection settings and network
2. **Memory Issues**: Monitor Redis memory usage and implement job cleanup
3. **Stalled Jobs**: Adjust the stalledInterval setting if jobs get stuck
4. **High Failure Rate**: Review error patterns in DLQ and fix root causes
