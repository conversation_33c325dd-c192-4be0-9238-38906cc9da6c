# Roshi API Validation Rules

## Overview

This document outlines the validation rules, type definitions, and best practices for the Roshi API. It serves as the authoritative reference for developers implementing or consuming the API.

## Type Definitions

### UserType Enum

The UserType enum defines the possible user types in the system:

```typescript
enum UserType {
  ANONYMOUS = 'ANONYMOUS',
  REGISTERED = 'REGISTERED',
}
```

### DeviceIdentifiers Interface

Device identifiers are used to uniquely identify user devices:

```typescript
interface DeviceIdentifiers {
  idfa?: string[]; // iOS Advertising Identifier
  idfv?: string[]; // iOS Vendor Identifier
  gaid?: string[]; // Google Advertising Identifier
  adid?: string[]; // Android Device Identifier
}
```

**Important Note**: The fields 'fcaid' and 'fcid' must always be provided at the root level of the request, not within the deviceIdentifiers object. The deviceIdentifiers object is reserved exclusively for device-specific identifiers (idfa, idfv, gaid, adid) which should be provided as arrays.

### CreateUserRequest Structure

```typescript
interface CreateUserRequest {
  userType: UserType;
  fcaid?: string; // FlipaClip Account ID
  fcid?: string; // FlipaClip ID (immutable)
  deviceIdentifiers: DeviceIdentifiers;
}
```

## Validation Rules

### Anonymous Users

1. **Device Identifiers**

   - At least one device identifier is required
   - Can include any combination of advertisingId, deviceId, or pushToken
   - All provided identifiers must be non-empty strings

2. **Authentication Fields**
   - Must NOT include fcaid
   - Must NOT include fcid

### Registered Users

1. **Required Fields**

   - fcaid is required
   - At least one device identifier is required

2. **Optional Fields**

   - fcid is optional but immutable once set
   - Additional device identifiers can be provided

3. **Field Constraints**
   - fcaid must be a valid FlipaClip Account ID
   - fcid, if provided, must be a valid FlipaClip ID

## Examples

### Valid Requests

#### Creating an Anonymous User

```json
{
  "userType": "ANONYMOUS",
  "deviceIdentifiers": {
    "idfa": ["BDCE8411-F598-4FC9-A315-EC538ED37CD1"],
    "gaid": ["********-8cf0-11bd-b23e-10b96e40000d"]
  }
}
```

#### Creating a Registered User

```json
{
  "userType": "REGISTERED",
  "fcaid": "flipaclip-account-123",
  "fcid": "flipaclip-id-456",
  "deviceIdentifiers": {
    "idfa": ["BDCE8411-F598-4FC9-A315-EC538ED37CD1"],
    "idfv": ["5C6C7B9D-9121-4405-9AA1-39B7EABC1234"]
  }
}
```

### Invalid Requests

#### Missing Device Identifiers

```json
{
  "userType": "ANONYMOUS",
  "deviceIdentifiers": {} // Error: At least one device identifier required
}
```

#### Invalid Anonymous User with Authentication

```json
{
  "userType": "ANONYMOUS",
  "fcaid": "flipaclip-account-123", // Error: Anonymous users cannot have fcaid
  "deviceIdentifiers": {
    "gaid": ["********-8cf0-11bd-b23e-10b96e40000d"]
  }
}
```

#### Invalid Device Identifiers Placement

```json
{
  "userType": "REGISTERED",
  "deviceIdentifiers": {
    "fcaid": "flipaclip-account-123", // Error: fcaid must be at root level
    "fcid": "flipaclip-id-456", // Error: fcid must be at root level
    "gaid": ["********-8cf0-11bd-b23e-10b96e40000d"]
  }
}
```

#### Attempting to Modify Immutable FCID

```json
{
  "userType": "REGISTERED",
  "fcaid": "flipaclip-account-123",
  "fcid": "new-fcid-456", // Error: Cannot modify immutable fcid
  "deviceIdentifiers": {
    "idfa": ["BDCE8411-F598-4FC9-A315-EC538ED37CD1"]
  }
}
```

## Error Responses

Error responses follow a standard format:

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "deviceIdentifiers",
      "message": "At least one device identifier is required"
    }
  ]
}
```

Common status codes:

- 400: Bad Request (validation errors)
- 409: Conflict (e.g., attempting to modify immutable fields)
- 422: Unprocessable Entity (valid format but invalid business rules)

## Best Practices

1. **Device Identifiers**

   - Always validate device identifiers before sending
   - Remove any whitespace from identifier values
   - Ensure identifiers follow platform-specific formats

2. **User Type Handling**

   - Always use uppercase values for UserType enum
   - Validate user type before processing other fields
   - Apply appropriate validation rules based on user type

3. **Error Handling**

   - Always check for error responses
   - Handle validation errors gracefully
   - Implement appropriate retry logic for transient failures

4. **Immutable Fields**

   - Never attempt to modify fcid once set
   - Cache immutable values when possible
   - Validate immutable fields haven't changed during updates

5. **Request Processing**
   - Validate payload structure before processing
   - Implement proper error handling for all scenarios
   - Log validation failures for debugging purposes
