# Database Performance Stress Testing Guide

This guide explains how to use the stress testing suite to measure the performance improvements from the database optimizations.

## 🎯 Overview

The stress testing suite simulates real-world usage patterns to measure:

- **Query performance** for device ID lookups (the main bottleneck)
- **User creation and merge operations** throughput
- **Batch operation** efficiency
- **Overall system responsiveness** under load

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Ensure server is running
npm run start:dev

# Verify server health
curl http://localhost:3000/health
```

### 2. Run Quick Performance Test

```bash
# Quick test (5 users, 2 minutes)
npm run perf-test:quick

# Or using the shell script directly
./scripts/run-performance-tests.sh quick
```

### 3. Run Comprehensive Test Suite

```bash
# Full test suite with multiple scenarios
npm run perf-test:comprehensive
```

## 📊 Test Scenarios

### Available Test Scenarios

| Scenario   | Users | Duration | Throttling       | Purpose                   |
| ---------- | ----- | -------- | ---------------- | ------------------------- |
| **Quick**  | 5     | 2 min    | 30 workflows/min | Basic functionality check |
| **Medium** | 10    | 5 min    | 30 workflows/min | Normal load simulation    |
| **Heavy**  | 20    | 3 min    | 30 workflows/min | High load stress test     |
| **Burst**  | 50    | 1 min    | 30 workflows/min | Spike load handling       |

**Note:** Tests are now time-based with automatic throttling to prevent system overload.

### Custom Test Configuration

```bash
# Custom test with specific parameters
./scripts/run-performance-tests.sh custom \
  --users 25 \
  --ops 75 \
  --duration 10 \
  --url http://localhost:3000
```

## 🔍 What Gets Tested

### 1. User Creation (Anonymous)

- **Pattern**: Create users with device identifiers (adid, gaid)
- **Measures**: Response time for user creation
- **Target**: <100ms per creation

### 2. Device ID Lookups (Critical Path)

- **Pattern**: Query users by device ID (the main performance bottleneck)
- **Measures**: Response time for array-based queries
- **Target**: <50ms per lookup (down from 138ms)

### 3. User Merge Operations

- **Pattern**: Convert anonymous users to registered users
- **Measures**: Merge operation performance
- **Target**: <200ms per merge

### 4. Batch Operations

- **Pattern**: Multiple concurrent operations
- **Measures**: Throughput and queue efficiency
- **Target**: 10x improvement over individual operations

## 📈 Performance Metrics

### Response Time Metrics

- **Average Response Time**: Mean time across all requests
- **P95 Response Time**: 95th percentile (most users experience this or better)
- **P99 Response Time**: 99th percentile (worst-case for most users)
- **Min/Max Response Time**: Best and worst individual requests

### Throughput Metrics

- **Requests Per Second (RPS)**: Overall system throughput
- **Success Rate**: Percentage of successful requests
- **Error Rate**: Percentage of failed requests

### Database Metrics

- **Cache Hit Ratio**: Should be >95%
- **Active Connections**: Should be <50 under normal load
- **Slow Queries**: Should decrease significantly after optimizations

## 🎯 Expected Performance Improvements

### Before Optimizations

- **Device ID Queries**: ~138ms average
- **Merged FCID Queries**: ~109ms average
- **CPU Usage**: 99%
- **Query Pattern**: Sequential scans with ANY() operator

### After Optimizations

- **Device ID Queries**: <50ms average (64% improvement)
- **Merged FCID Queries**: <40ms average (63% improvement)
- **CPU Usage**: <70% (30% reduction)
- **Query Pattern**: Index scans with EXISTS() operator

## 📋 Test Commands Reference

### Basic Commands

```bash
# Check server status
./scripts/run-performance-tests.sh check

# Run database optimization
./scripts/run-performance-tests.sh optimize

# Analyze current performance
./scripts/run-performance-tests.sh analyze
```

### Test Scenarios

```bash
# Quick test (development)
npm run perf-test:quick

# Medium load test
./scripts/run-performance-tests.sh medium

# Heavy load test
./scripts/run-performance-tests.sh heavy

# Comprehensive test suite
npm run perf-test:comprehensive
```

### Custom Tests

```bash
# Test against dev server
./scripts/run-performance-tests.sh medium --url https://roshi.dev.flipaclip.com

# High concurrency test
./scripts/run-performance-tests.sh custom --users 100 --ops 50 --duration 5

# Long-running stability test
./scripts/run-performance-tests.sh custom --users 20 --ops 500 --duration 30
```

## 📊 Interpreting Results

### Success Criteria

#### ✅ Good Performance

- Average response time <100ms
- P95 response time <200ms
- Success rate >99%
- RPS >50 for medium load
- Cache hit ratio >95%

#### ⚠️ Acceptable Performance

- Average response time <200ms
- P95 response time <500ms
- Success rate >95%
- RPS >20 for medium load
- Cache hit ratio >90%

#### ❌ Poor Performance

- Average response time >500ms
- P95 response time >1000ms
- Success rate <95%
- RPS <20 for medium load
- Cache hit ratio <90%

### Sample Output

```
📊 STRESS TEST RESULTS
================================================================================

⏱️  Test Duration: 300.45 seconds
📈 Total Requests: 2,450
✅ Successful: 2,448
❌ Failed: 2
🚀 Overall RPS: 8.15
⚡ Average Response Time: 67.23ms

📋 Operation Breakdown:
--------------------------------------------------------------------------------

🔸 USER_LOOKUP_DEVICE_ID
   Requests: 1,225
   RPS: 4.08
   Avg: 45.67ms
   Min: 12.34ms
   Max: 234.56ms
   P95: 89.12ms
   P99: 156.78ms

🔸 USER_CREATION
   Requests: 980
   RPS: 3.26
   Avg: 78.90ms
   Min: 23.45ms
   Max: 345.67ms
   P95: 145.23ms
   P99: 234.56ms
```

## 🔧 Troubleshooting

### Common Issues

#### High Response Times

```bash
# Check database metrics
curl http://localhost:3000/health/database-metrics

# Run database analysis
curl http://localhost:3000/health/analyze-database

# Check for slow queries
grep "Slow query" logs/application.log
```

#### High Error Rates

```bash
# Check server logs
tail -f logs/application.log

# Verify server health
curl http://localhost:3000/health

# Check database connection
npm run optimize-db
```

#### Low Throughput

```bash
# Check system resources
top
iostat 1

# Monitor database connections
curl http://localhost:3000/health/database-metrics | jq '.connectionCount'

# Check Redis connectivity
redis-cli ping
```

## 📁 Output Files

### Test Results

- `stress-test-results-{scenario}.json` - Detailed test results
- `db-metrics-{timestamp}.json` - Database performance snapshots

### Key Metrics in Results File

```json
{
  "summary": {
    "totalRequests": 2450,
    "totalSuccessful": 2448,
    "totalFailed": 2,
    "overallRPS": 8.15,
    "averageResponseTime": 67.23
  },
  "metrics": [
    {
      "operation": "user_lookup_device_id",
      "averageResponseTime": 45.67,
      "p95ResponseTime": 89.12,
      "requestsPerSecond": 4.08
    }
  ],
  "databaseMetrics": {
    "before": { "cacheHitRatio": 94.5, "slowQueries": 15 },
    "after": { "cacheHitRatio": 96.2, "slowQueries": 3 }
  }
}
```

## 🎯 Best Practices

### Before Testing

1. **Optimize database**: Run `npm run optimize-db`
2. **Clear caches**: Restart Redis if needed
3. **Check baseline**: Run `analyze` command first
4. **Monitor resources**: Ensure adequate CPU/memory

### During Testing

1. **Monitor logs**: Watch for errors or warnings
2. **Check metrics**: Use `/health/database-metrics` endpoint
3. **Avoid interference**: Don't run other heavy operations
4. **Document environment**: Note any special conditions

### After Testing

1. **Analyze results**: Compare before/after metrics
2. **Save reports**: Archive test results for comparison
3. **Identify bottlenecks**: Focus on slowest operations
4. **Plan improvements**: Use data to guide optimizations

## 🚀 Continuous Performance Testing

### Integration with CI/CD

```yaml
# Example GitHub Actions workflow
- name: Run Performance Tests
  run: |
    npm run start:dev &
    sleep 30
    npm run perf-test:quick
    kill %1
```

### Regular Monitoring

```bash
# Daily quick check
0 9 * * * cd /path/to/roshi && npm run perf-test:quick

# Weekly comprehensive test
0 2 * * 0 cd /path/to/roshi && npm run perf-test:comprehensive
```

This stress testing suite provides comprehensive performance measurement capabilities to validate the effectiveness of your database optimizations and ensure continued performance as the system evolves.
