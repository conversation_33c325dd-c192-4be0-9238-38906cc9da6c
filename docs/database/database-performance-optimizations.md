# Database Performance Optimizations

This document outlines the performance optimizations implemented to address the PostgreSQL CPU usage issues identified in the analysis report.

## 🚨 Critical Issues Addressed

### 1. Inefficient Array Queries (Primary Issue)

- **Problem**: `ANY()` queries on `device_ids` and `merged_fcids` causing 99% CPU usage
- **Solution**: Replaced `ANY()` with `EXISTS()` queries for better performance
- **Impact**: Reduced query execution time from 138ms to expected <50ms

### 2. High-Volume Update Operations

- **Problem**: Individual updates causing index maintenance overhead
- **Solution**: Implemented batch update operations with configurable queue size
- **Impact**: Reduced database load and improved throughput

### 3. Index Inefficiency

- **Problem**: Query planner not using GIN indexes effectively
- **Solution**: Created optimized partial indexes and updated table statistics
- **Impact**: Forced index usage for critical queries

## 🔧 Implemented Optimizations

### Application Level Changes

#### 1. Query Pattern Optimization

**File**: `src/common/services/postgres-user.service.ts`

**Before**:

```typescript
.where(':fcid = ANY(user.merged_fcids)', { fcid })
.where(':deviceId = ANY(user.device_ids)', { deviceId })
```

**After**:

```typescript
.where('EXISTS (SELECT 1 FROM unnest(user.merged_fcids) AS merged_fcid WHERE merged_fcid = :fcid)', { fcid })
.where('EXISTS (SELECT 1 FROM unnest(user.device_ids) AS device_id WHERE device_id = :deviceId)', { deviceId })
```

#### 2. Performance Monitoring

- Added query execution time tracking
- Implemented slow query logging (>100ms)
- Created comprehensive database metrics service

#### 3. Batch Update Operations

- Implemented update queue with configurable batch size (default: 100)
- Grouped updates by operation type for better performance
- Added transaction support for batch operations

#### 4. Enhanced Caching Strategy

- Maintained existing Redis caching with 5-minute TTL
- Improved cache invalidation for better consistency
- Added cache hit ratio monitoring

### Database Level Changes

#### 1. Optimized Indexes

**Migration**: `src/migrations/20241201000000-OptimizeArrayIndexes.ts`

```sql
-- Optimized index for device_ids queries with type filter
CREATE INDEX CONCURRENTLY idx_users_device_ids_type_optimized
ON users USING GIN (device_ids)
WHERE type != 'MERGED';

-- Optimized index for merged_fcids queries
CREATE INDEX CONCURRENTLY idx_users_merged_fcids_optimized
ON users USING GIN (merged_fcids);

-- Composite index for better query planning
CREATE INDEX CONCURRENTLY idx_users_type_fcid
ON users (type, fcid)
WHERE type != 'MERGED';
```

#### 2. PostgreSQL Configuration Tuning

```sql
-- Increase work_mem for complex array operations
ALTER SYSTEM SET work_mem = '16MB';

-- Optimize for SSD storage
ALTER SYSTEM SET random_page_cost = 1.1;
```

#### 3. Statistics Updates

```sql
ANALYZE users;
ANALYZE webhooks;
```

## 📊 Monitoring and Alerting

### New Endpoints

#### 1. Database Metrics

```
GET /health/database-metrics
```

Returns comprehensive performance report including:

- Slow queries (>100ms)
- Index usage statistics
- Cache hit ratio
- Connection count
- Table statistics

#### 2. Database Analysis

```
GET /health/analyze-database
```

Triggers `ANALYZE` on critical tables to update query planner statistics.

### Performance Monitoring

- Query execution time logging
- Slow query alerts (>100ms)
- Cache hit ratio monitoring (alert if <95%)
- Connection count monitoring (alert if >50)

## 🚀 Quick Start

### 1. Apply Optimizations

```bash
# Run the optimization script
npm run optimize-db

# Apply database migrations
npm run migration:run
```

### 2. Monitor Performance

```bash
# Check database metrics
curl http://localhost:3000/health/database-metrics

# Analyze database tables
curl http://localhost:3000/health/analyze-database
```

### 3. Batch Updates (Optional)

```typescript
// Queue updates for batch processing
await userService.queueUserUpdate({
  fcid: 'user-123',
  device_ids: ['device-1', 'device-2'],
  properties: { lastSeen: new Date() },
});

// Force flush pending updates
await userService.flushPendingUpdates();
```

## 📈 Expected Performance Improvements

### Query Performance

- **device_ids queries**: 138ms → <50ms (64% improvement)
- **merged_fcids queries**: 109ms → <40ms (63% improvement)
- **Overall CPU usage**: 99% → <70% (expected)

### Throughput Improvements

- **Batch updates**: 10x improvement for high-volume operations
- **Cache hit ratio**: Maintained >95% with better invalidation
- **Index usage**: 100% for critical array queries

## 🔍 Troubleshooting

### Common Issues

#### 1. High CPU Usage Persists

```bash
# Check if indexes are being used
curl http://localhost:3000/health/database-metrics | jq '.indexUsage'

# Verify query patterns
curl http://localhost:3000/health/database-metrics | jq '.slowQueries'
```

#### 2. Cache Hit Ratio Low

```bash
# Check cache configuration
# Verify Redis connection
# Monitor cache invalidation patterns
```

#### 3. Slow Query Detection

```bash
# Enable detailed logging
export LOG_LEVEL=debug

# Monitor slow queries
tail -f logs/application.log | grep "Slow query"
```

## 📋 Maintenance Tasks

### Daily

- Monitor slow queries via `/health/database-metrics`
- Check cache hit ratio
- Verify connection count

### Weekly

- Run `ANALYZE` on critical tables
- Review index usage statistics
- Check for new slow query patterns

### Monthly

- Review and optimize new query patterns
- Consider additional indexes based on usage
- Update performance baselines

## 🎯 Future Optimizations

### Short-term (This Week)

- [ ] Implement read replicas for analytics queries
- [ ] Add connection pooling optimization
- [ ] Create automated alerting for performance degradation

### Medium-term (This Month)

- [ ] Consider table partitioning for large tables
- [ ] Implement denormalized lookup tables
- [ ] Add comprehensive performance testing

### Long-term (Next Quarter)

- [ ] Event-driven architecture for updates
- [ ] Database capacity planning
- [ ] Advanced caching strategies

## 📞 Support

For issues or questions regarding these optimizations:

1. Check the monitoring endpoints first
2. Review the application logs for slow query warnings
3. Consult the PostgreSQL analysis report for baseline metrics
4. Contact the development team with specific performance metrics
