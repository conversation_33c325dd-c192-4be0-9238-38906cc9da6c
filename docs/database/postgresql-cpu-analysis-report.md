# PostgreSQL High CPU Usage Analysis Report

**Date**: May 28 2025
**Database**: PostgreSQL 16.6 on AWS RDS  
**Instance**: db.t4g.xlarge (4 vCPUs, 16 GB RAM)  
**Issue**: 99% CPU usage causing performance degradation

## Executive Summary

Our PostgreSQL database is experiencing severe CPU bottlenecks due to inefficient array queries on the `users` table. The primary culprits are queries performing full table scans instead of using available GIN indexes, combined with high-volume update operations.

## Database Statistics

- **Users table**: 1,045,981 rows, 1.3 GB
- **Webhooks table**: 6,179,669 rows, 4.5 GB
- **Total database size**: ~6 GB
- **Current load**: Multiple concurrent queries causing 99% CPU utilization

## Critical Issues Identified

### 1. Inefficient Array Queries (Primary Issue)

#### Query Pattern 1: `merged_fcids` Array Search

```sql
SELECT * FROM users WHERE $1 = ANY(merged_fcids)
```

- **Calls**: 496,159 executions
- **Total execution time**: 54,015,836 ms (54 seconds cumulative)
- **Average**: 108.87 ms per query
- **Problem**: Using parallel sequential scan instead of GIN index

#### Query Pattern 2: `device_ids` Array Search with Type Filter

```sql
SELECT * FROM users WHERE type != 'MERGED' AND $1 = ANY(device_ids)
```

- **Calls**: 2,341,456 executions
- **Total execution time**: 325,366,635 ms (325 seconds cumulative)
- **Average**: 138.96 ms per query
- **Problem**: **HIGHEST CPU CONSUMER** - not using partial GIN indexes effectively

### 2. High-Volume Update Operations

```sql
UPDATE users SET device_ids = $1, merged_fcids = $2, updated_at = $3 WHERE fcid IN ($4)
```

- **Calls**: 831,728 executions
- **Total execution time**: 863,876,822 ms (863 seconds cumulative)
- **Average**: 1,038.65 ms per update
- **Problem**: Causing index maintenance overhead and potential lock contention

### 3. Index Inefficiency

Despite having multiple GIN indexes, the query planner chooses sequential scans:

- `idx_users_merged_fcids_gin` - not being used effectively
- `idx_users_device_ids_type_gin` - partial index not optimal for query patterns
- Multiple duplicate indexes causing maintenance overhead

## Immediate Solutions

### 1. Database Optimizations

#### Update Table Statistics

```sql
-- Critical: Run immediately to help query planner
ANALYZE users;
ANALYZE webhooks;
```

#### Index Optimization

```sql
-- Create more specific index for the problematic query
CREATE INDEX CONCURRENTLY idx_users_device_ids_type_optimized
ON users USING GIN (device_ids)
WHERE type != 'MERGED';

-- Consider dropping duplicate indexes after testing
-- DROP INDEX IF EXISTS idx_users_device_ids_type;
-- DROP INDEX IF EXISTS idx_users_merged_fcids; -- keep only _gin version
```

#### Query Planner Tuning

```sql
-- Temporarily test index usage
SET enable_seqscan = off;
-- Test your queries, then reset:
SET enable_seqscan = on;

-- Adjust work_mem for complex array operations
SET work_mem = '16MB';

-- Optimize for SSD storage
SET random_page_cost = 1.1;
```

### 2. API/Application Level Fixes

#### Implement Query Caching

```typescript
// Add Redis caching for frequent user lookups
class UserService {
  async findByDeviceId(deviceId: string): Promise<User[]> {
    const cacheKey = `user:device:${deviceId}`;
    let users = await this.redis.get(cacheKey);

    if (!users) {
      users = await this.userRepository.findByDeviceId(deviceId);
      await this.redis.setex(cacheKey, 300, JSON.stringify(users)); // 5min cache
    }

    return JSON.parse(users);
  }
}
```

#### Batch Operations

```typescript
// Instead of individual updates, batch them
class UserUpdateService {
  private updateQueue: UserUpdate[] = [];

  async queueUpdate(update: UserUpdate) {
    this.updateQueue.push(update);

    if (this.updateQueue.length >= 100) {
      await this.flushUpdates();
    }
  }

  private async flushUpdates() {
    // Batch update multiple users at once
    await this.userRepository.batchUpdate(this.updateQueue);
    this.updateQueue = [];
  }
}
```

#### Optimize Query Patterns

```typescript
// Consider using EXISTS instead of ANY for better performance
const query = `
  SELECT u.* FROM users u 
  WHERE u.type != 'MERGED' 
  AND EXISTS (
    SELECT 1 FROM unnest(u.device_ids) AS device_id 
    WHERE device_id = $1
  )
`;
```

### 3. Monitoring and Alerting

#### Add Query Performance Monitoring

```sql
-- Enable pg_stat_statements for ongoing monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Monitor slow queries
SELECT query, calls, total_exec_time, mean_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 100
ORDER BY total_exec_time DESC;
```

#### Application Metrics

```typescript
// Add query timing metrics
class DatabaseMetrics {
  static trackQuery(queryName: string, duration: number) {
    // Send to your monitoring system (DataDog, New Relic, etc.)
    metrics.histogram('db.query.duration', duration, {
      query: queryName,
    });
  }
}
```

## Long-term Recommendations

### 1. Database Design Improvements

#### Consider Denormalization

```sql
-- Create separate lookup table for frequently searched device_ids
CREATE TABLE user_device_lookup (
  device_id TEXT NOT NULL,
  fcid VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  PRIMARY KEY (device_id, fcid)
);

CREATE INDEX idx_user_device_lookup_device_id ON user_device_lookup(device_id);
```

#### Table Partitioning

```sql
-- Consider partitioning users table by type or date
CREATE TABLE users_active PARTITION OF users FOR VALUES IN ('ACTIVE', 'PREMIUM');
CREATE TABLE users_merged PARTITION OF users FOR VALUES IN ('MERGED');
```

### 2. Infrastructure Improvements

#### Read Replicas

- Implement read replicas for read-heavy operations
- Route analytics queries to read replicas
- Use connection pooling (PgBouncer)

#### Caching Strategy

- Implement Redis for frequently accessed user data
- Cache query results for 5-15 minutes
- Use cache invalidation on user updates

### 3. Application Architecture

#### Event-Driven Updates

```typescript
// Use event sourcing for user updates
class UserEventHandler {
  async handleDeviceIdUpdate(event: DeviceIdUpdateEvent) {
    // Update user asynchronously
    await this.updateQueue.add('updateUser', event);
  }
}
```

## Action Plan (Priority Order)

### Immediate (Today)

1. ✅ **Run ANALYZE on users table**
2. ✅ **Monitor current active queries**
3. ✅ **Implement query result caching**
4. ✅ **Add query performance logging**

### Short-term (This Week)

1. 🔄 **Create optimized indexes**
2. 🔄 **Implement batch update operations**
3. 🔄 **Add database monitoring dashboard**
4. 🔄 **Optimize most expensive queries**

### Medium-term (This Month)

1. 📋 **Implement read replicas**
2. 📋 **Design denormalized lookup tables**
3. 📋 **Implement comprehensive caching strategy**
4. 📋 **Review and optimize all array-based queries**

### Long-term (Next Quarter)

1. 📋 **Consider table partitioning**
2. 📋 **Implement event-driven architecture**
3. 📋 **Database capacity planning**
4. 📋 **Performance testing framework**

## Monitoring Checklist

- [ ] Set up alerts for queries > 1 second
- [ ] Monitor index usage statistics
- [ ] Track cache hit ratios
- [ ] Monitor connection pool metrics
- [ ] Set up CPU/memory alerts
- [ ] Track slow query logs

## Notes for Development Team

1. **Avoid N+1 queries** when working with user arrays
2. **Always use prepared statements** for parameterized queries
3. **Consider pagination** for large result sets
4. **Test query performance** in staging with production data volumes
5. **Use EXPLAIN ANALYZE** before deploying new queries

---

**Report prepared by**: Database Performance Analysis  
**Next review**: Weekly until CPU usage stabilizes below 70%
