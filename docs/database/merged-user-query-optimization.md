# Merged User Query Optimization

## Problem Statement

The `findMergedUser` query was experiencing slow performance, with execution times ranging from 134ms to 470ms. This query is critical for user merge operations and is called frequently during user updates.

### Original Query Performance Issues

- **Query**: `SELECT * FROM users WHERE merged_fcids @> ARRAY['fcid_value']`
- **Performance**: 134ms - 470ms (slow)
- **Frequency**: Called frequently during user operations
- **Impact**: High CPU usage and slow response times

## Root Cause Analysis

1. **Inefficient GIN Index**: The original index `idx_users_merged_fcids_optimized` was a basic GIN index without filtering
2. **No Type Filtering**: Query searched ALL users, including MERGED users which shouldn't contain valid `merged_fcids`
3. **Array Size Growth**: As users get merged more, the `merged_fcids` arrays grow larger, making the GIN index less efficient
4. **Query Pattern**: The `@>` operator can be slow for large arrays

## Optimization Solutions

### 1. Database-Level Optimizations

#### A. Enhanced Indexes

**Migration**: `20250226000000-OptimizeMergedFcidsQuery.ts`

**New Indexes Created**:

1. **Optimized GIN Index with Filtering**:

   ```sql
   CREATE INDEX CONCURRENTLY idx_users_merged_fcids_optimized_v2
   ON users USING GIN (merged_fcids)
   WHERE type != 'MERGED'::user_type_enum
     AND merged_fcids IS NOT NULL
     AND array_length(merged_fcids, 1) > 0;
   ```

2. **Partial Index for Users with Merged FCIDs**:
   ```sql
   CREATE INDEX CONCURRENTLY idx_users_has_merged_fcids
   ON users (fcid, type)
   WHERE type != 'MERGED'::user_type_enum
     AND merged_fcids IS NOT NULL
     AND array_length(merged_fcids, 1) > 0;
   ```

Note: The merged_fcids column stores an array of strings (UUIDs), so the default GIN operator class is used. The previously documented use of gin\_\_int_ops is incorrect and would cause an error. The above indexes match the actual migration implementation (see 20250226000000-OptimizeMergedFcidsQuery.ts).

#### B. Materialized View for Fast Lookups

**Materialized View**:

```sql
CREATE MATERIALIZED VIEW mv_merged_users_lookup AS
SELECT
  fcid,
  unnest(merged_fcids) as merged_fcid,
  type,
  created_at,
  updated_at
FROM users
WHERE type != 'MERGED'::user_type_enum
  AND merged_fcids IS NOT NULL
  AND array_length(merged_fcids, 1) > 0;
```

**Indexes on Materialized View**:

```sql
CREATE INDEX idx_mv_merged_users_lookup_fcid ON mv_merged_users_lookup (merged_fcid, fcid);
CREATE UNIQUE INDEX idx_mv_merged_users_lookup_unique ON mv_merged_users_lookup (merged_fcid, fcid);
```

#### C. Automatic Refresh Functions

**Refresh Function**:

```sql
CREATE OR REPLACE FUNCTION refresh_merged_users_lookup()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_merged_users_lookup;
END;
$$ LANGUAGE plpgsql;
```

**Trigger for Automatic Updates**:

```sql
CREATE TRIGGER trigger_refresh_merged_users_lookup
  AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW
  EXECUTE FUNCTION trigger_refresh_merged_users_lookup();
```

### 2. Application-Level Optimizations

#### A. Optimized Query Method

**File**: `src/common/services/postgres-user.service.ts`

**New Implementation**:

```typescript
async findMergedUser(fcid: string): Promise<User | null> {
  return this.trackQuery('findMergedUser', async () => {
    // First try using the materialized view for fastest lookup
    try {
      const mergedUser = await this.userRepository
        .createQueryBuilder('user')
        .innerJoin(
          'mv_merged_users_lookup',
          'mv',
          'mv.fcid = user.fcid AND mv.merged_fcid = :fcid',
          { fcid }
        )
        .where('user.type != :type', { type: UserType.MERGED })
        .getOne();

      if (mergedUser) {
        return mergedUser;
      }
    } catch (error) {
      // Fallback to GIN index if materialized view fails
      this.logger.debug('Materialized view lookup failed, falling back to GIN index');
    }

    // Fallback: Use optimized GIN index query with type filtering
    const mergedUser = await this.userRepository
      .createQueryBuilder('user')
      .where('user.type = :type', { type: UserType.REGISTERED })
      .andWhere('user.merged_fcids IS NOT NULL')
      .andWhere('array_length(user.merged_fcids, 1) > 0')
      .andWhere('user.merged_fcids @> ARRAY[:fcid]', { fcid })
      .getOne();

    return mergedUser;
  });
}
```

#### B. Materialized View Refresh

**Method Added**:

```typescript
async refreshMergedUsersMaterializedView(): Promise<void> {
  try {
    await this.userRepository.query('SELECT refresh_merged_users_lookup();');
    this.logger.debug('Successfully refreshed merged users materialized view');
  } catch (error) {
    this.logger.warn('Failed to refresh merged users materialized view');
  }
}
```

#### C. Batch Update Integration

**Enhanced Batch Updates**:

```typescript
private async batchUpdateMergedFcids(updates: UserUpdate[]): Promise<void> {
  // ... existing batch update logic ...

  // Refresh materialized view after batch updates for optimal performance
  await this.refreshMergedUsersMaterializedView();
}
```

### 3. Monitoring and Maintenance

#### A. Performance Testing

**Test Script**: `scripts/test-merged-user-performance.ts`

**Usage**:

```bash
npm run test:merged-user-perf
```

**Features**:

- Tests existing merged users
- Tests non-existent fcids
- Checks materialized view status
- Reports query performance

#### B. NestJS Scheduled Tasks for Materialized View Refresh

**File**: `src/common/services/materialized-view-refresh.service.ts`

**Scheduled Refresh**:

```typescript
@Cron('*/5 * * * *')
async refreshMergedUsersMaterializedView(): Promise<void> {
  try {
    this.logger.log('🔄 Scheduled: Refreshing merged users materialized view...');
    await this.postgresUserService.refreshMergedUsersMaterializedView();
    this.logger.log('✅ Successfully refreshed merged users materialized view');
  } catch (error) {
    this.logger.error('Failed to refresh merged users materialized view', {
      error: error.message,
      stack: error.stack,
    });
  }
}

@Cron(CronExpression.EVERY_DAY_AT_2AM)
async refreshAllMaterializedViews(): Promise<void> {
  // Daily refresh of all materialized views
}
```

**Health Check Endpoint**:

```typescript
@Get('materialized-view')
async getMaterializedViewHealth() {
  const isHealthy = await this.materializedViewService.checkMaterializedViewHealth();
  return {
    status: isHealthy ? 'healthy' : 'unhealthy',
    materializedView: 'mv_merged_users_lookup',
    accessible: isHealthy,
    timestamp: new Date().toISOString(),
  };
}
```

## Performance Improvements

### Expected Results

- **Query Time**: Reduced from 134-470ms to <50ms
- **CPU Usage**: Significant reduction in database CPU usage
- **Scalability**: Better performance as data grows
- **Reliability**: Fallback mechanisms ensure query always works

### Query Execution Plan Improvements

**Before Optimization**:

- Sequential scan on large table
- No type filtering
- Inefficient GIN index usage

**After Optimization**:

- Index-only scans using materialized view
- Type filtering reduces scan scope
- Optimized GIN indexes with proper filtering
- Fallback to efficient GIN index queries

## Deployment Steps

### 1. Run Migration

```bash
npm run migration:run
```

### 2. Test Performance

```bash
npm run test:merged-user-perf
```

### 3. Monitor Results

- Check slow query logs
- Monitor CPU usage
- Verify materialized view refresh

### 4. Rollback Plan

If issues occur, the migration includes a complete rollback:

```bash
npm run migration:revert
```

## Monitoring and Alerts

### Key Metrics to Monitor

1. **Query Performance**: `findMergedUser` execution time
2. **Materialized View**: Refresh success/failure rate
3. **Index Usage**: GIN index hit rates
4. **CPU Usage**: Database CPU utilization

### Alert Thresholds

- Query time > 100ms
- Materialized view refresh failures
- High CPU usage (>80%)

## Conclusion

This optimization provides a comprehensive solution to the `findMergedUser` performance issues through:

1. **Multi-layered optimization**: Database indexes + materialized view + application logic
2. **Fallback mechanisms**: Ensures reliability even if materialized view fails
3. **Automatic maintenance**: Scheduled refresh and trigger-based updates
4. **Performance monitoring**: Comprehensive testing and monitoring tools

The solution is designed to scale with data growth and provides immediate performance improvements while maintaining data consistency and reliability.
