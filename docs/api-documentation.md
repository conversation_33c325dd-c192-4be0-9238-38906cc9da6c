# Roshi API Documentation

## Base URL

The base URL for all endpoints is `/users`.

**Behavior:**

- If any provided identifier matches an existing user, the system updates that user's record instead of creating a duplicate user
- New identifiers are merged with existing ones, creating a comprehensive user profile
- Existing identifiers are never overwritten or removed during updates
- The system maintains a consistent user identity across all provided identifiers
- All provided non-null and non-undefined identifiers are associated with the same user record
- The response includes the complete set of identifiers associated with the user
- Null, undefined, or empty string identifiers are ignored during updates to preserve existing data
- The system automatically resolves conflicts when the same identifier type is provided multiple times
- Updates to user attributes are merged with existing data, with new values taking precedence

## Authentication

All endpoints are secured using `AuthGuard`. Include appropriate authentication headers with each request.

## Endpoints

### 1. Create/Update User

**POST /users**

Creates a new user or updates an existing user based on provided identifiers.

#### Request Body Schema

```json
{
  "fcaid": "string",               // Required for registered users
  "installed_at": 1707194321000,   // Required for new user creation (timestamp in milliseconds)
  "fcid": "string",                // Used for anonymous users
  "identifiers": {
    "idfa": ["string"],           // iOS Advertising ID
    "idfv": ["string"],           // iOS Vendor ID
    "gaid": ["string"],           // Google Advertising ID
    "adid": ["string"]            // Android Device ID
  },
  "properties": {
    "totalAdRevenue": number,       // Total advertising revenue
    "totalSubscriptionRevenue": number // Total subscription revenue
  },
  "type": "ANONYMOUS" | "REGISTERED" | "MERGED",
  "mergedFcids": ["string"]
}
```

#### Key Behaviors

**Anonymous Users:**

- Can update advertising IDs by providing vendor IDs and vice versa
- Empty arrays in identifiers preserve existing values
- At least one device identifier must be provided
- Conflicts with existing anonymous users result in 409 Conflict error

**Registered Users:**

- Must include `fcaid` in the request
- Can update any identifier, with new values merged with existing ones

**Merge Operations:**

- User merges are processed asynchronously
- The API returns immediately with the current state
- The merge operation continues in the background
- Both source and target users are updated atomically
- Merged users are marked with `type: "MERGED"` and reference the target user

#### Success Response

```json
{
  "fcid": "string",
  "fcaid": "string",
  "type": "ANONYMOUS" | "REGISTERED" | "MERGED",
  "identifiers": {
    "idfa": ["string"],
    "idfv": ["string"],
    "gaid": ["string"],
    "adid": ["string"]
  },
  "properties": {
    "totalAdRevenue": number,
    "totalSubscriptionRevenue": number,
    "totalRevenue": number,        // Calculated asynchronously
    "daysAfterInstall": number     // Calculated asynchronously
  },
  "created_at": number,
  "updated_at": number,
  "mergedFcids": ["string"],
  "merged_to": "string"           // Present for merged users
}
```

#### Error Responses

- **400 Bad Request**: Missing required identifiers or invalid payload
- **409 Conflict**: Identifiers already associated with different anonymous user

### 2. Get User

**GET /users**

Retrieves user information based on provided identifiers.

#### Query Parameters

Same schema as create/update request body, passed as query string parameters.

#### Success Response

Same as create/update endpoint response.

#### Error Responses

- **400 Bad Request**: No valid identifier provided
- **404 Not Found**: User not found with provided identifiers

### 3. Delete User

**DELETE /users**

Deletes a user based on provided identifiers. This operation is processed asynchronously.

#### Request Body Schema

```json
{
  "fcid": "string",
  "token": "string" // Required security token for delete operations
}
```

> **Important**: The `token` field is required for all delete operations as an additional security measure.

#### Success Response

```json
{
  "message": "User deletion queued successfully"
}
```

#### Error Responses

- **400 Bad Request**: Invalid or missing identifiers
- **404 Not Found**: User not found

### 4. Update User Properties

**PATCH /users/{fcid}/properties**

Updates user properties asynchronously.

#### Request Body Schema

```json
{
  "properties": {
    "totalAdRevenue": number,
    "totalSubscriptionRevenue": number,
    // ... other properties
  }
}
```

#### Success Response

```json
{
  "message": "Property update queued successfully"
}
```

### 5. Calculate Analytics

**POST /users/{fcid}/analytics**

Triggers asynchronous calculation of derived properties.

#### Success Response

```json
{
  "message": "Analytics calculation queued successfully"
}
```

## Important Notes

1. **Identifier Precedence**

   - Anonymous users are primarily identified by device identifiers
   - Registered users require valid `fcaid`

2. **Revenue Calculations**

   - `totalAdRevenue` and `totalSubscriptionRevenue` can be directly set or updated
   - `totalRevenue` is calculated asynchronously as the sum of ad and subscription revenue
   - `daysAfterInstall` is calculated asynchronously based on the installation date

3. **Device Identifiers**

   - Empty arrays preserve existing identifiers
   - New anonymous users must provide at least one device identifier

4. **User Types**

   - `ANONYMOUS`: Users identified by device identifiers
   - `REGISTERED`: Users with valid `fcaid`
   - `MERGED`: Users resulting from merging operations

5. **Asynchronous Operations**
   - Operations are processed in the order they are received
   - Failed operations are automatically retried
   - The system maintains consistency during concurrent operations
   - Long-running operations do not block the API response
