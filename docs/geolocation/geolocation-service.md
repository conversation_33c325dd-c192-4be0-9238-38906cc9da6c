# Geolocation Service

## Overview

The Geolocation Service is responsible for determining a user's country based on their IP address. This service is integrated with the user creation flow to automatically capture and store geographic information when a user is first created.

Key features:

- Asynchronous processing using Bull queues
- Integration with Kids Web Services (KWS) API for geolocation
- Dead Letter Queue (DLQ) for handling failed requests
- Automatic country detection on first user creation only
- Country code to country name conversion

## Architecture

The geolocation functionality is implemented using the following components:

1. **GeolocationModule**: Registers the Bull queues and provides the service
2. **GeolocationService**: Core service that handles API calls and queue operations
3. **GeolocationProcessor**: Processes jobs from the queue
4. **UsersService Integration**: Captures IP addresses during user creation
5. **KwsService Integration**: Provides geolocation data from the KWS API

### Flow Diagram

```mermaid
graph TD
    A[User Creation] --> B[Extract IP]
    B --> C[Queue Job]
    C --> D[Process Job]
    D --> E[Update User]
    D --> F[KWS API Call]
    F -->|Success| G[Country Code]
    G --> H[Country Name]
    F -->|Failure| I[DLQ]

```

## Configuration

The geolocation service uses the Kids Web Services (KWS) API. Configuration is managed through the application's config system:

```typescript
// In config.interface.ts
geolocation: {
  fallbackIp: string;
  skipLocalIps: boolean;
}

// KWS configuration
kws: {
  productClientId: string;
  apiKey: string;
  authUrl: string;
  apiUrl: string;
}
```

These settings can be configured in environment variables:

```
KWS_PRODUCT_CLIENT_ID=your_client_id
KWS_API_KEY=your_api_key
KWS_AUTH_URL=https://auth.kidswebservices.com/auth/realms/kws/protocol/openid-connect/token
KWS_API_URL=https://api.kidswebservices.com/v1/age
GEOLOCATION_FALLBACK_IP=*******
GEOLOCATION_SKIP_LOCAL_IPS=true
```

Default values are provided in each environment configuration file:

```typescript
// In development.config.ts
geolocation: {
  fallbackIp: process.env.GEOLOCATION_FALLBACK_IP || '*******',
  skipLocalIps: process.env.GEOLOCATION_SKIP_LOCAL_IPS === 'true' || true,
}
```

## Bull Queues

The service uses two Bull queues:

1. **geolocation**: Main queue for processing geolocation requests

   - Job name: `process-geolocation`
   - Default settings: 3 retry attempts with exponential backoff
   - Successful jobs are removed from the queue

2. **geolocation-dlq**: Dead Letter Queue for failed geolocation requests
   - Job name: `process-dlq`
   - Stores failed jobs for later analysis
   - Jobs are never automatically removed

## Implementation Details

### IP Address Extraction

The service extracts IP addresses from HTTP requests using the following headers (in order of preference):

- `x-forwarded-for` header (for clients behind proxies)
- `x-real-ip` header
- Socket's remote address

#### Local IP Handling

The service includes special handling for local/private IP addresses:

1. **Detection**: Identifies local IPs using patterns like `127.0.0.1`, `192.168.x.x`, etc.
2. **Fallback**: Uses a configurable fallback IP (default: `*******`) for local environments
3. **Skip Option**: Can be configured to skip geolocation for local IPs entirely

This is controlled by two configuration options:

- `fallbackIp`: The IP address to use when a local IP is detected
- `skipLocalIps`: Whether to skip geolocation for local IPs when no fallback is provided

```typescript
// In GeolocationService
private processIpAddress(ip: string): string | null {
  // Check if it's a local/private IP
  if (this.isLocalIp(ip)) {
    // If we should skip local IPs and not use fallback, return null
    if (this.skipLocalIps && !this.fallbackIp) {
      return null;
    }

    // If we have a fallback IP, use it
    if (this.fallbackIp) {
      this.logger.debug(`Using fallback IP ${this.fallbackIp} instead of local IP ${ip}`);
      return this.fallbackIp;
    }
  }

  return ip;
}
```

### KWS API Integration

The service calls the KWS API to get geolocation data:

```typescript
// In GeolocationService
private async getCountryFromIp(ip: string): Promise<string> {
  try {
    // Use the KWS service to get country information
    const ageGateData = await this.kwsService.getAgeGateData({
      ip: ip,
    });

    if (!ageGateData || !ageGateData.country) {
      throw new Error('Invalid response from KWS API');
    }

    // Convert country code to country name
    const countryName = this.getCountryNameFromCode(ageGateData.country);

    return countryName;
  } catch (error) {
    this.logger.error(`Geolocation API error for IP ${ip}: ${error.message}`);
    throw error;
  }
}
```

The KWS API response format is:

```json
{
  "country": "US",
  "region": "CA",
  "consentAge": 13,
  "userAge": 18,
  "underAgeOfDigitalConsent": false
}
```

The service converts country codes to country names using a dedicated utility function:

```typescript
import { getCountryNameFromCode } from '../common/utils/country-utils';

// Convert country code to country name if needed
// KWS returns country codes (e.g., "US"), but we want full country names
const countryName = getCountryNameFromCode(ageGateData.country);
```

### Data Storage

The country information is stored in the user's `properties` JSONB column as `countryKws`:

```typescript
// Update the properties with the new country
const updatedProperties = {
  ...user.properties,
  countryKws: country,
};

await this.userRepository.update({ fcid }, { properties: updatedProperties });
```

The `countryKws` property is marked as updatable in the user utils, meaning once set, it cannot be updated through normal user update operations. This ensures the geolocation data remains consistent.

### Error Handling

The service implements robust error handling:

1. **Retry Logic**: Failed API calls are retried up to 3 times with exponential backoff
2. **DLQ**: After maximum retries, failed jobs are moved to the DLQ
3. **Graceful Degradation**: User creation continues even if geolocation fails
4. **Logging**: All errors are logged for monitoring and debugging

## Dead Letter Queue (DLQ)

The DLQ stores jobs that failed after maximum retry attempts. Each entry contains:

- Original job data (fcid, IP address)
- Error details (message, stack trace)
- Timestamp of failure
- Number of retry attempts made

This information is valuable for:

- Debugging API integration issues
- Monitoring failure rates
- Manually reprocessing failed jobs

## Usage in Code

### Queueing a Geolocation Job

```typescript
// In UsersService
private queueGeolocationForNewUser(fcid: string): void {
  try {
    // Get the user's IP address from the request
    const ip = this.getUserIpAddress();

    if (ip) {
      this.logger.debug(`Queueing geolocation lookup for new user ${fcid} with IP ${ip}`);
      this.geolocationService.queueGeolocation(fcid, ip);
    }
  } catch (error) {
    // Don't fail user creation if geolocation fails
    this.logger.error(`Failed to queue geolocation for user ${fcid}: ${error.message}`);
  }
}

// In GeolocationService
async queueGeolocation(fcid: string, ip: string): Promise<void> {
  try {
    const redisConfig = this.configService.get('redis');
    await this.geoQueue.add(
      'process-geolocation',
      { fcid, ip },
      redisConfig.queue.defaultJobOptions,
    );
    this.logger.debug(`Queued geolocation job for user ${fcid} with IP ${ip}`);
  } catch (error) {
    this.logger.error(`Failed to queue geolocation job: ${error.message}`);
  }
}
```

### Processing a Geolocation Job

```typescript
// In GeolocationProcessor
@Process('process-geolocation')
async processGeolocation(job: Job<GeolocationJob>): Promise<void> {
  this.logger.debug(
    `Processing geolocation job ${job.id} for user ${job.data.fcid} with IP ${job.data.ip}`,
  );

  try {
    await this.geolocationService.processGeolocation(job.data);
    this.logger.debug(`Successfully processed geolocation job ${job.id}`);
  } catch (error) {
    this.logger.error(
      `Failed to process geolocation job ${job.id}: ${error.message}`,
    );
    throw error; // Let Bull handle retries
  }
}
```

## Testing

The geolocation service includes comprehensive tests:

1. **Unit Tests**: Test the GeolocationService in isolation
2. **Integration Tests**: Test the integration with UsersService
3. **Mock Tests**: Use axios mocking to test API interactions

Run the tests with:

```
npm test -- test/geolocation
```

## Troubleshooting

Common issues and solutions:

1. **API Rate Limiting**: If you encounter rate limiting, consider implementing a delay between requests
2. **Invalid IP Addresses**: Ensure proper IP address validation before calling the API
3. **DLQ Growth**: Monitor the DLQ size and implement alerts if it grows too large
4. **Missing Country Data**: Some IP addresses (especially internal ones) may not return country information
