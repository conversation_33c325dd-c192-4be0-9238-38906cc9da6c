# User Agent Parsing Usage

This document shows how to use the new user agent parsing utilities in your NestJS application.

## Overview

The user agent parsing utilities extract family and app version information from user agent strings in the format:

- `Android/X.X.X (757)`
- `iOS/X.X.X (757)`
- `Vegeta` (special case)

## Available Functions

### `parseUserAgent(userAgent: string): ParsedUserAgent`

Parses a user agent string and returns detailed information:

```typescript
interface ParsedUserAgent {
  family: 'Android' | 'iOS' | 'Vegeta' | 'Unknown';
  appVersion: string | null;
  buildNumber: string | null;
}
```

### `formatUserAgent(userAgent: string): { family: string; app_ver: string | null }`

Formats user agent information into a standardized object:

```typescript
{
  family: 'Android',
  app_ver: '7.2.3'
}
```

### `getUserAgentInfo(req: Request): ParsedUserAgent | null`

Gets parsed user agent information from an Express request (requires UserAgentMiddleware).

### `getFormattedUserAgent(req: Request): { family: string; app_ver: string | null }`

Gets formatted user agent information from an Express request (requires UserAgentMiddleware).

## Usage Examples

### In a Controller

```typescript
import { Controller, Get, Req } from '@nestjs/common';
import { Request } from 'express';
import { getFormattedUserAgent, getUserAgentInfo } from '../common/utils/user-agent.utils';

@Controller('example')
export class ExampleController {
  @Get('user-agent')
  getUserAgentInfo(@Req() req: Request) {
    // Get formatted user agent info
    const formatted = getFormattedUserAgent(req);
    // Returns: { family: 'Android', app_ver: '7.2.3' }

    // Get detailed user agent info
    const detailed = getUserAgentInfo(req);
    // Returns: { family: 'Android', appVersion: '7.2.3', buildNumber: '757' }

    return {
      formatted,
      detailed,
    };
  }
}
```

### In a Service

```typescript
import { Injectable } from '@nestjs/common';
import { parseUserAgent, formatUserAgent } from '../common/utils/user-agent.utils';

@Injectable()
export class ExampleService {
  processUserAgent(userAgentString: string) {
    // Parse user agent string directly
    const parsed = parseUserAgent(userAgentString);

    // Format for API response
    const formatted = formatUserAgent(userAgentString);

    // Use the information
    if (parsed.family === 'Android' && parsed.appVersion) {
      // Handle Android-specific logic
      console.log(`Android app version: ${parsed.appVersion}`);
    }

    return formatted;
  }
}
```

### With Error Logger

The error logger service now includes user agent information in error logs:

```typescript
import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import { ErrorLoggerService } from '../common/services/error-logger.service';

@Injectable()
export class ExampleService {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  async handleRequest(req: Request) {
    try {
      // Your business logic here
      throw new Error('Something went wrong');
    } catch (error) {
      // Log error with user agent information
      this.errorLogger.logError(error, req, {
        includeUserAgent: true,
        context: 'ExampleService.handleRequest',
      });

      // Or use the convenience method
      this.errorLogger.logErrorWithUserAgent(error, req, {
        context: 'ExampleService.handleRequest',
      });
    }
  }
}
```

The error log will now include structured user agent information:

```json
{
  "message": "Something went wrong",
  "context": "ExampleService.handleRequest",
  "userAgent": {
    "family": "Android",
    "appVersion": "7.2.3",
    "buildNumber": "757"
  },
  "exceptionType": "Error",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Manual Parsing

```typescript
import { parseUserAgent } from '../common/utils/user-agent.utils';

// Parse different user agent formats
const androidResult = parseUserAgent('Android/7.2.3 (757)');
// Returns: { family: 'Android', appVersion: '7.2.3', buildNumber: '757' }

const iosResult = parseUserAgent('iOS/4.2.12 (757)');
// Returns: { family: 'iOS', appVersion: '4.2.12', buildNumber: '757' }

const vegetaResult = parseUserAgent('Vegeta');
// Returns: { family: 'Vegeta', appVersion: null, buildNumber: null }
```

## Middleware Integration

The `UserAgentMiddleware` automatically parses and attaches user agent information to the request object. Make sure this middleware is applied to your routes:

```typescript
// In your module
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(UserAgentMiddleware).forRoutes('*');
  }
}
```

## Error Handling

The utilities handle various edge cases gracefully:

- Empty or null user agent strings
- Malformed user agent strings
- Unknown user agent families
- Missing version information

All functions return safe default values when parsing fails.

## Error Logger Integration

The error logger service now supports user agent information with these options:

- **`includeUserAgent: boolean`** - Whether to include user agent information (default: `true`)
- **`logErrorWithUserAgent()`** - Convenience method that always includes user agent information

This allows you to correlate errors with specific app versions and platforms, making debugging much easier.
