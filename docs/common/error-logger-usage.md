# ErrorLoggerService Usage Guide

## Overview

The `ErrorLoggerService` provides a unified, flexible approach to error logging throughout the application. It automatically extracts `fcid` fields, handles sensitive data masking, and provides configurable options for including stack traces and request details.

## Key Features

- **Unified Format**: Consistent error log structure across the application
- **Automatic FCID Extraction**: Extracts `fcid` from request body, query, headers, or params
- **Flexible Options**: Choose what to include (stack traces, request details, custom metadata)
- **Sensitive Data Masking**: Automatically masks authentication tokens and other sensitive fields
- **Multiple Convenience Methods**: Different methods for different use cases

## Basic Usage

### 1. Simple Error Logging

```typescript
import { ErrorLoggerService } from '../common/services/error-logger.service';

@Injectable()
export class MyService {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  async someMethod() {
    try {
      // Your code here
    } catch (error) {
      // Simple error logging with stack trace
      this.errorLogger.logSimpleError(error, 'some-fcid');
    }
  }
}
```

### 2. Error Logging with Request Context

```typescript
@Controller('users')
export class UsersController {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  @Post()
  async createUser(@Req() request: Request, @Body() body: CreateUserDto) {
    try {
      // Your code here
    } catch (error) {
      // Log with full context (stack trace + request details + auto-extracted fcid)
      this.errorLogger.logErrorWithContext(error, request, {
        context: 'UsersController.createUser',
        metadata: { action: 'user_creation' },
      });
      throw error;
    }
  }
}
```

### 3. Custom Error Logging with Options

```typescript
@Injectable()
export class GeolocationService {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  async processGeolocation(job: { fcid: string; ip: string }) {
    try {
      // Your code here
    } catch (error) {
      // Custom logging with specific options
      this.errorLogger.logError(error, undefined, {
        context: 'GeolocationService.processGeolocation',
        fcid: job.fcid,
        includeStack: true,
        includeRequest: false,
        metadata: {
          ip: job.ip,
          jobType: 'geolocation',
        },
      });
      throw error;
    }
  }
}
```

## Migration Examples

### Before (Current Pattern)

```typescript
// Old way in geolocation service
catch (error) {
  this.logger.error(
    `Failed to process geolocation for user ${job.fcid}: ${error.message}`,
    error.stack,
  );
  throw error;
}
```

### After (New ErrorLoggerService)

```typescript
// New way with ErrorLoggerService
catch (error) {
  this.errorLogger.logError(error, undefined, {
    context: 'GeolocationService.processGeolocation',
    fcid: job.fcid,
    includeStack: true,
    metadata: { ip: job.ip }
  });
  throw error;
}
```

## API Reference

### Core Method: `logError()`

```typescript
logError(error: Error | unknown, request?: Request, options: ErrorLogOptions = {}): void
```

**Parameters:**

- `error`: The error or exception to log
- `request`: Optional Express request object for context extraction
- `options`: Configuration options

**Options:**

- `includeStack?: boolean` - Include error stack trace (default: true)
- `includeRequest?: boolean` - Include request details (default: false)
- `context?: string` - Custom context for the log (default: 'ErrorLogger')
- `fcid?: string` - Custom fcid to use instead of extracting from request
- `metadata?: Record<string, any>` - Additional metadata to include
- `errorName?: string` - Custom error name/type to log (overrides default)

### Example: Passing a Custom Error Name

```typescript
this.errorLogger.logError(error, undefined, {
  context: 'AuthController.createJwt',
  fcid: payload.fcid,
  includeStack: true,
  errorName: 'JWTCreationError',
  metadata: { deviceId: payload.idfv || payload.adid },
});
```

### Convenience Methods

#### `logErrorWithContext()`

Logs errors with both stack trace and request details enabled:

```typescript
logErrorWithContext(error: Error | unknown, request: Request, options?: Omit<ErrorLogOptions, 'includeStack' | 'includeRequest'>): void
```

#### `logSimpleError()`

Logs errors with minimal information (no stack trace or request details):

```typescript
logSimpleError(error: Error | unknown, fcid?: string, options?: Omit<ErrorLogOptions, 'includeStack' | 'includeRequest'>): void
```

## Use Case Examples

### 1. Controller Error Handling

```typescript
@Controller('webhooks')
export class WebhooksController {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  @Post()
  async processWebhook(@Req() request: Request, @Body() body: WebhookDto) {
    try {
      // Process webhook
      return await this.webhookService.process(body);
    } catch (error) {
      // Log with full context for debugging
      this.errorLogger.logErrorWithContext(error, request, {
        context: 'WebhooksController.processWebhook',
        metadata: {
          webhookType: body.event_type,
          source: 'webhook_processing',
        },
      });

      // Re-throw for global exception filter
      throw error;
    }
  }
}
```

### 2. Service Layer Error Handling

```typescript
@Injectable()
export class UserService {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  async updateUser(fcid: string, updateData: UpdateUserDto) {
    try {
      // Update logic
      return await this.userRepository.update(fcid, updateData);
    } catch (error) {
      // Log without request details (service layer)
      this.errorLogger.logError(error, undefined, {
        context: 'UserService.updateUser',
        fcid,
        includeStack: true,
        includeRequest: false,
        metadata: {
          operation: 'user_update',
          hasEmailUpdate: !!updateData.email,
        },
      });
      throw error;
    }
  }
}
```

### 3. Queue Processor Error Handling

```typescript
@Processor('user-events')
export class UserEventProcessor {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  @Process('send-email')
  async processEmail(job: Job<EmailJobData>) {
    try {
      // Process email
      await this.emailService.send(job.data);
    } catch (error) {
      // Log with job-specific context
      this.errorLogger.logError(error, undefined, {
        context: 'UserEventProcessor.processEmail',
        fcid: job.data.fcid,
        includeStack: true,
        metadata: {
          jobId: job.id,
          emailType: job.data.type,
          retryCount: job.attemptsMade,
        },
      });
      throw error; // Let Bull handle retries
    }
  }
}
```

### 4. Custom Error Types

```typescript
class BusinessLogicError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'BusinessLogicError';
  }
}

@Injectable()
export class PaymentService {
  constructor(private readonly errorLogger: ErrorLoggerService) {}

  async processPayment(fcid: string, amount: number) {
    try {
      // Payment logic
    } catch (error) {
      if (error instanceof BusinessLogicError) {
        // Log business errors with less verbosity
        this.errorLogger.logSimpleError(error, fcid, {
          context: 'PaymentService.processPayment',
          metadata: {
            errorCode: error.code,
            amount,
            errorType: 'business_logic',
          },
        });
      } else {
        // Log technical errors with full stack
        this.errorLogger.logError(error, undefined, {
          context: 'PaymentService.processPayment',
          fcid,
          includeStack: true,
          metadata: { amount, errorType: 'technical' },
        });
      }
      throw error;
    }
  }
}
```

## Best Practices

### 1. Context Naming

Use descriptive contexts that include the class and method:

```typescript
context: 'ServiceName.methodName';
```

### 2. Metadata Usage

Include relevant business context in metadata:

```typescript
metadata: {
  operation: 'user_creation',
  userType: 'premium',
  feature: 'geolocation'
}
```

### 3. Error Re-throwing

Always re-throw errors after logging them unless you're handling them:

```typescript
catch (error) {
  this.errorLogger.logError(error, request, options);
  throw error; // Let upstream handle the error
}
```

### 4. Performance Considerations

- Use `includeRequest: false` in service layers where request context isn't needed
- Use `logSimpleError()` for high-frequency operations where stack traces aren't critical
- Avoid logging the same error multiple times in the call stack

### 5. Sensitive Data

The service automatically masks common sensitive fields, but be mindful of:

- Custom sensitive fields in metadata
- Business-specific sensitive information
- PII data in error messages

## Log Output Format

The service produces structured logs like this:

```json
{
  "message": "Database connection failed",
  "context": "UserService.createUser",
  "fcid": "user-123-abc",
  "stacktrace": "Error: Database connection failed\n    at UserService.createUser...",
  "request": {
    "method": "POST",
    "url": "/users",
    "headers": { "authorization": "[MASKED]" },
    "query": {},
    "body": { "fcid": "user-123-abc", "email": "<EMAIL>" }
  },
  "exceptionType": "DatabaseError",
  "metadata": {
    "operation": "user_creation",
    "retry_count": 0
  }
}
```

## Migration Checklist

When migrating from existing error logging:

1. **Replace direct logger calls**: Change `this.logger.error()` to `this.errorLogger.logError()`
2. **Remove manual fcid concatenation**: The service extracts it automatically
3. **Remove manual stack trace handling**: Use the `includeStack` option
4. **Add appropriate context**: Use descriptive context strings
5. **Include relevant metadata**: Add business context to metadata
6. **Use `errorName` for custom error types if needed**
7. **Update error handling tests**: Update test assertions for the new log format
