# Running Roshi in different container stages

## Prerequisites:

- A docker-compose instance of all services (base: `postgres`, `redis` & `redis-commander`, check _./docker-compose.yml_) and shared volumes.

## Current build and run status:

> NOTE: some variables may be in your .env file, will be [inside marks] here.

### Build and Run

To build the images and start all services, run:

`docker compose up --build -d`

This command will use the `.env` file in the root directory for environment variables.

### Stop services

To stop and remove the containers, run:

`docker compose down`

### View logs

To view the logs for a specific service (e.g., the `app` service), run:

`docker compose logs -f app`

## Running outside docker-compose

`docker build --build-arg NODE_ENV=local --^Cild-arg GH_AUTH_PACKAGE_TOKEN=**************************************** -t app .`

`docker run --network=roshi_default --env-file ./.env.test -p 3000:3000 -t app`
`docker stop $(docker ps -f "ancestor=app" -q)`
