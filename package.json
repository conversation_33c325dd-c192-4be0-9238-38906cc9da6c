{"name": "roshi", "version": "2.0.0", "description": "User identity management service", "sideEffects": false, "module": "dist/index.js", "scripts": {"build": "nest build", "start:dev": "TZ=UTC nest start --watch", "start": "TZ=UTC node /var/app/current/dist/main.js", "deploy": "npm ci && npm run build && npm run start", "docker:up": "docker compose up -d", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "prepare": "if [ \"$NODE_ENV\" != \"production\" ]; then husky install; fi", "test": "jest", "test:verbose": "VERBOSE=1 jest", "analyze": "ANALYZE=true webpack", "migration:create": "typeorm migration:create ./src/migrations", "migration:generate": "typeorm-ts-node-commonjs migration:generate ./src/migrations/auto -d ./src/config/typeorm.config.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d ./src/config/typeorm.config.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d ./src/config/typeorm.config.ts", "migration:reset": "typeorm-ts-node-commonjs schema:drop -d ./src/config/typeorm.config.ts && typeorm-ts-node-commonjs migration:run -d ./src/config/typeorm.config.ts", "seed": "ts-node -r tsconfig-paths/register src/seeders/seed.ts", "test:webhooks": "ts-node scripts/test-webhook-batch.ts", "test:pending": "ts-node scripts/test-pending-user-lock.ts", "test:merged-user-perf": "ts-node scripts/test-merged-user-performance.ts", "test:materialized-view": "jest test/materialized-view-refresh.service.spec.ts", "optimize-db": "ts-node -r tsconfig-paths/register scripts/optimize-database.ts", "stress-test": "ts-node -r tsconfig-paths/register scripts/stress-test-performance.ts", "perf-test": "./scripts/run-performance-tests.sh", "perf-test:quick": "./scripts/run-performance-tests.sh quick", "perf-test:comprehensive": "./scripts/run-performance-tests.sh comprehensive", "compare-performance": "ts-node -r tsconfig-paths/register scripts/compare-performance.ts"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.0.0", "@aws-sdk/client-secrets-manager": "^3.744.0", "@aws-sdk/lib-dynamodb": "^3.0.0", "@faker-js/faker": "^9.5.0", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@opentelemetry/api": "^1.6.0", "@opentelemetry/auto-instrumentations-node": "^0.39.4", "@opentelemetry/exporter-logs-otlp-http": "^0.45.1", "@opentelemetry/exporter-trace-otlp-http": "^0.45.1", "@opentelemetry/resources": "^1.6.0", "@opentelemetry/sdk-logs": "^0.45.1", "@opentelemetry/sdk-node": "^0.45.1", "@opentelemetry/tracing": "^0.24.0", "@opentelemetry/winston-transport": "^0.13.0", "@types/passport": "^1.0.17", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bull": "^4.16.5", "cache-manager": "^6.4.0", "chalk": "^4.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dotenv": "^16.5.0", "express": "^4.21.2", "firebase-admin": "^13.2.0", "in-app-purchase": "^1.11.4", "ioredis": "^5.6.1", "jest": "^29.7.0", "joi": "^17.13.3", "jsonschema": "^1.5.0", "node-cron": "^3.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.2", "prom-client": "^15.1.3", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "uuid": "^9.0.1", "winston": "^3.17.0"}, "devDependencies": {"@golevelup/ts-jest": "^0.6.2", "@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.6", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/in-app-purchase": "^1.11.4", "@types/jest": "^29.5.14", "@types/node": "^20.11.24", "@types/node-cron": "^3.0.11", "@types/passport-jwt": "^4.0.1", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.0", "lint-staged": "^15.4.1", "prettier": "^2.8.8", "terser-webpack-plugin": "^5.3.11", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.1", "typescript": "^5.7.3", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}