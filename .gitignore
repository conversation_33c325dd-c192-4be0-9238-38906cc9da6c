# Environment variables
.env
.env.local

# Dependencies
/node_modules

# Build output
/dist
/.build
/.serverless

# IDE
/.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Tests
/coverage
/.nyc_output
jest_results.json

# TypeScript
*.tsbuildinfo
dist/*.js
dist/*.js.map

# OS
.DS_Store

# DynamoDB Local files
docker/dynamodb/

# Perf artifacts
/db-performance/*
/.cursor
