### Create TOKEN from Anonymous User (no fcid) with iOS Device ID
# @name createJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{$guid}}"
}

### Use created JWT to create a user and retrieve it
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### Create TOKEN again with all the user data
# @name createJwtAgain
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "idfv": "{{createJwt.request.body.idfv}}"
}

### Retrieve the user again
POST {{baseUrl}}/users
Authorization: Bearer {{createJwtAgain.response.body.data.accessToken}}

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  }
}

### Error: malformed idfv
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "non-valid-idfv"
}
