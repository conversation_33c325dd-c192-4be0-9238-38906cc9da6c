### Test JWT Creation with New Pending User Library
### This file tests the new pending user management system

### Test 1: Create JWT with iOS device ID only (Flow 2.a)
# @name createJwtIOS
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{$guid}}"
}

### Test 2: Create JWT with Android device ID only (Flow 2.a)
# @name createJwtAndroid
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "1234567890abcdef"
}

### Test 3: Create JWT with device ID + FCID (Flow 2.b - should fall back to 2.a if FCID doesn't exist)
# @name createJwtWithFcid
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{$guid}}",
  "fcid": "non-existent-fcid-12345"
}

### Test 4: Create JWT again with same device ID (should return same FCID due to deterministic generation)
# @name createJwtSameDevice
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "12345678-1234-1234-1234-123456789012"
}

### Test 4b: Create JWT with same device ID again (verify FCID consistency)
# @name createJwtSameDeviceAgain
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "12345678-1234-1234-1234-123456789012"
}

### Test 5: Invalid request - missing device ID
# @name createJwtInvalid
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "fcid": "some-fcid"
}

### Test 6: Invalid request - invalid IDFV format
# @name createJwtInvalidIdfv
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "invalid-uuid-format"
}

### Test 7: Create user with JWT from new system
# @name createUserWithNewJwt
POST {{baseUrl}}/users
Authorization: Bearer {{createJwtSameDevice.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["12345678-1234-1234-1234-123456789012"]
  },
  "installed_at": 1738772200000
}

### Test 8: Verify user was created correctly
# @name getUserWithNewJwt
GET {{baseUrl}}/users?identifiers.idfv={{createUserWithNewJwt.request.body.identifiers.[0]}}
Authorization: Bearer {{createJwtSameDevice.response.body.data.accessToken}}
