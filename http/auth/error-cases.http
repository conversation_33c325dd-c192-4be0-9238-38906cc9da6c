### Trying to create TOKEN with idfa
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfa": "test-jwt-idfa"
}

### Trying to create TOKEN with gaid
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "gaid": "test-jwt-gaid"
}

### Trying to create TOKEN with idfa
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfa": "test-jwt-idfa",
  "idfv": "test-jwt-idfv"
}

### Trying to create TOKEN with gaid
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "e8383625e4d2dd7c",
  "gaid": "test-jwt-gaid"
}


### Trying to create TOKEN with empty payload
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{}

### Error: Step 1 - Create new token with valid adid and fake fcid
# @name createFakeJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "fcid": "fake-fcid",
  "adid": "test-fake-fcid"
}

### Error: Step 2 - use JWT token with fake fcid
POST {{baseUrl}}/users
Authorization: Bearer {{createFakeJwt.response.body.data.accessToken}}

{
  "fcid": "fake-fcid",
  "identifiers": {
    "adid": ["test-fake-fcid"]
  }
}