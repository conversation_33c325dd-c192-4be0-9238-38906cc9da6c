### Create TOKEN from Anonymous User (no fcid) with Android Device ID
# @name createJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "e8383625e4d2dd7c"
}

### Use created JWT to create a user and retrieve it
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "adid": ["e8383625e4d2dd7c"]
  },
  "installed_at": 1738772200000
}

### Create TOKEN again with all the user data
# @name createJwtAgain
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "adid": "e8383625e4d2dd7c"
}

### Retrieve the user again
POST {{baseUrl}}/users
Authorization: Bearer {{createJwtAgain.response.body.data.accessToken}}

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "identifiers": {
    "adid": ["e8383625e4d2dd7c"]
  }
}

### Error: malformed adid
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "non-valid-adid"
}
