#!/usr/bin/env bash
cd "$(dirname $0)"

# Read settings and environment variables
source ../.env
SETTINGS=$(cat ../.vscode/settings.json)
BASEURL="${ROSHI_URL}"

UUID="$(uuidgen)"
# Create request body with device identifiers for anonymous user
REQUEST_BODY=$(cat <<EOF
{
  "idfv": "${UUID}"
}
EOF
)

echo "Making JWT token creation request to $BASEURL/auth/createJwt"
echo "Using API key: $API_AUTH_TOKEN"
echo REQUEST_BODY: $REQUEST_BODY

# Make the request with proper headers and error handling
RESULT=$(curl --request POST \
  --url "$BASEURL/auth/createJwt" \
  --header 'Accept: application/json' \
  --header 'Content-Type: application/json' \
  --header "User-Agent: Flipaclip" \
  --header "x-api-key: $API_AUTH_TOKEN" \
  --data "$REQUEST_BODY" \
  --silent \
  --show-error \
  --fail)

# Check curl exit status
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo "Error: Failed to make the request (Exit code: $EXIT_CODE)"
    echo "Response: $RESULT"
    exit 1
fi

# Extract tokens and expiration
JWT=$(echo "$RESULT" | jq -r '.data.accessToken')
REFRESH_TOKEN=$(echo "$RESULT" | jq -r '.data.refreshToken')
EXPIRY=$(echo "$RESULT" | jq -r '.data.accessTokenExpiry')

if [ -z "$JWT" ] || [ "$JWT" = "null" ]; then
    echo "Error: Failed to extract JWT token from response"
    echo "Response: $RESULT"
    exit 1
fi

echo "Successfully created JWT token"
echo "Token expires at: $EXPIRY"

# Update settings.json with the new token
echo "$SETTINGS" | jq --arg auth "Bearer $JWT" '."rest-client.defaultHeaders" += {"Authorization": $auth}' > ../.vscode/settings.json
echo "Operation completed."

echo "Warning: removing .vscode/settings.json from git tracking, to add it back, run: git update-index --no-skip-worktree .vscode/settings.json"
git update-index --skip-worktree ../.vscode/settings.json