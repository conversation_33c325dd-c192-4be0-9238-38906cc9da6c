### Android requests creating anonymous user
# @name create
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["ad1d000000000002"]
  },
  "installed_at": 1738772299999
}

### Linking its fcid to an existing fcaid
# @name merge
POST {{baseUrl}}/users

{
  "fcaid": "AA",
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000002"]
  }
}

### Get merged user details TODO: should also bring merge-to prop
GET {{baseUrl}}/users?fcid={{create.response.body.data.fcid}}

### Trying to perform an operation (get or update) on a previously merged user fcid should fail
POST {{baseUrl}}/users

{
  "fcid": "{{create.response.body.data.fcid}}",
  "fcaid": "AA",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000002"]
  }
}

### Post request with only the device IDs of a previously merged user, should create a new anonymous user
# @name create_anon
POST {{baseUrl}}/users

{
	"identifiers":{
		"gaid" : ["{{create.request.body.identifiers.gaid[0]}}"],
		"adid" : ["ad1d000000000002"]
	},
	"installed_at" : 1738772255555
}

### Log in the new user
POST {{baseUrl}}/users

{
  "fcid": "{{create_anon.response.body.data.fcid}}",
  "fcaid": "AA",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000002"]
  }
}

### Create new registered user
# @name create_reg
POST {{baseUrl}}/users

{
  "fcid": "{{merge.response.body.data.fcid}}",
  "fcaid": "BB",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000002"]
  },
  "installed_at": 1738772200000
}

### Log that registered user to known FCAID
POST {{baseUrl}}/users

{
  "fcid": "{{create_reg.response.body.data.fcid}}",
  "fcaid": "AA",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000002"]
  }
}
