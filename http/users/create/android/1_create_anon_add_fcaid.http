### Android requests creating anonymous user
# @name create
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["ad1d000000000127"]
  },
  "isFreshInstall": true,
  "installed_at": 1738772200000
}

### Linking its fcid to a never seen fcaid
POST {{baseUrl}}/users

{
  "fcaid": "AA",
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000001"]
  }
}

### Android request failing because trying to get a registered user without providing fcaid
POST {{baseUrl}}/users

{
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000001"]
  }
}

