### Create TOKEN from Anonymous User (no fcid) with adid
# @name createJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "ad1d001234567890"
}

### Try to create user with empty gaid
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "gaid": ["00000-0000000"],
    "adid": ["ad1d001234567890"]
  },
  "installed_at": 1738772200000
}