### Create new anonymous Android user
# @name create
@adid = ad{{$randomInt 111111111111 ************}}
@fixex_gaid = {{$guid}}
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["{{adid}}"]
  },
  "isFreshInstall": true,
  "installed_at": 1738772200000
}

### Should merge the anonymous user into the registered user using the fcaid
# @name merge
POST {{baseUrl}}/users

{
  "fcaid": "TEST_{{$randomInt 1111 9999}}",
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "gaid": ["{{fixex_gaid}}"],
    "adid": ["{{adid}}"]
  }
}

### Should update users using new gaid and same adid
# @name merge_again
POST {{baseUrl}}/users

{
  "fcaid": "{{merge.response.body.data.fcaid}}",
  "fcid":  "{{merge.response.body.data.fcid}}",
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["{{adid}}"]
  }
}

### Should return the same user because it's already merged
POST {{baseUrl}}/users

{
  "fcid": "{{merge_again.response.body.data.fcid}}",
  "fcaid": "{{merge_again.response.body.data.fcaid}}",
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["{{adid}}"]
  }
}


### Should fail because fcaid is not provided
POST {{baseUrl}}/users

{
  "fcid": "{{merge_again.response.body.data.fcid}}",
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["{{adid}}"]
  }
}
