### Android requests creating anonymous user with non-existent fcid
POST {{baseUrl}}/users

{
  "fcid": "{{$guid}}-33",
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["ad1d000000000333"]
  },
  "isFreshInstall": true,
  "installed_at": 1738772200000
}

### Android requests creating anonymous user with non-existent fcid and fcaid
POST {{baseUrl}}/users

{
  "fcid": "{{$guid}}-44",
  "fcaid": "JJ",
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["ad1d000000000444"]
  },
  "isFreshInstall": true,
  "installed_at": 1738772200000
}