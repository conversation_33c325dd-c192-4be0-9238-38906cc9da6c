### Trying to create iOS user without installed_at (idfv and idfa)
POST {{baseUrl}}/users

{
  "identifiers": {
    "idfv": ["{{$guid}}"],
    "idfa": ["{{$guid}}"]
  }
}

### Trying to create iOS user without installed_at (idfv)

POST {{baseUrl}}/users

{
  "identifiers": {
    "idfv": ["{{$guid}}"]
  }
}

### Trying to create iOS user without installed_at (adid)

POST {{baseUrl}}/users

{
  "identifiers": {
    "adid": ["ad1d001234567890"]
  }
}


### Trying to create iOS user without installed_at (gaid)

POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{$guid}}"]
  }
}