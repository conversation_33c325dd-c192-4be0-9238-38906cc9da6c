### iOS Request without idfa but with idfv
POST {{baseUrl}}/users
Authorization: <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>OiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGZ2IjoiZTQxNWZiZGMtOTJhZC00M2YyLWFkZDMtMGM2MTJiMTllMmU1IiwiaWF0IjoxNzQ3MTYwMjE2LCJleHAiOjE3NDcxNjExMTZ9.GCbcm18NekY5Qim30ySJDNJLwGpxhxdtJAXAW14iI-I

{
  "identifiers": {
    "idfv": ["{{$guid}}"]
  },
  "installed_at": 1738772200000
}

### Create anonymous user
# @name create
POST {{baseUrl}}/users

{
  "identifiers": {
    "idfv": ["{{$guid}}"]
  },
  "installed_at": 1738772200000
}

### Link with fcid new fcaid
POST {{baseUrl}}/users

{
  "fcaid": "CC",
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "idfv": ["{{create.request.body.identifiers.idfv[0]}}"]
  },
  "installed_at": 1738772200000
}

### Retrieve user without knowing fcid
POST {{baseUrl}}/users

{
  "fcaid": "CC",
  "identifiers": {
    "idfv": ["{{create.request.body.identifiers.idfv[0]}}"]
  },
  "type": "REGISTERED",
  "installed_at": 1738772200000
}