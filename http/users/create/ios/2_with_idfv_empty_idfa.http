### iOS Request with idfv and opt-out value for idfa
POST {{baseUrl}}/users

{
  "identifiers": {
    "idfv": ["{{$guid}}"],
    "idfa": ["00000-0000-00000"]
  },
  "installed_at": 1738772200000
}

### UUID opt-out variants
POST {{baseUrl}}/users

{
  "identifiers": {
    "idfv": ["{{$guid}}"],
    "idfa": ["00000000-0000-0000-0000-000000000000"]
  },
  "installed_at": 1738772200000
}


### empty zeros opt-out variants
POST {{baseUrl}}/users

{
  "identifiers": {
    "idfv": ["{{$guid}}"],
    "idfa": ["0000000000000000"]
  },
  "installed_at": 1738772200000
}
