### /users without FCID must include device_id from token in identifiers
# @name createJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}

{
  "adid": "ad1d000000000007"
}

### Create anon user without advertising ID
# @name create
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "adid": ["ad1d000000000007"],
    "gaid": ["0000000-000000-000000"]
  },
  "installed_at": 1738772200000
}

### Use match directive
# @name use_used_adid
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "adid": ["ad1d000000000008"],
    "gaid": ["0000000-000000-000000"],
    "match" : {
			"adid" : "ad1d000000000007"
		}
  },
  "installed_at": 1738772200000
}

### Should fail when colling with existing vendor ID 
# @name error
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "fcid": "{{create.response.body.data.fcid}}",
  "identifiers": {
    "adid": ["ad1d000000000003"],
    "gaid": ["0000000-000000-000000"],
    "match" : {
			"adid" : "ad1d000000000008"
		}
  },
  "installed_at": 1738772200000
}

