### Create TOKEN from Anonymous User (no fcid) with Android Device ID
# @name createJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "f123456f12345678"
}

### Use created JWT to create a user and retrieve it
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "adid": ["f123456f12345678"]
  },
  "installed_at": 1738772200000
}

### Set moengageMigrated to true
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "identifiers": {
    "adid": ["f123456f12345678"]
  },
  "properties": {
    "moengageMigrated": true
  }
}

### Set moengageMigrated to false
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "identifiers": {
    "adid": ["f123456f12345678"]
  },
  "properties": {
    "moengageMigrated": false
  }
}
