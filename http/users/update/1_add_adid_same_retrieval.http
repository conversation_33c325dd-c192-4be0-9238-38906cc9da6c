# Create new anon user
# @name create
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{$guid}}"],
    "adid": ["ad1d000000000003"]
  },
  "installed_at": 1738772200000
}

### Update User with new adid
# @name update_adid
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000004"]
  },
  "installed_at": 1738772288888
}

### Update User with new adid
# @name retrieve_with_old_adid
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000003"]
  },
  "installed_at": 1738772277777
}

### Update User with new adid
# @name retrieve_with_new_adid
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000004"]
  },
  "installed_at": 1738772288888
}

### Update User with new adid
# @name add_new_adid
POST {{baseUrl}}/users

{
  "fcid": "{{create.response.body.fcid}}",
  "identifiers": {
    "gaid": ["{{create.request.body.identifiers.gaid[0]}}"],
    "adid": ["ad1d000000000005"]
  },
  "installed_at": 1738772288888
}
