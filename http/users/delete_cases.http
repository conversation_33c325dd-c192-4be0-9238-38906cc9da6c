### Delete User by FCID
DELETE {{baseUrl}}/users

{
  "fcid": "non-existing-fcid",
  "token": "{{$processEnv SLACK_TOKEN}}"
}

### Expected Response for Successful Deletion
# {
#   "message": "User successfully deleted"
# }

### Error Responses - User Not Found
DELETE {{baseUrl}}/users

{
  "fcid": "non-existing-fcid",
  "token": "{{$processEnv SLACK_TOKEN}}"
}

### User Not Found
# {
#   "statusCode": 404,
#   "message": "User not found"
# }

### Error Responses - Missing FCID
DELETE {{baseUrl}}/users

{
  "fcaid": "existing-fcaid",
  "token": "{{$processEnv SLACK_TOKEN}}"
}

### Invalid Request Response
# {
#   "statusCode": 400,
#   "message": "A valid roshi identifier (fcid) must be provided"
# }

### Error Responses - Missing Token
DELETE {{baseUrl}}/users

{
  "fcid": "existing-fcid"
}

### Unauthorized Response (Missing Token)
# {
#   "statusCode": 401,
#   "message": "Delete requests require a token field for validation"
# }

### Error Responses - Empty Token
DELETE {{baseUrl}}/users

{
  "fcid": "existing-fcid",
  "token": ""
}

### Unauthorized Response (Empty Token)
# {
#   "statusCode": 401,
#   "message": "Token field cannot be empty"
# }

### Error Responses - No JWT Token
DELETE {{baseUrl}}/users
Authorization: Bearer invalid-token

{
  "fcid": "existing-fcid",
  "token": "{{$processEnv SLACK_TOKEN}}"
}

### Invalid Request
# {
#   "statusCode": 400,
#   "message": "A valid roshi identifier (fcid) must be provided"
# }

### Unauthorized
# {
#   "statusCode": 401,
#   "message": "Unauthorized"
# }
