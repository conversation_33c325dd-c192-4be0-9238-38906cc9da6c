### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token, should create a new user
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

###/users without FCID and missing token device_id in identifiers fails
### should fail because we first use and idfv to get the token, then try to use an adid to create a user
# @name createJwt2
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "f15e2c5e-bef4-4ace-acac-f0fe1b55dd50"
}


### Create user with idfv from token
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt2.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt2.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### ADID already known binds new IDFV to same user if FCID is not provided
# @name createJwt3
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt3.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt3.request.body.idfv}}"],
    "idfa": ["{{idfa}}"]
  },
  "installed_at": 1738772200000
}

### With FCID and new IDFV but no IDFA, creates new anonymous user
# @name createJwt4
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt4.response.body.data.accessToken}}

{
  "fcid": "{{createJwt4.response.body.data.fcid}}",
  "identifiers": {
    "idfv": ["{{createJwt4.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### With FCID and valid FCAID, FCAID takes precedence
# @name createJwt5
@idfv= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "22262243-d484-4ead-be58-d1bacd1e2bce"
}

### Create user with idfv from token
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt5.response.body.data.accessToken}}

{
  "fcid": "{{createJwt5.response.body.data.fcid}}",
  "fcaid": "TEST_{{$randomInt 111 999}}",
  "identifiers": {
    "idfv": ["{{createJwt5.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### With FCAID and new device ID, device is added to user bound to FCAID
# @name createJwt6
@idfv= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "33362243-d484-4ead-be58-d1bacd1e2bce"
}

### Create user with idfv from token
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt6.response.body.data.accessToken}}

{
  "fcid": "{{createJwt6.response.body.data.fcid}}",
  "fcaid": "TEST_{{$randomInt 111 999}}",
  "identifiers": {
    "idfv": ["{{createJwt6.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### With FCID, new IDFV, and match statement with old IDFV, returns matched user
# @name createJwt7
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token
# @name createUser1
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt7.response.body.data.accessToken}}

{
  "fcid": "{{createJwt7.response.body.data.fcid}}",
  "identifiers": {
    "idfv": ["{{createJwt7.request.body.idfv}}"],
    "idfa": ["{{idfa}}"]
  },
  "installed_at": 1738772200000
}

### Create the user again using the old IDFV and new IDFA, should return the same user
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt7.response.body.data.accessToken}}

{
  "fcid": "{{createJwt7.response.body.data.fcid}}",
  "identifiers": {
    "idfv": ["{{createUser1.request.body.identifiers.idfv[0]}}"],
    "idfa": ["{{idfa}}"]
  },
  "installed_at": 1738772200000
}