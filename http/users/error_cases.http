### Error Cases for User Management API

### 1. Invalid Request - Missing Identifiers
POST {{baseUrl}}/users

{
  "properties": {
    "totalAdRevenue": 10.99
  },
  "installed_at": 1739361032336
}

### Expected Response: 400 Bad Request
# {
#     "statusCode": 400,
#     "message": "At least one valid identifier must be provided (fcid, fcaid, or device identifiers)"
# }

### 2. Unauthorized Request
POST {{baseUrl}}/users

{
  "identifiers": {
    "adid": ["new-adid"]
  }
}

### Expected Response: 401 Unauthorized
# {
#     "statusCode": 401,
#     "message": "Unauthorized"
# }

### 3. User Not Found
GET {{baseUrl}}/users?fcaid=non-existent-fcaid

### Expected Response: 404 Not Found
# {
#     "statusCode": 404,
#     "message": "User not found"
# }

### 4. Ignore empty strings in identifiers
POST {{baseUrl}}/users

{
  "identifiers": {
    "gaid": [""],
    "adid": ["new-adid", ""],
    "idfv": ["", "idfv_2"],
    "idfa": [""]
  },
  "installed_at": 1738772200000
}