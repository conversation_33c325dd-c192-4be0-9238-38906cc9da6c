### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}
### Create user with idfv from token, should create a new user
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### Terms Accepted Webhook
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "provider": "FlipaClip",
  "event_name": "age_selected",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1998972200042
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "google_play",
  "session_id": 1738772200000,
  "payload": {
    "userDeclaredAge": 5
  }
}

### Terms Accepted Webhook with optional age selector ID parameter 
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "provider": "FlipaClip",
  "event_name": "age_selected",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1998972200042
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "google_play",
  "session_id": 1738772200000,
  "payload": {
    "ageSelectorId": "age_selector_12345",
    "userDeclaredAge": 12
  }
}

### Terms Accepted Webhook with optional age selector ID parameter 
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "provider": "FlipaClip",
  "event_name": "age_selected",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1998972200042
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "google_play",
  "session_id": 1738772200000,
  "payload": {
    "ageSelectorId": "age_selector_12345",
    "userDeclaredAge": 21
  }
}