### Valid Generic Webhook Event
# Demonstrates a properly formatted webhook event with all required fields
POST {{baseUrl}}/webhooks
X-FC-SEC: {{$dotenv X_FC_SEC_SECRET}}

{
  "event_name": "user_interaction",
  "event_control": {
    "device_id": "test-idfv-1",
    "timestamp": 1738772200000
  },
  "fcid": "4e53de10-6229-44cd-ad83-d3a7c073ee8f-58",
  "provider": "generic_service",
  "payload": {
    "action": "button_click",
    "page": "homepage",
    "button_id": "cta-main",
    "session_id": "sess_abc123"
  }
}

### Invalid Generic Webhook Event - Missing Required Field
# Demonstrates error response when required field (event_name) is missing
POST {{baseUrl}}/webhooks
X-FC-SEC: {{$dotenv X_FC_SEC_SECRET}}

{
  "event_control": {
    "device_id": "test-idfv-1",
    "timestamp": 1738772200000
  },
  "provider": "generic_service",
  "fcid": "0bac3ba4-90cc-4fe0-bbb5-baffcd70b2eb",
  "payload": {
    "action": "form_submit",
    "form_id": "contact-form"
  }
}

### Invalid Generic Webhook Event - event_control is missing
POST {{baseUrl}}/webhooks
X-FC-SEC: {{$dotenv X_FC_SEC_SECRET}}

{
  "event_name": "user_interaction",
  "fcid": "4e53de10-6229-44cd-ad83-d3a7c073ee8f-58",
  "provider": "generic_service",
  "payload": {
    "action": "button_click",
    "page": "homepage",
    "button_id": "cta-main",
    "session_id": "sess_abc123"
  }
}

### Invalid Generic Webhook Event - Missing Authentication
# Demonstrates error response when X-FC-SEC header is missing
POST {{baseUrl}}/webhooks

{
  "event_name": "user_interaction",
  "event_control": {
    "device_id": "test-idfv-1",
    "timestamp": 1738772200000
  },
  "fcid": "4e53de10-6229-44cd-ad83-d3a7c073ee8f-58",
  "provider": "generic_service",
  "payload": {
    "action": "button_click",
    "page": "homepage",
    "button_id": "cta-main",
    "session_id": "sess_abc123"
  }
}
