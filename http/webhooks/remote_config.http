### Variables
@baseUrl = http://localhost:3000
@apiKey = {{$dotenv API_AUTH_TOKEN}}

### Get a remote config value
# Get a specific remote config value with an optional default value
GET {{baseUrl}}/webhooks/remote-config/test_feature
Content-Type: application/json
x-api-key: {{apiKey}}

### Get a remote config value with default value
# If the key doesn't exist, it will return the provided default value
GET {{baseUrl}}/webhooks/remote-config/non_existent_key?defaultValue={"enabled":false}
Content-Type: application/json
x-api-key: {{apiKey}}

### Update a remote config value
# Set or update a remote config parameter
POST {{baseUrl}}/webhooks/remote-config/update
Content-Type: application/json
x-api-key: {{apiKey}}

{
    "key": "test_feature",
    "value": {
        "enabled": true,
        "message": "Test feature is enabled",
        "settings": {
            "maxRetries": 3,
            "timeout": 5000
        }
    },
    "description": "Test feature flag with settings"
}

### Update another remote config value
# Example of updating a different parameter
POST {{baseUrl}}/webhooks/remote-config/update
Content-Type: application/json
x-api-key: {{apiKey}}

{
    "key": "app_settings",
    "value": {
        "version": "1.0.0",
        "maintenance": false,
        "allowedCountries": ["US", "CA", "UK"]
    },
    "description": "Global app settings"
}

### Sync all remote config values
# This will fetch all remote config values and update the cache
POST {{baseUrl}}/webhooks/remote-config/sync
Content-Type: application/json
x-api-key: {{apiKey}}

### Invalidate cache for a specific key
# Remove a specific key from the cache
POST {{baseUrl}}/webhooks/remote-config/invalidate-cache
Content-Type: application/json
x-api-key: {{apiKey}}

{
    "key": "test_feature"
}

### Invalidate all cache
# Remove all remote config values from the cache
POST {{baseUrl}}/webhooks/remote-config/invalidate-cache
Content-Type: application/json
x-api-key: {{apiKey}}

{}
