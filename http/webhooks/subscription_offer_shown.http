### Paywall Webhook Test Request
POST {{baseUrl}}/webhooks
Content-Type: application/json

{
  "provider": "FlipaClip",
  "event_name": "subscription_offer_shown",
  "event_control": {
    "device_id": "test-idfv-1",
    "timestamp": 1738772200000
  },
  "fcid": "4e53de10-6229-44cd-ad83-d3a7c073ee8f-58",
  "store": "google_play",
  "session_id": 1738772200000,
  "payload": {
    "paywall_id": "default_light_qa",
    "placement_id": "home_subscription_button",
    "trigger_action": "app_open",
    "plans": [
      {
        "id": "flipaclip_599_1m_7d0",
        "offers_free_trial": true,
        "period": "MONTH"
      },
      {
        "id": "flipaclip_2999_1y_7d0",
        "offers_free_trial": true,
        "period": "YEAR"
      }
    ],
    "ab_test_id": "test_001",
    "ab_test_variant": "variant_A"
  }
}
