### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token, should create a new user
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### MoEngage Webhook - Single Event (Notification Received)
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "app_name": "FlipaClip",
  "source": "MOENGAGE",
  "moe_request_id": "moengage-unique-request-id-123",
  "events": [
    {
      "event_name": "Notification Received Android",
      "event_code": "NOTIFICATION_RECEIVED_M<PERSON>",
      "event_uuid": "moengage-unique-event-id-456",
      "event_time": 1580967474,
      "event_type": "CAMPAIGN_EVENT",
      "event_source": "MOENGAGE",
      "push_id": "recipient-device-push-token",
      "uid": "{{createUser.response.body.data.fcid}}",
      "event_attributes": {
        "campaign_id": "353df897hkbh67658",
        "campaign_name": "Welcome Back Campaign",
        "campaign_type": "Event Trigger",
        "campaign_channel": "Push"
      },
      "user_attributes": {
        "moengage_user_id": "moe_internal_user_id_123",
        "user_attr_1": "user_attr_val1",
        "user_attr_2": "user_attr_val2"
      },
      "device_attributes": {
        "moengage_device_id": "moe_internal_device_id_456",
        "device_attr_1": "device_attr_val1",
        "device_attr_2": "device_attr_val2"
      }
    }
  ]
}

### MoEngage Webhook - Single Event (Email Sent)
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "app_name": "FlipaClip",
  "source": "MOENGAGE",
  "moe_request_id": "moengage-unique-request-id-789",
  "events": [
    {
      "event_name": "Email Sent",
      "event_code": "MOE_EMAIL_SENT",
      "event_uuid": "moengage-unique-event-id-101",
      "event_time": 1580967475,
      "event_type": "CAMPAIGN_EVENT",
      "event_source": "MOENGAGE",
      "email_id": "<EMAIL>",
      "uid": "{{createUser.response.body.data.fcid}}",
      "event_attributes": {
        "campaign_id": "353df897hkbh67659",
        "campaign_name": "Weekly Newsletter",
        "campaign_type": "General",
        "campaign_channel": "Email"
      },
      "user_attributes": {
        "moengage_user_id": "moe_internal_user_id_123",
        "user_attr_1": "user_attr_val1",
        "user_attr_2": "user_attr_val2"
      },
      "device_attributes": {
        "moengage_device_id": "moe_internal_device_id_456",
        "device_attr_1": "device_attr_val1",
        "device_attr_2": "device_attr_val2"
      }
    }
  ]
}

### MoEngage Webhook - Single Event (SMS Delivered)
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "app_name": "FlipaClip",
  "source": "MOENGAGE",
  "moe_request_id": "moengage-unique-request-id-202",
  "events": [
    {
      "event_name": "SMS Delivered",
      "event_code": "SMS_DELIVERED",
      "event_uuid": "moengage-unique-event-id-303",
      "event_time": 1580967476,
      "event_type": "CAMPAIGN_EVENT",
      "event_source": "MOENGAGE",
      "mobile_number": "+1234567890",
      "uid": "{{createUser.response.body.data.fcid}}",
      "event_attributes": {
        "campaign_id": "353df897hkbh67660",
        "campaign_name": "SMS Verification",
        "campaign_type": "Event Trigger",
        "campaign_channel": "SMS"
      },
      "user_attributes": {
        "moengage_user_id": "moe_internal_user_id_123",
        "user_attr_1": "user_attr_val1",
        "user_attr_2": "user_attr_val2"
      },
      "device_attributes": {
        "moengage_device_id": "moe_internal_device_id_456",
        "device_attr_1": "device_attr_val1",
        "device_attr_2": "device_attr_val2"
      }
    }
  ]
}

### MoEngage Webhook - Multiple Events (Batch-like structure for testing)
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "app_name": "FlipaClip",
  "source": "MOENGAGE",
  "moe_request_id": "moengage-unique-request-id-batch",
  "events": [
    {
      "event_name": "Notification Received Android",
      "event_code": "NOTIFICATION_RECEIVED_MOE",
      "event_uuid": "moengage-unique-event-id-1",
      "event_time": 1580967477,
      "event_type": "CAMPAIGN_EVENT",
      "event_source": "MOENGAGE",
      "push_id": "recipient-device-push-token-1",
      "uid": "{{createUser.response.body.data.fcid}}",
      "event_attributes": {
        "campaign_id": "353df897hkbh67661",
        "campaign_name": "Daily Reminder",
        "campaign_type": "Event Trigger",
        "campaign_channel": "Push"
      },
      "user_attributes": {
        "moengage_user_id": "moe_internal_user_id_123",
        "user_attr_1": "user_attr_val1",
        "user_attr_2": "user_attr_val2"
      },
      "device_attributes": {
        "moengage_device_id": "moe_internal_device_id_456",
        "device_attr_1": "device_attr_val1",
        "device_attr_2": "device_attr_val2"
      }
    },
    {
      "event_name": "Email Sent",
      "event_code": "MOE_EMAIL_SENT",
      "event_uuid": "moengage-unique-event-id-2",
      "event_time": 1580967478,
      "event_type": "CAMPAIGN_EVENT",
      "event_source": "MOENGAGE",
      "email_id": "<EMAIL>",
      "uid": "{{createJwt.request.body.idfv}}",
      "event_attributes": {
        "campaign_id": "353df897hkbh67662",
        "campaign_name": "Monthly Digest",
        "campaign_type": "General",
        "campaign_channel": "Email"
      },
      "user_attributes": {
        "moengage_user_id": "moe_internal_user_id_124",
        "user_attr_1": "user_attr_val3",
        "user_attr_2": "user_attr_val4"
      },
      "device_attributes": {
        "moengage_device_id": "moe_internal_device_id_457",
        "device_attr_1": "device_attr_val3",
        "device_attr_2": "device_attr_val4"
      }
    }
  ]
}

### MoEngage Webhook - Invalid User (Non-existent FCID)
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "app_name": "FlipaClip",
  "source": "MOENGAGE",
  "moe_request_id": "moengage-unique-request-id-invalid",
  "events": [
    {
      "event_name": "Notification Received Android",
      "event_code": "NOTIFICATION_RECEIVED_MOE",
      "event_uuid": "moengage-unique-event-id-invalid",
      "event_time": 1580967479,
      "event_type": "CAMPAIGN_EVENT",
      "event_source": "MOENGAGE",
      "push_id": "recipient-device-push-token",
      "uid": "non-existent-fcid-12345",
      "event_attributes": {
        "campaign_id": "353df897hkbh67663",
        "campaign_name": "Test Campaign",
        "campaign_type": "Event Trigger",
        "campaign_channel": "Push"
      },
      "user_attributes": {
        "moengage_user_id": "moe_internal_user_id_invalid",
        "user_attr_1": "user_attr_val1",
        "user_attr_2": "user_attr_val2"
      },
      "device_attributes": {
        "moengage_device_id": "moe_internal_device_id_invalid",
        "device_attr_1": "device_attr_val1",
        "device_attr_2": "device_attr_val2"
      }
    }
  ]
} 