### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}
### Create user with idfv from token, should create a new user
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### Identify Webhook Event Example
POST http://localhost:3000/webhooks
Authorization: Bearer {{createJwt.response.body.data.accessToken}}


{
  "provider": "FlipaClip",
  "event_name": "identify",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1718000000003
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "apple_app_store",
  "session_id": 1234,
  "payload": {
    "totalProjectsBackedUpCount": 1,
    "notificationChannels": ["tets", "test2"]
  }
}

### Expected Response
# (Status 201)
{
  "message": "Success",
  "statusCode": 201,
  "modified_properties": {
    "user_fcid_123": {
      "totalProjectsBackedUpCount": 1,
      "notificationChannels": ["a", "c"]

    }
  },
  "timestamp": "2024-06-10T12:00:00.000Z"
} 

### Birthdate Webhook Event Example (for users with FCAID)
POST http://localhost:3000/webhooks
Content-Type: application/json

{
  "provider": "FlipaClip",
  "event_name": "identify",
  "event_control": {
    "device_id": "e8b47f7e-f0f6-4027-9785-2339beed28ef",
    "timestamp": 1718000000010
  },
  "fcid": "9bd4d9c8-b900-47a9-ba5b-7d3ad6346dfe-48",
  "fcaid": "user-firebase-id-123",
  "store": "apple_app_store",
  "session_id": 1234,
  "payload": {
    "birthDate": "1995-06-15"
  }
}

### Expected Birthdate Response
# (Status 201)
{
  "message": "Success",
  "statusCode": 201,
  "modified_properties": {
    "9bd4d9c8-b900-47a9-ba5b-7d3ad6346dfe-48": {
      "birthDate": "1995-06-15",
      "userDeclaredAge": 28,
      "userCurrentAge": 29
    }
  },
  "timestamp": "2024-06-10T12:00:00.000Z"
}

### Project Backup Webhook Batch Request
POST {{baseUrl}}/webhooks/batch
Content-Type: application/json

{
  "events": [
   {
  "provider": "FlipaClip",
  "event_name": "identify",
  "event_control": {
    "device_id": "e8b47f7e-f0f6-4027-9785-2339beed28ef",
    "timestamp": 1718000000010
  },
  "fcid": "9bd4d9c8-b900-47a9-ba5b-7d3ad6346dfe-48",
  "store": "apple_app_store",
  "session_id": 1234,
  "payload": {
    "saveToDatabase": false,
    "totalProjectsBackedUpCount": 1,
    "notificationChannels": ["a", "c"]
  }
}, 
   {
  "provider": "FlipaClip",
  "event_name": "identify",
  "event_control": {
    "device_id": "e8b47f7e-f0f6-4027-9785-2339beed28ef",
    "timestamp": 1718000000011
  },
  "fcid": "9bd4d9c8-b900-47a9-ba5b-7d3ad6346dfe-48",
  "store": "apple_app_store",
  "session_id": 1234,
  "payload": {
    "saveToDatabase": false,
    "totalProjectsBackedUpCount": 1,
    "notificationChannels": ["a", "c"]
  }
}
  ]
} 

### Birthdate Batch Request Example
POST {{baseUrl}}/webhooks/batch
Content-Type: application/json

{
  "events": [
    {
      "provider": "FlipaClip",
      "event_name": "identify",
      "event_control": {
        "device_id": "e8b47f7e-f0f6-4027-9785-2339beed28ef",
        "timestamp": 1718000000020
      },
      "fcid": "9bd4d9c8-b900-47a9-ba5b-7d3ad6346dfe-48",
      "fcaid": "user-firebase-id-123",
      "store": "apple_app_store",
      "session_id": 1234,
      "payload": {
        "birthDate": "1990-03-20"
      }
    },
    {
      "provider": "FlipaClip",
      "event_name": "identify",
      "event_control": {
        "device_id": "e8b47f7e-f0f6-4027-9785-2339beed28ef",
        "timestamp": 1718000000021
      },
      "fcid": "9bd4d9c8-b900-47a9-ba5b-7d3ad6346dfe-48",
      "fcaid": "user-firebase-id-123",
      "store": "apple_app_store",
      "session_id": 1234,
      "payload": {
        "totalProjectsBackedUpCount": 5
      }
    }
  ]
} 