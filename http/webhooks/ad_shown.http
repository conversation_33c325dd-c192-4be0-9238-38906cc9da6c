### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}
### Create user with idfv from token, should create a new user
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### Ad Shown Webhook with FlipaClip as provider
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "provider": "FlipaClip",
  "event_name": "ad_shown",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1998972200042
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "apple_app_store",
  "session_id": 1750039094091,
  "payload": {
    "adUnitId": "DefaultRewardedVideo",
    "adType": "Rewarded",
    "loadTime": 3,
    "revenue": 0.0028,
    "publisherNetwork": "admanager",
    "triggerAction": "Add, Merge or Duplicate Layer",
    "isRewardGranted": true
  }
}

### Ad Shown Webhook with iron-source as provider
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "event_name": "ad_shown",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1998972200042
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "provider": "iron-source",
  "store": "google_play",
  "session_id": 1738772200000,
  "payload": {
    "revenue": 0.00025,
    "adType": "Rewarded",
    "abTestGroup": "A",
    "instanceName": "Bidding",
    "segmentName": "non_coppa",
    "loadTime": 1,
    "adUnitId": "DefaultRewardedVideo",
    "isRewardGranted": true,
    "publisherNetwork": "IronSource",
    "triggerAction": "user_initiated"
  }
}