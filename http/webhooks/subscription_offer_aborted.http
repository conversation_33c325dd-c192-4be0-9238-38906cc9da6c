### Paywall Webhook Test Request
POST {{baseUrl}}/webhooks/batch
Content-Type: application/json

{
  "events": [
    {
  "provider": "FlipaClip",
  "event_name": "subscription_offer_aborted",
  "event_control": {
    "device_id": "2BC41E11-772A-4B19-B557-B5F6632B145E",
    "timestamp": 1747749083521
  },
  "fcid": "f2875fb4-5b5f-488a-8f64-c1b994aed72b-40",
  "store": "google_play",
  "session_id": 1743622850028,
  "payload": {
    "paywall_id": "default_light_qa",
    "placement_id": "home_subscription_button",
    "trigger_action": "app_open",
    "plans": [
      {
        "id": "flipaclip_599_1m_7d0",
        "offers_free_trial": true,
        "period": "MONTH"
      },
      {
        "id": "flipaclip_2999_1y_7d0",
        "offers_free_trial": true,
        "period": "YEAR"
      }
    ],
    "offer_selected": "flipaclip_599_1m_7d0",
    "abort_reason": "Any reason"
  }
    }
  ]
}