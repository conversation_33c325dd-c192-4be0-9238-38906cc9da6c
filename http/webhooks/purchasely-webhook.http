### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token, should create a new user
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### Purchasely Webhook Test Request
POST {{baseUrl}}/webhooks
Content-Type: application/json
X-FC-SEC: {{$dotenv X_FC_SEC_SECRET}}

{
  "event_name": "SUBSCRIPTION_RECEIVED",
  "fcid": "{{createUser.response.body.data.fcid}}",
  "provider": "purchasely",
  "payload": {
    "plan": "monthly",
    "store": "GOOGLE_PLAY_STORE",
    "product": "PURCHASELY_PLUS",
    "user_id": "toto",
    "event_id": "5e45109f-7fac-45f8-a7e4-464892d5d35d",
    "event_name": "ACTIVATE",
    "offer_type": "NONE",
    "api_version": 3,
    "device_type": "PHONE",
    "environment": "SANDBOX",
    "purchased_at": "2023-12-12T14:13:11.777Z",
    "purchase_type": "RENEWING_SUBSCRIPTION",
    "store_country": "FR",
    "next_renewal_at": "2023-12-12T14:23:11.777Z",
    "purchased_at_ms": 1702390391777,
    "event_created_at": "2023-12-12T14:19:26.120Z",
    "is_family_shared": false,
    "store_product_id": "com.purchasely.plus.monthly",
    "customer_currency": "EUR",
    "plan_price_in_eur": 9.99,
    "next_renewal_at_ms": 1702390991777,
    "event_created_at_ms": 1702390766120,
    "previous_offer_type": "NONE",
    "store_app_bundle_id": "com.purchasely.demo",
    "subscription_status": "AUTO_RENEWING",
    "store_transaction_id": "GPA.3355-5688-7970-28037..5",
    "original_purchased_at": "2023-12-12T13:48:16.233Z",
    "original_purchased_at_ms": 1702388896233,
    "cumulated_revenues_in_eur": 69.9,
    "effective_next_renewal_at": "2023-12-12T14:23:11.777Z",
    "purchasely_subscription_id": "subs_D7GnVQbUxvY6YxoeK6nhyPDkmyCVcfe",
    "effective_next_renewal_at_ms": 1702390991777,
    "store_original_transaction_id": "GPA.3355-5688-7970-28037",
    "plan_price_in_customer_currency": 9.99,
    "amount_in_usd": 0.99
  }
}
