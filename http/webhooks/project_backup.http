### /users without FCID must include device_id from token in identifiers
# @name createJwt
@idfv= {{$guid}}
@idfa= {{$guid}}
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "idfv": "{{idfv}}"
}

### Create user with idfv from token, should create a new user
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "idfv": ["{{createJwt.request.body.idfv}}"]
  },
  "installed_at": 1738772200000
}

### Project Backup Webhook with FlipaClip as provider
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "provider": "FlipaClip",
  "event_name": "project_backed_up",
  "event_control": {
    "device_id": "{{createJwt.request.body.idfv}}",
    "timestamp": 1998972200042
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "apple_app_store",
  "session_id": 1234,
  "payload": {
    "canvasSize": "YouTube (720p)",
    "fps": 24,
    "backgroundType": "Preset",
    "totalFramesCount": 190,
    "projectType": "User",
    "projectId": "123",
    "templateId": "flipaquest",
    "isImportedProject": false,
    "triggerAction": "manual_backup"
  }
}

### Project Backup Webhook Batch Request
POST {{baseUrl}}/webhooks/batch
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "events": [
    {
      "provider": "FlipaClip",
      "event_name": "project_backed_up",
      "event_control": {
        "device_id": "{{createJwt.request.body.idfv}}",
        "timestamp": 1938972200018
      },
      "fcid": "{{createUser.response.body.data.fcid}}",
      "store": "apple_app_store",
      "session_id": 1234,
      "payload": {
        "canvasSize": "YouTube (720p)",
        "fps": 24,
        "backgroundType": "Preset",
        "totalFramesCount": 190,
        "projectType": "User",
        "projectId": "123",
        "templateId": "flipaquest",
        "isImportedProject": false,
        "triggerAction": "auto_backup"
      }
    },
    {
      "provider": "FlipaClip",
      "event_name": "project_backed_up",
      "event_control": {
        "device_id": "{{createJwt.request.body.idfv}}",
        "timestamp": 1938972200019
      },
      "fcid": "{{createUser.response.body.data.fcid}}",
      "store": "apple_app_store",
      "session_id": 1234,
      "payload": {
        "canvasSize": "Instagram (1080p)",
        "fps": 30,
        "backgroundType": "Color",
        "totalFramesCount": 240,
        "projectType": "Contest",
        "projectId": "456",
        "templateId": "contest_2024",
        "isImportedProject": true,
        "triggerAction": "before_export"
      }
    }
  ]
} 