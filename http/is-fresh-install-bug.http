### Create TOKEN from Anonymous User (no fcid) with Android Device ID
# @name createJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "e8383625e4d2dd7d"
}

### Use created JWT to create a user and retrieve it
# @name createUser
POST {{baseUrl}}/users
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "identifiers": {
    "adid": ["{{createJwt.request.body.adid}}"]
  },
  "installed_at": "2031-06-11T19:23:20.018Z",
  "isFreshInstall": true
}

### Ad Shown Webhook with <PERSON>lipaClip as provider
POST {{baseUrl}}/webhooks
Content-Type: application/json
Authorization: Bearer {{createJwt.response.body.data.accessToken}}

{
  "provider": "FlipaClip",
  "event_name": "ad_shown",
  "event_control": {
    "device_id": "{{createJwt.request.body.adid}}",
    "timestamp": 1938972200021
  },
  "fcid": "{{createUser.response.body.data.fcid}}",
  "store": "apple_app_store",
  "session_id": 1234,
  "payload": {
    "adUnitId": "DefaultRewardedVideo",
    "adType": "Rewarded",
    "loadTime": 3,
    "revenue": 0.0028,
    "publisherNetwork": "admanager",
    "triggerAction": "Add, Merge or Duplicate Layer",
    "isRewardGranted": true
  }
}

### Get new JWT with all the user data
# @name createAnotherJwt
POST {{baseUrl}}/auth/createJwt
x-api-key: {{$dotenv API_AUTH_TOKEN}}
Content-Type: application/json

{
  "adid": "e8383625e4d2dd7e"
}

### Reproduce bug with match directive update
POST {{baseUrl}}/users
Authorization: Bearer {{createAnotherJwt.response.body.data.accessToken}}

{
  "fcid": "{{createUser.response.body.data.fcid}}",
  "identifiers": {
    "adid": ["{{createAnotherJwt.request.body.adid}}"],
    "match": {
      "adid": "{{createJwt.request.body.adid}}"
    }
  },
  "properties": {
    "totalRevenue": 10,
    "totalAdRevenue": 10,
    "totalSubscriptionRevenue": 10,
    "moengageMigrated": true,
    "totalAdsShown": 10,
    "subscriptionType": "a",
    "subscriptionState": "b",
    "totalSubscriptionOffers": 10,
    "totalSubscriptionOffersAborted": 10,
    "daysAfterInstall": 1000,
    "reinstallCount": 10
  },
  "installed_at": "2031-06-11T19:23:20.018Z",
  "isFreshInstall": true
}
