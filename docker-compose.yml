services:
  app-test:
    image: roshi-app:latest
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        GH_AUTH_PACKAGE_TOKEN: ${GH_AUTH_PACKAGE_TOKEN}
      cache_from:
        - roshi-app:latest
        - node:20-alpine
    ports:
      - '3000:3000'
    depends_on:
      database-test:
        condition: service_healthy
      redis:
        condition: service_started
    environment:
      - API_AUTH_TOKEN=${API_AUTH_TOKEN}
      - ENABLE_SIGNOZ=false
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - JWT_SECRET=${JWT_SECRET}
      - NODE_ENV=local
      - POSTGRES_HOST=database-test
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - REDIS_HOST=redis
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_TLS=${REDIS_TLS}
      - ROSHI_URL=${ROSHI_URL}
      - SLACK_TOKEN=${SLACK_TOKEN}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 12
      start_period: 60s
    profiles:
      - 'test'

  test:
    image: roshi-test:latest
    build:
      context: .
      dockerfile: Dockerfile.test
      target: test
      args:
        GH_AUTH_PACKAGE_TOKEN: ${GH_AUTH_PACKAGE_TOKEN}
      cache_from:
        - roshi-test:latest
        - node:20-alpine
    depends_on:
      app-test:
        condition: service_healthy
      database-test:
        condition: service_healthy
      redis:
        condition: service_started
    environment:
      - API_AUTH_TOKEN=${API_AUTH_TOKEN}
      - ENABLE_SIGNOZ=false
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - JWT_SECRET=${JWT_SECRET}
      - NODE_ENV=test
      - POSTGRES_HOST=database-test
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - REDIS_HOST=redis
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_TLS=${REDIS_TLS}
      - ROSHI_URL=http://app-test:3000
      - SLACK_TOKEN=${SLACK_TOKEN}
      - TEST_ENV=${TEST_ENV}
    volumes:
      - ./test-results:/app/test-results
    profiles:
      - 'test'

  database-test:
    image: ghcr.io/dbsystel/postgresql-partman:16
    ports:
      - '${POSTGRES_PORT:-5432}:5432'
    environment:
      - POSTGRESQL_USER=${POSTGRES_USER}
      - POSTGRESQL_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRESQL_DB=${POSTGRES_DB}
      - POSTGRESQL_PORT=${POSTGRES_PORT}
      - ALLOW_EMPTY_PASSWORD=yes
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    profiles:
      - 'test'

  redis:
    image: redis:7-alpine
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ['CMD', 'redis-cli', '-a', '${REDIS_PASSWORD}', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    profiles:
      - 'test'

volumes:
  redis_data:
